## Differences between production and development codes

**File: workflow/urls.py**

1. line 26: title="Workflow API (Kubix Production)", / title="Workflow API (Kubix Dev)",
2. line 33: url='https://pisa-workflow.prdin.kubix.tm/', / url='https://pisa-workflow.devin.kubix.tm/',

**File: api/modules/Bod.py**

1. line 92: url = 'https://pisa-juniper.prdin.kubix.tm/juniper/pe/v1/' / url = 'https://pisa-juniper.devin.kubix.tm/juniper/pe/v1/'
2. line 493: response = requests.get('https://cgate.tm.com.my/cms/api/pisa/createRFC.php', params=payload, verify=False) / response = requests.get('http://*************/cms/api/pisa/createRFC.php', params=payload, verify=False)
3. line 605: url = 'https://pisa-juniper.prdin.kubix.tm/juniper/ce/v1/' / url = 'https://pisa-juniper.devin.kubix.tm/juniper/ce/v1/'
4. line 877: url = 'https://pisa-cisco.prdin.kubix.tm/cisco/ce/v1/' / url = 'https://pisa-cisco.devin.kubix.tm/cisco/ce/v1/'

**File: api/libs.py**

line 25: url = 'http://***********:7281/prj_HsbbEai_Async_CRM_Inbound_War/httpMessageReceiver.do' / 'http://************:8001/prj_HsbbEai_Async_CRM_Inbound_War/httpMessageReceiver.do'

**File: api/modules/IPME_EAI_Bod.py**

1. line 15: #authentication_classes = () / authentication_classes = ()
2. line 16: #permission_classes = () / permission_classes = ()
3. line 121: url = 'https://pisa-juniper.prdin.kubix.tm/juniper/pe/v1/'	/ url = 'https://pisa-juniper.devin.kubix.tm/juniper/pe/v1'
4. line 488: response = requests.post('https://pisa-juniper.prdin.kubix.tm/juniper/pe/v2/ipme/generate_config_modify_bw/',data=payload, verify=False) / response = requests.post('https://pisa-juniper.devin.kubix.tm/juniper/pe/v2/ipme/generate_config_modify_bw/', data=payload, verify=False)
5. line 653: response = requests.get('https://cgate.tm.com.my/cms/api/pisa/createRFC.php', params=payload, verify=False, timeout=10) / response = requests.get('http://*************/cms/api/pisa/createRFC.php',params=payload, verify=False, timeout=5)
6. line 808: url = 'https://pisa-juniper.prdin.kubix.tm/juniper/ce/v1/' / url = 'https://pisa-juniper.devin.kubix.tm/juniper/ce/v1'
7. line 1370: url = 'https://pisa-cisco.prdin.kubix.tm/cisco/ce/v1/' / url = 'https://pisa-cisco.devin.kubix.tm/cisco/ce/v1/'







