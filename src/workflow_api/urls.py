"""workflow_api URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path
# for swagger
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from api import databases, views
from api.modules import Ldap, Ldapv2

schema_view = get_schema_view(
   openapi.Info(
      title="PISA - Workflow Prod (Dedicated)",
      default_version="v1.0",
      description="Document for PISA Workflow API endpoints for Regression",
      terms_of_service="https://www.google.com/policies/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="BSD License"),
   ),
   url='https://pisa.tm.com.my/workflow/',
   public=True,
   permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path('workflow/admin/', admin.site.urls),
    path('workflow/', include('api.urls')),
    path('workflow/ipme', include('api.urls_ipme')),
    path('workflow/file/', include('api.file_urls')),
    path('workflow/data/', include('api.urls_data')),
    path('workflow/database/v1/get_log_data', databases.GetLogData.as_view(), name='get_log_data'),
    path('workflow/database/v2/get_log_data', databases.GetLogDataLatest.as_view(), name='get_log_data_v2'),
    # path('workflow/database/v3/get_log_data', views.AuditTrailApi.as_view(), name='get_log_data_v3'), 
    path('workflow/database/v1/network_element/', databases.NetworkElementApi.as_view(), name='get_network_element'),
    path('workflow/database/v1/network_element/<str:pk>', databases.NetworkElementDetailApi.as_view(), name='get_network_element_detail'),
    path('workflow/database/v1/network_element/bulk/', databases.NetworkElementBulkDetailApi.as_view(), name='get_network_element_bulk_detail'),
    path('workflow/swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('workflow/redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('workflow/user_5g_bot/', databases.User5GBotListCreateAPIView.as_view(), name='user5gbot-list-create'),
    path('workflow/user_5g_bot/<str:chat_id>/', databases.User5GBotRetrieveUpdateDestroyAPIView.as_view(), name='user5gbot-retrieve-update-destroy'),
    path('workflow/log/', databases.LogList.as_view(), name='log_list'),
    # OGG Database
    path('workflow/database/v1/ogg/clean_pipe/query_activities_details', databases.OggQueryActivitiesDetails.as_view(), name='clean_pipe_query_activities_details'),
    path('workflow/database/v1/ogg/clean_pipe/query_customer_details', databases.OggQueryCustomerDetails.as_view(), name='clean_pipe_query_customer_details'),
    path('workflow/database/v1/ogg/clean_pipe/query_line_attributes_details', databases.OggQueryLineAttributeDetails.as_view(), name='clean_pipe_query_line_attributes_details'),
    path('workflow/database/v1/ogg/clean_pipe/query_line_items_details', databases.OggQueryLineItemsDetails.as_view(), name='clean_pipe_query_line_items_details'),
    path('workflow/database/v1/ogg/clean_pipe/query_per_activity_id', databases.OggQueryPerActivityId.as_view(), name='clean_pipe_query_per_activity_id'),
    path('workflow/database/v1/ogg/clean_pipe/query_all_activities', databases.OggQueryAllActivities.as_view(), name='clean_pipe_query_all_activities'),
    path('workflow/database/v1/ogg/get_customer_name', databases.OGGRetrieveCustomerName.as_view(), name='get_customer_name'),
    path('workflow/database/v1/ogg/processing_order_inprogress_activity', databases.OGGRetrieveAllActivityInProgress.as_view(), name='processing_order_inprogress_activity'),
    path('workflow/database/v1/ogg/ponr_order_inprogress_activity', databases.OGGRetrieveAllActivityPONR.as_view(), name='ponr_order_inprogress_activity'),
    # CSR
    path('workflow/database/v1/csr/', databases.CsrInfoApi.as_view(), name='csr'),
    path('workflow/database/v1/csr/<str:csr>', databases.CsrInfoDetailApi.as_view(), name='csr_detail'),
    # LDAP
    path('workflow/ldap/v1/authenticate', Ldap.Authenticate.as_view(), name='ldap_authenticate'),
    path('workflow/ldap/v2/authenticate', Ldapv2.Authenticate.as_view(), name='ldap_authenticate_v2')
]
