"""
Django settings for workflow_api project.

Generated by 'django-admin startproject' using Django 2.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.2/ref/settings/
"""

import os

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'd^x^-^luf9+2v3-3&rax2@m+tryw8gx02e3i+9t+b^pj8@#bm3'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    # Django Apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 3rd party apps
    'rest_framework',
    'rest_framework.authtoken',
    'drf_yasg',     # for swagger
    # local apps
    'api.apps.ApiConfig',
    'sslserver',
    'corsheaders',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'corsheaders.middleware.CorsMiddleware',
]

ROOT_URLCONF = 'workflow_api.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        }, 
    },
]

WSGI_APPLICATION = 'workflow_api.wsgi.application'

# For authentication

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    # 'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema'
}

# Swagger Settings
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'api_key': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization'
        }
    },
    'USE_SESSION_AUTH': False,
}

# Database
# https://docs.djangoproject.com/en/2.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'workflow_api',
        'USER': 'ipgen_app',
        'PASSWORD': 'Pswd2019',
        # 'HOST': 'mysql-dev'
        'HOST': 'db-haproxy.pisa',
        'CONN_MAX_AGE': 0
    },
    'oracle': {
        'ENGINE': 'django.db.backends.oracle',
        'NAME': 'NCMSPRD',
        'USER': 'ESFRPA',
        'PASSWORD': 'Pasword1',
        'HOST': 'nprcmsodb-scn', # leave blank if connecting locally a
        'PORT': '1521', # leave blank if using default port
        'OPTIONS': {
            'threaded': True, # recommended for Oracle
            'use_returning_into': False
        },
    },
    'UNIS': {
        'ENGINE': 'django.db.backends.oracle',
        'NAME': 'HGRNPRD',
        'USER': 'GRNMigration',
        'PASSWORD': 'Gr@nm1g!',
        'HOST': 'grndbprod', # leave blank if connecting locally a
        'PORT': '1521', # leave blank if using default port
        'OPTIONS': {
            'threaded': True, # recommended for Oracle
            'use_returning_into': False
        },
    },
    'TMEDWH': {
        'ENGINE': 'django.db.backends.oracle',
        'NAME': 'dwh',
        'USER': 'surf_user',
        'PASSWORD': 'surfsys1',
        'HOST': 'ex01-scan.tm.com.my', # leave blank if connecting locally
        'PORT': '1521', # leave blank if using default port
        'OPTIONS': {
            'threaded': True, # recommended for Oracle
            'use_returning_into': False
        },
    }
}

# DATABASES = {
#    'default': {
#        'ENGINE': 'django.db.backends.sqlite3',
#        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
#    }
# }


# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Kuala_Lumpur'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

STATIC_DIR = os.path.join(BASE_DIR, 'workflow')
STATIC_URL = '/workflow/static/'
STATICFILES_DIRS = [STATIC_DIR,]


# Setup support for proxy headers
# USE_X_FORWARDED_HOST = True
# SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

CORS_ALLOWED_ORIGINS = [
    'http://ipgen.intra.tm',
]