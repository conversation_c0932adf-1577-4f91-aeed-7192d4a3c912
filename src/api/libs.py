from api.serializers import AuditTrailSerializer
from api.models import AuditTrail
import requests, json
from django.db import close_old_connections

def log_text(filename, content, ne_output=''):

	#file = 'root/home/<USER>/activation_log/'+filename
	file = '/root/home/<USER>/'+filename
	myfile = open(file, 'a')
	myfile.write('>> '+content+'.\n\n')

	if ne_output != '':
		myfile.write(ne_output+'\n\n\n')

	myfile.close()

def log_database(data_DB):

	# serialized_data = AuditTrailSerializer(data=data_DB)
	# serialized_data.is_valid(raise_exception=True)
	# serialized_data.save()

	AuditTrail.objects.create(**data_DB)
	close_old_connections()



def eai_response(payload):

	try:
		url = 'http://10.41.86.25:7281/prj_HsbbEai_Async_CRM_Inbound_War/httpMessageReceiver.do'
		response = requests.post(url, data=json.dumps(payload))
		if response.status_code == 200 and not 'Exception' in response.text:
			file = '/root/home/<USER>/{}'.format(payload['PISAResponse']['BusinessEvent']+'@'+payload['PISAResponse']['CorrelationID']+'.txt')
			myfile = open(file, 'a')
			myfile.write('Status Code: {} \n\nResponse: {}'.format(response.status_code,response.text))
			return 'success'
		else:
			file = '/root/home/<USER>/{}'.format(payload['PISAResponse']['BusinessEvent']+'@'+payload['PISAResponse']['CorrelationID']+'.txt')
			myfile = open(file, 'a')
			myfile.write('Status Code: {} \n\nResponse: {}'.format(response.status_code,response.text))
			return 'failed'
	except Exception as error:
		file = '/root/home/<USER>/{}'.format(payload['PISAResponse']['BusinessEvent']+'@'+payload['PISAResponse']['CorrelationID']+'.txt')
		myfile = open(file, 'a')
		myfile.write('Response: {}'.format(error))
		return 'failed'
