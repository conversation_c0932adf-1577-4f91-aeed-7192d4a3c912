from django.core import validators
from rest_framework import serializers
from django.core.validators import validate_ipv4_address
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token
import re


# Global validation for bandwidth
def validate_bandwidth(bw):
    # bandwidth must start with numbers ^[1-9], can contain numbers 0-9 in the middle [0-9]* and end with m/M/k/K/g/G [mMkKgG]$
    if not re.search('(^[1-9][0-9]*[mMkKgG]$)', bw):
        raise serializers.ValidationError('Invalid bandwidth format')

# Global validation for interface
def validate_interface(interface):
    # ensure there are total of 1 X '-', 2 X '/' and 1 X '.'
    if not len(re.findall('[-/.]', interface)) == 4:
        raise serializers.ValidationError('Invalid interface format')

# Global validation for interface
def validate_interface_me(interface):
    # ensure there are total of 1 X '-', 2 X '/' and 1 X '.'
    if not len(re.findall('[.]', interface)) == 1:
        raise serializers.ValidationError('Invalid interface format')



# IPME
class IPMENewInstall(serializers.Serializer):
    ne_ip = serializers.CharField(validators=[validate_ipv4_address])
    username = serializers.CharField()
    password = serializers.CharField()
    pe_node = serializers.CharField()
    service_id = serializers.CharField()
    interface = serializers.CharField()
    interface_desc = serializers.CharField()
    bandwidth = serializers.CharField()
    pe_wan_ipv4 = serializers.CharField()
    ce_wan_ipv4 = serializers.CharField()
    cos_package = serializers.ChoiceField(choices=['CLASSIC', 'ESSENTIAL', 'PERFORMANCE', 'CONVERGENCE', 'VERSATILE', 'CUSTOMIZED', 'CUSTOMISED'])
    topology = serializers.ChoiceField(choices=['FULL MESH', 'PARTIAL HUB AND SPOKE'])
    site_role = serializers.ChoiceField(choices=['HUB', 'HUB MESH', 'SPOKE'])
    extranet = serializers.JSONField()
    vpn = serializers.CharField()
    hrt = serializers.CharField()
    srt = serializers.CharField()
    route_distinguisher = serializers.JSONField(help_text=['hub', 'hub_upstream', 'hub_downstream', 'spoke'])
    first_circuit = serializers.BooleanField(default=True)
    ip_version = serializers.ChoiceField(choices=['IPV4', 'DUAL STACK'])
    circuit_type = serializers.ChoiceField(choices=['ACTIVE', 'PASSIVE', 'SINGLE'])
    ce_as = serializers.CharField()
    bgpneighborv4_import_map = serializers.JSONField()
    bgpneighborv6_import_map = serializers.JSONField(required=False, default=None)
    pe_wan_ipv6 = serializers.CharField(required=False, default=None)
    ce_wan_ipv6 = serializers.CharField(required=False, default=None)
    sg_percentage = serializers.CharField(required=False, default=None)
    ipp = serializers.CharField(required=False, default=None)

    # Validate Bandwidth
    def validate_bandwidth(self, value):
        if re.compile('[mMkKgG]').search(value) != None and re.compile('[0-9]').search(value) != None:
            if re.compile('[@_!#$%^&*()<>?/\|}{~:.,]').search(value) != None:
                raise serializers.ValidationError("Invalid bandwidth format")
            else:
                return value
        else:
            raise serializers.ValidationError("Invalid bandwidth format")

    # Validate Interface
    def validate_interface(self, value):
        if '.' not in value:
            raise serializers.ValidationError("Invalid interface format - missing vlan")
        else:
            return value

    # Validate CoS Package
    # def validate_cos_package(self, value):
    #     if 'CUSTOMISED' in value:
    #         raise serializers.ValidationError("COS Configuration for package (" + value + ") is not supported")
    #     else:
    #         return value

    # Validate Extranet
    def validate_extranet(self, value):
        if len(value) == 0:
            raise serializers.ValidationError('This field may not be blank.')
        else:
            return value

    # Validate Route Distinguisher
    def validate_route_distinguisher(self, value):
        if len(value) == 0:
            raise serializers.ValidationError('This field may not be blank.')
        else:
            return value

    # Validate IP Format IPV4
    def validate_pe_wan_ipv4(self, value):
        if '/' in value:
            try:
                validate_ipv4_address(value.split('/')[0])
                return value
            except:
                raise serializers.ValidationError('Invalid IPv4 format')
        else:
            raise serializers.ValidationError('Missing subnet mask')

    def validate_ce_wan_ipv4(self, value):
        if '/' in value:
            try:
                validate_ipv4_address(value.split('/')[0])
                return value
            except:
                raise serializers.ValidationError('Invalid IPv4 format')
        else:
            raise serializers.ValidationError('Missing subnet mask')


    # Validate Topology and Site Role
    # def validate(self, data):
    #     if data['topology'] == 'FULL MESH' and data['site_role'] == 'SPOKE':
    #         raise serializers.ValidationError("Invalid site role for topology Full Mesh")
    #     return data

    # remove NULL parameter
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Remove keys with None values
        return {key: value for key, value in data.items() if value is not None}


class IPMETerminate(serializers.Serializer):
    ne_ip = serializers.CharField(validators=[validate_ipv4_address])
    username = serializers.CharField()
    password = serializers.CharField()
    vpn = serializers.CharField()
    topology = serializers.ChoiceField(choices=['FULL MESH', 'PARTIAL HUB AND SPOKE'])
    site_role = serializers.ChoiceField(choices=['HUB', 'SPOKE'])
    delete_policy_hub = serializers.BooleanField(default=True)
    delete_policy_spoke = serializers.BooleanField(default=True)
    delete_community = serializers.BooleanField(default=True)
    last_circuit = serializers.BooleanField(default=False)
    ip_version = serializers.ChoiceField(choices=['IPV4', 'DUAL STACK'])
    interface = serializers.CharField()
    ce_wan_ipv4 = serializers.CharField()
    ce_wan_ipv6 = serializers.CharField(required=False, default=None, allow_blank=True)
    delete_soo = serializers.BooleanField(default=False)
    site_abr = serializers.CharField(required=False, default=None, allow_blank=True)
    cos_package = serializers.ChoiceField(choices=['CLASSIC', 'CONVERGENCE', 'PERFORMANCE','VERSATILE','ESSENTIAL','CUSTOMIZED', 'CUSTOMISED'])
    firewall_name = serializers.CharField(required=False, default=None, allow_blank=True) 
    scheduler_map = serializers.CharField(required=False, default=None, allow_blank=True) 
    scheduler_name = serializers.JSONField(required=False, default=None)
    delete_group_ipv6 = serializers.BooleanField(default=False)
    delete_rib_ipv6 = serializers.BooleanField(default=False)

    #[1] Validate VPN
    def validate_vpn(self, value):
        return value.upper()

    #[2] Validate Interface
    def validate_interface(self, value):
        if '.' not in value:
            raise serializers.ValidationError("Invalid interface format - missing vlan")
        else:
            return value

    #[3] Validate CE WAN IPv4
    def validate_ce_wan_ipv4(self, value):
        if '/' in value:
            try:
                validate_ipv4_address(value.split('/')[0])
                return value
            except:
                raise serializers.ValidationError('Invalid IPv4 format')
        else:
            raise serializers.ValidationError('Missing subnet mask')

    #[4] Validate Global
    def validate(self, data):
        if data['topology'] == 'FULL MESH' and data['site_role'] == 'SPOKE':
            raise serializers.ValidationError('Invalid Topology (Full Mesh) with Site Role (Spoke)')
        elif data['last_circuit'] == False and data['delete_policy_hub'] == True:
            raise serializers.ValidationError('Invalid Last Circuit (False) with Delete Policy Hub (True)')
        elif data['last_circuit'] == False and data['delete_policy_spoke'] == True:
            raise serializers.ValidationError('Invalid Last Circuit (False) with Delete Policy Spoke (True)')
        elif data['last_circuit'] == False and data['delete_community'] == True:
            raise serializers.ValidationError('Invalid Last Circuit (False) with Delete Community (True)')
        # elif data['delete_soo'] == True and data['site_role'] == 'SPOKE':
        #     raise serializers.ValidationError('Invalid Delete SOO (True) with Site Role (Spoke)')
        else:
            return data
        
    # remove NULL parameter
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Remove keys with None values
        return {key: value for key, value in data.items() if value is not None}
   


