# Generated by Django 2.2.5 on 2020-03-23 08:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AuditTrail',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_type', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ('service_type', models.<PERSON>r<PERSON><PERSON>(max_length=10)),
                ('input_details', models.Char<PERSON>ield(max_length=5000)),
                ('message', models.Char<PERSON>ield(max_length=5000)),
                ('api_output', models.Char<PERSON>ield(max_length=5000)),
                ('status', models.Char<PERSON><PERSON>(max_length=10)),
                ('updated', models.DateTimeField(auto_now_add=True)),
                ('filename', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=100)),
                ('user', models.<PERSON><PERSON><PERSON><PERSON>(max_length=20)),
            ],
        ),
    ]
