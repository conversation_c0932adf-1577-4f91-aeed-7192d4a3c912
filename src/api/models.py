from django.db import models
import uuid

# Create your models here.
class AuditTrail(models.Model):
    order_type = models.Char<PERSON>ield(max_length=50)
    service_type = models.Char<PERSON>ield(max_length=50)
    # input_details = models.Char<PERSON>ield(max_length=5000)
    input_details = models.JSO<PERSON>ield()
    message = models.CharField(max_length=5000)
    api_output = models.CharField(max_length=5000)
    status = models.Char<PERSON>ield(max_length=10)
    updated = models.DateTimeField(auto_now_add=True)
    log_file = models.CharField(max_length=100, blank=True)
    config_file = models.CharField(max_length=1000, blank=True)
    user = models.CharField(max_length=20)

class NetworkElement(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hostname = models.Char<PERSON>ield(max_length=30, unique=True)
    neType = models.CharField(max_length=30, null=True, blank=True)
    loopbackIp = models.Char<PERSON><PERSON>(max_length=30)
    vendor = models.CharField(max_length=30, null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "network_elements"
        # ordering = ['-createdAt']

        def __str__(self) -> str:
            return self.title

class User5GBot(models.Model):
    chat_id = models.CharField(primary_key=True, max_length=255)
    staff_id = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    division = models.CharField(max_length=255)
    is_approved = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "user_5g_bot"

        def __str__(self) -> str:
            return self.chat_id
        

class CsrInfo(models.Model):
    # id = models.IntegerField(primary_key=True)
    csr = models.CharField(max_length=100, unique=True)
    ip = models.GenericIPAddressField(protocol='both', unique=True)
    port_primary = models.CharField(max_length=30, null=True, blank=True)
    port_secondary = models.CharField(max_length=30, null=True, blank=True)
    device_type = models.CharField(max_length=100)
    location = models.CharField(max_length=1000)
    region = models.CharField(max_length=100)
    vendor = models.CharField(max_length=30)
    agg = models.CharField(max_length=30, null=True, blank=True)
    agg_port_primary = models.CharField(max_length=30, null=True, blank=True)
    agg_port_secondary = models.CharField(max_length=30, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "csr_info"

        def __str__(self) -> str:
            return self.ip

