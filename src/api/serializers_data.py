from django.core import validators
from rest_framework import serializers
# from django.core.validators import validate_ipv4_address
# import re
# import ipaddress

# Global validation for bandwidth
# def validate_bandwidth(bw):
#     # bandwidth must start with numbers ^[1-9], can contain numbers 0-9 in the middle [0-9]* and end with m/M/k/K/g/G [mMkKgG]$
#     if not re.search('(^[1-9][0-9]*[mMkKgG]$)', bw):
#         raise serializers.ValidationError('Invalid bandwidth format')

class UnisGetL3CircuitSerializer(serializers.Serializer):
    cust_abbr = serializers.ListField(child=serializers.CharField(), required=False)

class UnisGetUpeNidSerializer(serializers.Serializer):
    nid_upe_name = serializers.ListField(child=serializers.CharField(), required=True)

