from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import BodSerializer
import requests, json, time, re, os, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps

class API(APIView):

	#authentication_classes = ()     #exclude from global authentication
	#permission_classes = ()

	@swagger_auto_schema(
		request_body=BodSerializer,
		responses={201: BodSerializer()},
	)

	def post(self, request):

		try:

			# GET USER ID
			token = request.META.get('HTTP_AUTHORIZATION')
			if token:
				user_id = Token.objects.get(key=token.replace('Token ','')).user_id
				user = User.objects.get(id=user_id).username
			else:
				user = 'not_available'

			#                                           #
			#              VERIFY API INPUT             #
			#                                           #
			input_data = BodSerializer(data=request.data)
			input_data.is_valid()

			# initiate variable
			api_response = dict()

			if len(input_data.errors) > 0:  # check if passed data contains error and return error
				output_message = 'Incomplete/invalid information fetched by API'
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':request.data,
					'message':output_message,
					'api_output':str(input_data.errors),
					'status':'failed',
					'log_file':'N/A',
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = input_data.errors
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			# serialized_data = input_data.data[]   # assign a variable with serialized data
			serialized_data = input_data.validated_data
			date_now = datetime.now().strftime('%d-%m-%Y')
			time_now = datetime.now().strftime('%H:%M:%S')
			log_file = 'BOD_{}_{}_{}@{}_by_{}.txt'.format(serialized_data['service_id'], serialized_data['node_name'], date_now, time_now, user)		# Create Log File


			# LOG CREATED TIME #
			output_message = "File created at {} {}".format(date_now, time_now) 
			log_text(filename=log_file, content=output_message)

			output_message = 'Information fetched by API contains no error'
			log_text(filename=log_file, content=output_message, ne_output=str(request.data))

			#                                                #
			#              CHECK SERVICE TYPE                #
			#                                                #
			if not 'IPVPN Service (Metro Ethernet)' in serialized_data['product_name']:
				output_message = 'Invalid service type ({}). Only product IPVPN Service (Metro Ethernet) is allowed'.format(serialized_data['product_name'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':output_message,
					'api_output':output_message,
					'status':'failed',
					'log_file':log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = output_message
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                                #
			#        CHECK PE IP AND INTERFACE MATCH         #
			#                                                #

			url = 'https://pisa-juniper.prdin.kubix.tm/juniper/pe/v1/'	# declare main url variable

			payload = {
				'ne_ip': serialized_data['device_id'],
				'interface': serialized_data['interface'],
				'pe_ip': serialized_data['pe_wan_ip']
			}
			response = requests.get(url+'check_interface_match_ip/', params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while validating PE IP Address ({}) and Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				if output['output'] == 'Interface is not match with IP Address':
					error_message = 'Mismatched PE IP Address ({}) and Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'PE IP Address ({}) match with Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

			#                                                #
			#        CHECK PE INT DESCRIPTION (EXTENSION)    #
			#                                                #

			payload = {
				'ne_ip': serialized_data['device_id'],
				'interface': serialized_data['interface']
			}
			response = requests.get(url+'get_interface_description/', params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while retrieving interface description for Interface ({})'.format(serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				if re.search('extension', output['output'], re.IGNORECASE):
					output_message = 'Change of bandwidth is not required due to BOD extension'
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file':log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                                #
			#                 CHECK QOS PE                   #
			#                                                #
			payload = {
				'ne_ip': serialized_data['device_id'],
				'interface': serialized_data['interface'],
			}

			response = requests.get(url+'get_qos/',params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while retrieving QOS Package from PE Interface ({})'.format(serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				if output['output'] == 'No QOS package is found':
					error_message = 'No QOS package returned from PE Interface ({})'.format(serialized_data['interface'])
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					serialized_data['pe_qos'] = output['output']
					output_message = 'QOS Package ({}) has been successfully retrieved from PE'.format(serialized_data['pe_qos'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

			#                                                #
			#               CHECK BGP Status                 #
			#                                                #
			if re.findall('bgp', serialized_data['protocol'], re.IGNORECASE):	# check if routing-protocol = bgp/BGP

				payload = {
					'ne_ip': serialized_data['device_id'],
					'vpn': serialized_data['vpn_name'],
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'get_bgp_status/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while retrieving BGP status from PE'
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed','log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] != 'Established':   # != Performance Test
					# if output['output'] != 'Idle':
						if output['output'] == 'Invalid BGP status':
							output_message = 'Invalid BGP status for CE IP Address ({}) returned from PE'.format(serialized_data['ce_wan_ip'])
						else:	
							output_message = 'BGP status for CE IP Address ({}) returned from PE ({}) is not established'.format(serialized_data['ce_wan_ip'], output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': 'N/A',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						output_message = 'BGP status for CE IP Address ({}) returned from PE is ({})'.format(serialized_data['ce_wan_ip'], output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

			#                                                #
			#                   CHECK CPU                    #
			#                                                #
			match, count, ne_output, master_util_success, master_util_failed = False, int(), str(), dict(), dict()
			while not match:
				# if failed check 5 times
				if count > 4:
					break

				# create 5s delay to re-chek CPU
				if count > 0:
					time.sleep(10)

				payload = {
					'ne_ip': serialized_data['device_id'],
				}

				response = requests.get(url+'check_cpu/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while retrieving CPU utilization info from PE'
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_keys = output['output'].keys()
					for key in output_keys:
						if re.findall('main', key, re.IGNORECASE):
							if int(output['output'][key]['memory_utilization']) < 70 and int(output['output'][key]['idle']) > 10:
								match = True
								master_util_success[key] = {
									'memory_utilization':output['output'][key]['memory_utilization'],
									'idle':output['output'][key]['idle']
								}
							else:
								match = False
								master_util_failed[key] = {
									'memory_utilization':output['output'][key]['memory_utilization'],
									'idle':output['output'][key]['idle']
								}

					# increase counter if does not match threshold
					if not match:
						count += 1
					ne_output += output['ne_response']		# accumulate ne output in a var

			if not match:
				output_message = str()
				for key in master_util_failed:
					if output_message == '':
						output_message = "CPU utilization is outside threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
					else:
						output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
				log_text(filename=log_file, content=output_message, ne_output=ne_output)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':output_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else :
				output_message = str()
				for key in master_util_success:
					if output_message == '':
						output_message = "CPU utilization is within threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(master_util_success[key]['memory_utilization']) + ' percent' , str(master_util_success[key]['idle']) + ' percent')
					else:
						output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(master_util_success[key]['memory_utilization'])+' percent', str(master_util_success[key]['idle'])+' percent')
				log_text(filename=log_file, content=output_message, ne_output=ne_output)

			#                                                #
			#          GENERATE PE BOD CONFIGURATION         #
			#                                                #
			config_file = 'PE_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['node_name'],  date_now, time_now, user)		#create config file
			interface_split = serialized_data['interface'].split('.')
			port = interface_split[0]
			vlan = interface_split[1]
			# parameters = [port, vlan, serialized_data['new_bandwidth'].lower(), serialized_data['vpn_name'].upper(), serialized_data['service_id']]

			payload = {
				'filename': config_file,
				'port': port,
				'vlan': vlan,
				'vpn': serialized_data['vpn_name'].upper(),
				'service_id': serialized_data['service_id'],
				'bandwidth': serialized_data['new_bandwidth'].lower(),
			}

			response = requests.post('https://pisa-juniper.prdin.kubix.tm/juniper/pe/v2/ipme/generate_config_modify_bw/', data=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while generating PE configurations'
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': config_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				output_message = output['output']
				log_text(filename=log_file, content=output_message)

			#                                   #
			#        CHECK SHAPING-RATE         #
			#                                   #

			payload = {
				'ne_ip': serialized_data['device_id'],
				'interface': serialized_data['interface']
			}
			output = requests.get(url+'get_qos_interface_shaping_rate/', params=payload, verify=False).json()
			if output['status'] == 'failed':
				error_message = 'An error has occured while retrieving QOS interface shaping-rate for Interface ({})'.format(serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': config_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				if output['output'] == 'No shaping-rate is found':
					shaping_rate = str()
				else:
					shaping_rate = output['output']
				output_message = 'Shaping-rate returned from PE is ({})'.format(output['output'])
				log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

			#                                   #
			#        COMPARE EXISTING BW        #
			#                                   #

			if re.match(shaping_rate, serialized_data['old_bandwidth'], re.IGNORECASE):

				#                                                #
				#                 PUSH PE CONFIG                 #
				#                                                #
				payload = {
					'ne_ip': serialized_data['device_id'],
					'filename': config_file
				}

				response = requests.post(url+'load_config_modify/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while loading the generated configurations to PE'
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] != 'Successfully loaded configurations at PE':
						error_message = 'Configurations loading process is unsuccessful'
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])

					#                                                #
					#              CREATE RFC in CGATE               #
					#                                                #

					description = 'Service ID: {}, PE loopback IP : {}, PE interface : {}, New bandwidth : {}, Old bandwidth : {}, VPN Name: {}'.format(serialized_data['service_id'], serialized_data['device_id'], serialized_data['interface'], serialized_data['new_bandwidth'], serialized_data['old_bandwidth'], serialized_data['vpn_name'])
					file_config = open('/root/home/<USER>/{}.txt'.format(config_file), 'r')
					workplan = file_config.read()
					datetime_provi = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

					payload = {
						'rfc_status': 'close',
						'title': 'MODIFY BANDWIDTH - {} ({})'.format(serialized_data['service_id'], serialized_data['vpn_name']),
						'description': description,
						'impact': 'Low',
						'work_plan': workplan,
						'fall_back': 'Rollback',
						'reason': 'Provisioning',
						'start': datetime_provi,
						'end': datetime_provi,
						'hostname': serialized_data['node_name'],
						'region': serialized_data['pe_region'],
						'actual_start': datetime_provi,
						'actual_end': datetime_provi,
						'task_completed': 'Successful',
						'inventory_update': 'No'
					}

					try:
						response = requests.get('https://cgate.tm.com.my/cms/api/pisa/createRFC.php',params=payload, verify=False, timeout=40)
						cgate_output = response.json()

						cgate_error_message = str()

						if cgate_output[0]['HEADER']['CREATE_STATUS']=='FAIL':
							cgate_error_message = 'Failed to create RFC in Cgate'
							output_message = cgate_error_message + '\n[error message: {}]'.format(cgate_output[0]['HEADER']['ERROR_MSG'])
							log_text(filename=log_file, content=output_message)
						else:
							cgate_success_message = 'Successfully created RFC in Cgate'
							log_text(filename=log_file, content=cgate_success_message)

					except Exception as error:
						cgate_error_message = 'Failed to create RFC in Cgate'
						output_message = cgate_error_message + '\n[error message: {}]'.format(str(error))
						log_text(filename=log_file, content=output_message)

			elif re.match(shaping_rate, serialized_data['new_bandwidth'], re.IGNORECASE):
				output_message = 'Existing bandwidth ({}) match new bandwidth ({}). No change is required at PE'.format(shaping_rate, serialized_data['new_bandwidth'])
				log_text(filename=log_file, content=output_message)
				# Initialize variable due to no RFC created
				cgate_error_message = "RFC application is not required"

			else:
				output_message = 'Existing bandwidth ({}) is not match old bandwidth ({}) and new bandwidth ({})'.format(shaping_rate, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
				# if cgate_error_message == 'Failed to create RFC in Cgate':
				# 	output_message = '{} .{}'.format(output_message, cgate_error_message)
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'BOD',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':output_message,
					'api_output':'Existing bandwidth ({})'.format(shaping_rate),
					'status':'failed',
					'log_file':log_file,
					'config_file': config_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = 'Existing bandwidth ({})'.format(shaping_rate)
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                                #
			#                 CHECK CE TYPE                  #
			#                                                #
			if not re.findall('premium', serialized_data['l3_service_ce'], re.IGNORECASE):
				if cgate_error_message == 'Failed to create RFC in Cgate':
					output_message = 'Successfully provisioned new bandwidth at PE only (unmanaged CE) but failed to create RFC in CGate'
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = cgate_error_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Successfully provisioned new bandwidth at PE only (unmanaged CE)'
					if cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and unmanaged CE'
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'success',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'success'
					api_response['message'] = output_message
					api_response['data'] = 'No error'
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_200_OK)

			#                                                #
			#                CHECK CE VENDOR                 #
			#                                                #
			if not re.findall('cisco', serialized_data['cpe_type'], re.IGNORECASE) and not re.findall('juniper', serialized_data['cpe_type'], re.IGNORECASE):
				if cgate_error_message == 'Failed to create RFC in Cgate':
					output_message = 'Successfully provisioned new bandwidth at PE only but not managed {} CE which is not supported and failed to create RFC in CGate'.format(serialized_data['cpe_type'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = cgate_error_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Successfully provisioned new bandwidth at PE only but not managed {} CE which is not supported'.format(serialized_data['cpe_type'])
					if cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and failed to provision managed {} CE which is not supported'.format(serialized_data['cpe_type'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                                #
			#                   JUNIPER CE                   #
			#                                                #
			if re.findall('juniper', serialized_data['cpe_type'], re.IGNORECASE):
				url = 'https://pisa-juniper.prdin.kubix.tm/juniper/ce/v1/'

				#                                                        #
				#                GET JUNIPER CE INTERFACE                #
				#                                                        #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'get_interface/', params=payload, verify=False)
				output = response.json()

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving interface from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == "No CE interface is found":
						error_message = 'Failed to retrieve interface from CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						output_message = 'CE Interface ({}) has been successfully retrieved'.format(output['output'])
						serialized_data['ce_interface'] = output['output']
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				#                                               #
				#               GET JUNIPER CE QOS              #
				#                                               #
				ce_interface_split = serialized_data['ce_interface'].split('.')
				ce_port = ce_interface_split[0]
				ce_vlan = ce_interface_split[1]
				ce_interface_unit = '{} unit {}'.format(ce_port, ce_vlan)

				payload = {
					'ce_ip': serialized_data['ce_wan_ip'], 
					'interface': ce_interface_unit,
				}
				response = requests.get(url+'get_qos/', params=payload, verify=False)
				output = response.json()

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving QOS from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'CE QOS ({}) have been successfully retrieved'.format(output['output'])
					serialized_data['ce_qos'] = output['output']
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				#                                                        #
				#              COMPARE PE & JUNIPER CE QOS               #
				#                                                        #
				if serialized_data['ce_qos'] != serialized_data['pe_qos']:
					output_message = 'Mismatch QOS Package retrieved from PE ({}) and CE ({})'.format(serialized_data['pe_qos'], serialized_data['ce_qos'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = '{} .{}'.format(output_message, cgate_error_message)
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = 'PE QOS({}) and CE QOS ({})'.format(serialized_data['pe_qos'], serialized_data['ce_qos'])
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Both PE and CE QOS Package ({}) is match'.format(serialized_data['pe_qos'])
					log_text(filename=log_file, content=output_message)

				#                                                          #
				#              GEN JUNIPER CE CONFIGURATIONS               #
				#                                                          #
				ce_config_file = 'CE_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ce_wan_ip'],  date_now, time_now, user)		#create config file
				# parameters = [ce_port, ce_vlan, serialized_data['new_bandwidth']]
				
				payload = {
					'filename': ce_config_file,
					'port': ce_port,
					'vlan': ce_vlan,
					'bandwidth': serialized_data['new_bandwidth']
				}

				if serialized_data['ce_qos']=='CLASSIC':
					payload['scenario'] = 'a'
				else:
					payload['scenario'] = 'b'

				response = requests.post(url+'ipme/generate_config_modify_bw/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while generating CE configurations'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = output['output']
					log_text(filename=log_file, content=output_message)

				#                                                                      #
				#        GET EXISTING SHAPING-RATE FOR QOS OTHER THAN CLASSIC          #
				#                                                                      #
				if not re.search('CLASSIC', serialized_data['ce_qos'], re.IGNORECASE):

					payload = {
						'ce_ip': serialized_data['ce_wan_ip'], 
						'interface': ce_interface_unit,
					}
					output = requests.get(url+'get_qos_shaping_rate/', params=payload, verify=False).json()
					if output['status'] == 'failed':
						error_message = 'An error has occured while retrieving QOS interface shaping-rate for Interface ({}) at CE'.format(ce_interface_unit)
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if output['output'] == 'No shaping-rate is found':
							ce_bw = str()
						else:
							ce_bw = output['output']
						output_message = 'Shaping-rate returned from CE is ({})'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				else:
				#                                                           #
				#        GET EXISTING INTERFACE BW FOR QOS CLASSIC          #
				#                                                           #
					payload = {
						'ce_ip': serialized_data['ce_wan_ip'], 
						'interface': serialized_data['ce_interface'],
					}
					output = requests.get(url+'get_interface_bw/', params=payload, verify=False).json()
					if output['status'] == 'failed':
						error_message = 'An error has occured while retrieving interface bandwidth for Interface ({}) at CE'.format(serialized_data['ce_interface'])
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': 'N/A',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if output['output'] == 'No bandwidth is found':
							ce_bw = str()
						else:
							ce_bw = output['output']
						output_message = 'Interface bandwidth returned from CE is ({})'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				#                                                                                  #
				#              EXISTING BW MATCH OLD BW/EXISTING BW IS NOT CONFIGURED              #
				#                                                                                  #
				if ce_bw == '' or re.match(ce_bw, serialized_data['old_bandwidth'], re.IGNORECASE):

					#                                                  #
					#              PUSH JUNIPER CE CONFIG              #
					#                                                  #
					payload = {
						'ce_ip': serialized_data['ce_wan_ip'],
						'filename': ce_config_file
					}

					response = requests.post(url+'load_config/', data=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = 'An error has occured while loading the generated configurations to CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if output['output'] != 'Successfully loaded configurations at CE':
							error_message = 'CE configurations loading process is unsuccessful'
							if cgate_error_message == 'Failed to create RFC in Cgate':
								error_message = '{} .{}'.format(error_message, cgate_error_message)
							output_message = error_message + '\n[error message: {}]'.format(output['output'])
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							DB_data = {
								'order_type':'BOD',
								'service_type':'IPVPN',
								'input_details':serialized_data,
								'message':error_message,
								'api_output':str(output['output']),
								'status':'failed',
								'log_file': log_file,
								'config_file': config_file + ',' + ce_config_file,
								'user': user
							}
							api_response['status'] = 'failed'
							api_response['message'] = error_message
							api_response['data'] = output['output']
							log_database(data_DB=DB_data)
							return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
						else:
							if cgate_error_message == 'Failed to create RFC in Cgate':
								output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in CGate'
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								DB_data = {
									'order_type':'BOD',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':output_message,
									'api_output':str(output['output']),
									'status':'failed',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'failed'
								api_response['message'] = output_message
								api_response['data'] = cgate_error_message
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							elif cgate_error_message == 'RFC application is not required':
								output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								DB_data = {
									'order_type':'BOD',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':output_message,
									'api_output':str(output['output']),
									'status':'success',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'success'
								api_response['message'] = output_message
								api_response['data'] = 'No error'
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_200_OK)
							else:
								log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])
								success_message = 'Successfully provisioned new bandwidth at PE and CE'
								log_text(filename=log_file, content=success_message, ne_output=output['ne_response'])
								DB_data = {
									'order_type':'BOD',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message': success_message,
									'api_output':str(output['output']),
									'status':'success',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'success'
								api_response['message'] = success_message
								api_response['data'] = 'No error'
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_200_OK)

				#                                                    #
				#              EXISTING BW MATCH NEW BW              #
				#                                                    #
				elif re.match(ce_bw, serialized_data['new_bandwidth'], re.IGNORECASE):

					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = 'Successfully provisioned new bandwidth at PE and no change is required at CE (existing bandwidth ({}) match new bandwidth ({})). Failed to create RFC in CGate'.format(ce_bw, serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':cgate_error_message,
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = cgate_error_message
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					elif cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and CE (existing bandwidth ({}) match new bandwidth ({}))'.format(ce_bw, serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':'Existing bandwidth {} at CE'.format(ce_bw),
							'status':'success',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'success'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth {} at CE'.format(ce_bw)
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_200_OK)						
					else:
						log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])
						success_message = 'Successfully provisioned new bandwidth at PE and no change is required at CE (existing bandwidth ({}) match new bandwidth ({}))'.format(ce_bw, serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=success_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message': success_message,
							'api_output':'Existing bandwidth {} at CE'.format(ce_bw),
							'status':'success',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'success'
						api_response['message'] = success_message
						api_response['data'] = 'No error'
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_200_OK)

				#                                                              #
				#              EXISTING BW NOT MATCH NEW & OLD BW              #
				#                                                              #
				else:	
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = 'Successfully provisioned new bandwidth at PE but existing bandwidth ({}) at CE is not match old bandwidth ({}) and new bandwidth ({}). Failed to create RFC in CGate'.format(ce_bw, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':'Existing bandwidth {} at CE. '.format(ce_bw) + cgate_error_message,
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth {} at CE. '.format(ce_bw) + cgate_error_message,
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)					
					elif cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and existing bandwidth ({}) at CE is not match old bandwidth ({}) and new bandwidth ({})'.format(ce_bw, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':'Existing bandwidth {} at CE'.format(ce_bw),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth {} at CE'.format(ce_bw)
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)						
					else:
						output_message = 'Successfully provisioned new bandwidth at PE but existing bandwidth ({}) at CE is not match old bandwidth ({}) and new bandwidth ({})'.format(ce_bw, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message': output_message,
							'api_output':'Existing bandwidth {} at CE'.format(ce_bw),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth {} at CE'.format(ce_bw)
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)


			#                                                #
			#                    CISCO CE                    #
			#                                                #
			if re.findall('cisco', serialized_data['cpe_type'], re.IGNORECASE):

				url = 'https://pisa-cisco.prdin.kubix.tm/cisco/ce/'
				
				#                                                #
				#                GET CE INTERFACE                #
				#                                                #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'v1/get_interface/', params=payload, verify=False)
				output = response.json()

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving interface from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == "No CE interface with status 'up' is found":
						error_message = 'Failed to retrieve interface from CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						output_message = 'CE Interface ({}) has been successfully retrieved'.format(output['output'])
						serialized_data['ce_interface'] = output['output']
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				#                                                #
				#               GET CE QOS & Policy              #
				#                                                #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'], 
					'ce_interface': serialized_data['ce_interface'],
				}
				response = requests.get(url+'v1/get_qos_service_policy/', params=payload, verify=False)
				output = response.json()

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving QOS/service policy from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if len(output['output']) == 2:
						output_message = 'CE QOS ({}) and service policy ({}) have been successfully retrieved'.format(output['output']['qos'], output['output']['service-policy'])
						serialized_data['ce_qos'] = output['output']['qos']
						serialized_data['service_policy'] = output['output']['service-policy']
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					elif len(output['output']) == 1:
						output_message = 'CE QOS ({}) have been successfully retrieved'.format(output['output']['qos'])
						serialized_data['ce_qos'] = output['output']['qos']
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					else:
						error_message = 'Failed to retrieve QOS/service policy from CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

				#                                                #
				#              COMPARE PE & CE QOS               #
				#                                                #
				if serialized_data['ce_qos'] != serialized_data['pe_qos']:
					output_message = 'Mismatch QOS Package retrieved from PE ({}) and CE ({})'.format(serialized_data['pe_qos'], serialized_data['ce_qos'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = '{} .{}'.format(output_message, cgate_error_message)
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Both PE and CE QOS Package ({}) is match'.format(serialized_data['pe_qos'])
					log_text(filename=log_file, content=output_message)
					

				#                                                                   #
				#              GEN SHAPE AVERAGE AND INTERFACE NEW BW               #
				#                                                                   #
				if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
					serialized_data['shape_average'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000
					serialized_data['ce_bandwidth'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1
				elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
					serialized_data['shape_average'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000000
					serialized_data['ce_bandwidth'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000
				elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
					serialized_data['shape_average'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000000000
					serialized_data['ce_bandwidth'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000000
				else:
					output_message = 'New bandwidth ({}) does not contain a valid unit'.format(serialized_data['new_bandwidth'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = '{} .{}'.format(output_message, cgate_error_message)
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':serialized_data['new_bandwidth'],
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = serialized_data['new_bandwidth']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

				#                                                                   #
				#              GEN SHAPE AVERAGE AND INTERFACE OLD BW               #
				#                                                                   #
				if re.findall('k', serialized_data['old_bandwidth'], re.IGNORECASE):
					serialized_data['old_shape_average'] = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000
					serialized_data['old_ce_bandwidth'] = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1
				elif re.findall('m', serialized_data['old_bandwidth'], re.IGNORECASE):
					serialized_data['old_shape_average'] = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000000
					serialized_data['old_ce_bandwidth'] = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000
				elif re.findall('g', serialized_data['old_bandwidth'], re.IGNORECASE):
					serialized_data['old_shape_average'] = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000000000
					serialized_data['old_ce_bandwidth'] = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000000
				else:
					output_message = 'Old bandwidth ({}) does not contain a valid unit'.format(serialized_data['old_bandwidth'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = '{} .{}'.format(output_message, cgate_error_message)
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':serialized_data['old_bandwidth'],
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = serialized_data['old_bandwidth']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

				#                                                  #
				#              GEN CE CONFIGURATIONS               #
				#                                                  #
				ce_config_file = 'CE_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ce_wan_ip'],  date_now, time_now, user)		#create config file
				# parameters = [serialized_data['ce_interface'], serialized_data['ce_bandwidth']]

				payload = {
					'interface': serialized_data['ce_interface'],
					'interface_bw': serialized_data['ce_bandwidth'],
					'filename': ce_config_file,
				}

				if serialized_data['ce_qos']=='CLASSIC':
					payload['scenario'] = 'a'
					payload['service_policy'] = ''
					payload['shape_average'] = ''
				else:
					payload['scenario'] = 'b'
					payload['service_policy'] = serialized_data['service_policy']
					payload['shape_average'] = serialized_data['shape_average']
				
				response = requests.post(url+'v1/ipme/generate_config_modify_bw/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while generating CE configurations'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = output['output']
					log_text(filename=log_file, content=output_message)

				#                                                                      #
				#        GET EXISTING SHAPE AVERAGE FOR QOS NOT EQUAL CLASSIC          #
				#                                                                      #
				if not re.search('CLASSIC', serialized_data['ce_qos'], re.IGNORECASE):

					payload = {
						'ce_ip': serialized_data['ce_wan_ip'], 
						'ce_service_policy': serialized_data['service_policy'],
					}
					output = requests.get(url+'v1/get_shape_average/', params=payload, verify=False).json()
					if output['status'] == 'failed':
						error_message = 'An error has occured while retrieving shape-average at CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if output['output'] == 'No shape average is found':
							ce_shape_average = str()
						else:
							ce_shape_average = output['output']
						output_message = 'Shape average returned from CE is ({})'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				#                                                        #
				#        GET EXISTING INTERFACE BW FOR ALL QOS           #
				#                                                        #

				payload = {
					'ce_ip': serialized_data['ce_wan_ip'], 
					'ce_interface': serialized_data['ce_interface'],
				}
				output = requests.get(url+'v1/get_interface_bw/', params=payload, verify=False).json()
				if output['status'] == 'failed':
					error_message = 'An error has occured while retrieving interface bandwidth for interface ({}) at CE'.format(serialized_data['ce_interface'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file + ',' + ce_config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == 'No interface bandwidth is found':
						ce_interface_bw = str()
					else:
						ce_interface_bw = output['output']
					output_message = 'Interface bandwidth returned from CE is ({})'.format(output['output'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])


				#                                     #
				#        COMPARE EXISTING BW          #
				#                                     #
				existing_cisco_ce_bw = list()

				if not re.search('CLASSIC', serialized_data['ce_qos'], re.IGNORECASE):

					if re.search(str(ce_shape_average), str(serialized_data['old_shape_average']), re.IGNORECASE):
						if re.search(str(ce_interface_bw), str(serialized_data['old_ce_bandwidth']), re.IGNORECASE):
							existing_cisco_ce_bw = ['old_bandwidth', ce_shape_average]
						elif re.search(str(ce_interface_bw), str(serialized_data['ce_bandwidth']), re.IGNORECASE):
							existing_cisco_ce_bw = ['old_bandwidth', ce_shape_average]
						else:
							existing_cisco_ce_bw = ['different_bandwidth', ce_interface_bw]

					elif re.search(str(ce_shape_average), str(serialized_data['shape_average']), re.IGNORECASE):
						if re.search(str(ce_interface_bw), str(serialized_data['old_ce_bandwidth']), re.IGNORECASE):
							existing_cisco_ce_bw = ['old_bandwidth', ce_interface_bw]
						elif re.search(str(ce_interface_bw), str(serialized_data['ce_bandwidth']), re.IGNORECASE):
							existing_cisco_ce_bw = ['new_bandwidth', ce_shape_average]
						else:
							existing_cisco_ce_bw = ['different_bandwidth', ce_interface_bw]
					
					else:
						existing_cisco_ce_bw = ['different_bandwidth', ce_shape_average]

				else:

					if str(ce_interface_bw) == '' or re.search(str(ce_interface_bw), str(serialized_data['old_ce_bandwidth']), re.IGNORECASE):
						existing_cisco_ce_bw = ['old_bandwidth', ce_interface_bw]

					elif re.search(str(ce_interface_bw), str(serialized_data['ce_bandwidth']), re.IGNORECASE):
						existing_cisco_ce_bw = ['new_bandwidth', ce_interface_bw]

					else:
						existing_cisco_ce_bw = ['different_bandwidth', ce_interface_bw]

				#                                                    #
				#              EXISTING BW MATCH OLD BW              #
				#                                                    #
				if existing_cisco_ce_bw[0] == 'old_bandwidth':

					#                                          #
					#              PUSH CE CONFIG              #
					#                                          #
					payload = {
						'ce_ip': serialized_data['ce_wan_ip'],
						'filename': ce_config_file
					}

					response = requests.post(url+'v1/load_config/', data=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = 'An error has occured while loading the generated configurations to CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if output['output'] != 'Successfully loaded configurations at CE':
							error_message = 'CE configurations loading process is unsuccessful'
							if cgate_error_message == 'Failed to create RFC in Cgate':
								error_message = '{} .{}'.format(error_message, cgate_error_message)
							output_message = error_message + '\n[error message: {}]'.format(output['output'])
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							DB_data = {
								'order_type':'BOD',
								'service_type':'IPVPN',
								'input_details':serialized_data,
								'message':error_message,
								'api_output':str(output['output']),
								'status':'failed',
								'log_file': log_file,
								'config_file': config_file + ',' + ce_config_file,
								'user': user
							}
							api_response['status'] = 'failed'
							api_response['message'] = error_message
							api_response['data'] = output['output']
							log_database(data_DB=DB_data)
							return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
						else:
							ce_output_message = 'Successfully provisioned new bandwidth at CE'
							log_text(filename=log_file, content=ce_output_message, ne_output=output['ne_response'])

				#                                                    #
				#              EXISTING BW MATCH NEW BW              #
				#                                                    #
				elif existing_cisco_ce_bw[0] == 'new_bandwidth':
					ce_output_message = 'No change is required at CE (existing bandwidth ({}) match new bandwidth ({}))'.format(existing_cisco_ce_bw[1], serialized_data['new_bandwidth'])
					log_text(filename=log_file, content=ce_output_message)

				#                                                              #
				#              EXISTING BW NOT MATCH NEW & OLD BW              #
				#                                                              #
				else:	
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = 'Successfully provisioned new bandwidth at PE but existing bandwidth ({}) at CE is not match old bandwidth ({}) and new bandwidth ({}). Failed to create RFC in CGate'.format(existing_cisco_ce_bw[1], serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':'Existing bandwidth {} at CE. '.format(existing_cisco_ce_bw[1]) + cgate_error_message,
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth ({}) at CE. '.format(existing_cisco_ce_bw[1]) + cgate_error_message,
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)					
					elif cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and existing bandwidth ({}) at CE is not match old bandwidth ({}) and new bandwidth ({})'.format(existing_cisco_ce_bw[1], serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':'Existing bandwidth ({}) at CE'.format(existing_cisco_ce_bw[1]),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth {} at CE'.format(existing_cisco_ce_bw[1])
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])
						output_message = 'Successfully provisioned new bandwidth at PE but existing bandwidth ({}) at CE is not match old bandwidth ({}) and new bandwidth ({})'.format(existing_cisco_ce_bw[1], serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message': output_message,
							'api_output':'Existing bandwidth {} at CE'.format(existing_cisco_ce_bw[1]),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'Existing bandwidth {} at CE'.format(existing_cisco_ce_bw[1])
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

				#                                                  #
				#               CHECK CE SOFTWARE                  #
				#                                                  #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'v2/show_version/', params=payload, verify=False)
				output = response.json()

				# expected output
				# {
				# 	"software": "Cisco IOS Software",
				# 	"model": "CISCO2911/K9"
				# }

				if output['status'] == 'failed':
					error_message = 'An error has occured while checking CE software details'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'BOD',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file + ',' + ce_config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == 'CE software/model version is not found':
						error_message = output['output']
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'BOD',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:

						output_message = "Successfully retrieved CE software/model version"
						log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

						if re.search('xe', output['output']['software'], re.IGNORECASE) or re.search('xr', output['output']['software'], re.IGNORECASE):
							#                                                   #
							#               CHECK CE THROUGHPUT                 #
							#                                                   #
							payload = {
								'ce_ip': serialized_data['ce_wan_ip'],
							}
							response = requests.get(url+'v2/get_throughput/', params=payload, verify=False)
							output = response.json()
							if output['status'] == 'failed':
								error_message = 'An error has occured while checking CE throughput'
								if cgate_error_message == 'Failed to create RFC in Cgate':
									error_message = '{} .{}'.format(error_message, cgate_error_message)
								output_message = error_message + '\n[error message: {}]'.format(output['output'])
								log_text(filename=log_file, content=output_message)
								DB_data = {
									'order_type':'BOD',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':error_message,
									'api_output':str(output['output']),
									'status':'failed',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'failed'
								api_response['message'] = error_message
								api_response['data'] = output['output']
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							else:
								if output['output'] == 'CE throughput level is unthrottled' or output['output'] == 'No CE throughput level is found':
									error_message = 'CE throughput level is not configured'
									if cgate_error_message == 'Failed to create RFC in Cgate':
										error_message = '{} .{}'.format(error_message, cgate_error_message)
									output_message = error_message + '\n[error message: {}]'.format(output['output'])
									log_text(filename=log_file, content=output_message)
									DB_data = {
										'order_type':'BOD',
										'service_type':'IPVPN',
										'input_details':serialized_data,
										'message':error_message,
										'api_output':str(output['output']),
										'status':'failed',
										'log_file': log_file,
										'config_file': config_file + ',' + ce_config_file,
										'user': user
									}
									api_response['status'] = 'failed'
									api_response['message'] = error_message
									api_response['data'] = output['output']
									log_database(data_DB=DB_data)
									return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
								else:
									#                                           #
									#              GEN THROUGHPUT               #
									#                                           #
									if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
										serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2
									elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
										serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2000
									elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
										serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2000000
									else:
										output_message = 'New bandwidth ({}) does not contain a valid unit'.format(serialized_data['new_bandwidth'])
										if cgate_error_message == 'Failed to create RFC in Cgate':
											output_message = '{} .{}'.format(output_message, cgate_error_message)
										log_text(filename=log_file, content=output_message)
										DB_data = {
											'order_type':'BOD',
											'service_type':'IPVPN',
											'input_details':serialized_data,
											'message':output_message,
											'api_output':serialized_data['new_bandwidth'],
											'status':'failed',
											'log_file': log_file,
											'config_file': config_file + ',' + ce_config_file,
											'user': user
										}
										api_response['status'] = 'failed'
										api_response['message'] = output_message
										api_response['data'] = serialized_data['new_bandwidth']
										log_database(data_DB=DB_data)
										return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

									if serialized_data['ce_throughput'] > int(output['output']['throughput']):
										error_message = 'The new bandwidth {} (times 2) is more than allowable CE throughput level {}'.format(serialized_data['new_bandwidth'].lower(), str(output['output']['throughput'])+'k')
										if cgate_error_message == 'Failed to create RFC in Cgate':
											error_message = '{} .{}'.format(error_message, cgate_error_message)
										log_text(filename=log_file, content=error_message)
										DB_data = {
											'order_type':'BOD',
											'service_type':'IPVPN',
											'input_details':serialized_data,
											'message':error_message,
											'api_output': 'CE througput level is {}'.format(str(output['output']['throughput'])+'k'),
											'status':'failed',
											'log_file': log_file,
											'config_file': config_file + ',' + ce_config_file,
											'user': user
										}
										api_response['status'] = 'failed'
										api_response['message'] = error_message
										api_response['data'] = 'CE througput level is {}'.format(str(output['output']['throughput'])+'k')
										log_database(data_DB=DB_data)
										return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
									else:
										output_message = 'The new bandwidth {} (times 2) is lesser than allowable CE throughput level {}'.format(serialized_data['new_bandwidth'].lower(), str(output['output']['throughput'])+'k')
										log_text(filename=log_file, content=output_message)

										if cgate_error_message == 'Failed to create RFC in Cgate':
											if 'Successfully provisioned' in ce_output_message:
												output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
											elif 'No change' in ce_output_message:
												output_message = 'Successfully provisioned new bandwidth at PE and {}. Failed to create RFC in Cgate'.format(ce_output_message)
											
											log_text(filename=log_file, content=output_message)
											DB_data = {
												'order_type':'BOD',
												'service_type':'IPVPN',
												'input_details':serialized_data,
												'message':output_message,
												'api_output':cgate_error_message,
												'status':'failed',
												'log_file': log_file,
												'config_file': config_file,
												'user': user
											}
											api_response['status'] = 'failed'
											api_response['message'] = output_message
											api_response['data'] = cgate_error_message
											log_database(data_DB=DB_data)
											return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

										elif cgate_error_message == 'RFC application is not required':
											if 'Successfully provisioned' in ce_output_message:
												output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
											elif 'No change' in ce_output_message:
												output_message = 'No change is required at PE and {}'.format(ce_output_message)

											log_text(filename=log_file, content=output_message)
											DB_data = {
												'order_type':'BOD',
												'service_type':'IPVPN',
												'input_details':serialized_data,
												'message':output_message,
												'api_output':output_message,
												'status':'success',
												'log_file': log_file,
												'config_file': config_file,
												'user': user
											}
											api_response['status'] = 'success'
											api_response['message'] = output_message
											api_response['data'] = output_message
											log_database(data_DB=DB_data)
											return Response(api_response,status=status.HTTP_200_OK)
										else:
											if 'Successfully provisioned' in ce_output_message:
												success_message = 'Successfully provisioned new bandwidth at PE and CE'
											elif 'No change' in ce_output_message:
												success_message = 'Successfully provisioned new bandwidth at PE and {}'.format(ce_output_message)
										
											log_text(filename=log_file, content=success_message)
											DB_data = {
												'order_type':'BOD',
												'service_type':'IPVPN',
												'input_details':serialized_data,
												'message': success_message,
												# 'api_output':str(output['output']),
												'api_output':output_message,
												'status':'success',
												'log_file': log_file,
												'config_file': config_file + ',' + ce_config_file,
												'user': user
											}
											api_response['status'] = 'success'
											api_response['message'] = success_message
											api_response['data'] = 'No error'
											log_database(data_DB=DB_data)
											return Response(api_response,status=status.HTTP_200_OK)

						else:
							ce_version_bandwidth = [
								# {'version':'3945E','bandwidth':350},
								# {'version':'3925E','bandwidth':250},
								# {'version':'3945','bandwidth':150},
								# {'version':'3925','bandwidth':100},
								# {'version':'2951','bandwidth':75},
								# {'version':'2921','bandwidth':50},
								# {'version':'2911','bandwidth':35},
								# {'version':'2901','bandwidth':25},
								# {'version':'1941','bandwidth':25},
								# {'version':'1921','bandwidth':15},
								# {'version':'890','bandwidth':15},
								# {'version':'880','bandwidth':8},
								# {'version':'860','bandwidth':4},
								{'version':'3945E','bandwidth':350},
								{'version':'3925E','bandwidth':250},
								{'version':'3945','bandwidth':150},
								{'version':'3925','bandwidth':100},
								{'version':'2951','bandwidth':75},
								{'version':'2921','bandwidth':50},
								{'version':'2911','bandwidth':35},
								{'version':'2901','bandwidth':25},
								{'version':'1941','bandwidth':9},
								{'version':'1921','bandwidth':7},
								{'version':'890','bandwidth':25},
								{'version':'887VAG+7-K9','bandwidth':21},
								{'version':'887VA-K9','bandwidth':15},
								{'version':'880','bandwidth':15},
								{'version':'860VAE','bandwidth':8},
								{'version':'860','bandwidth':4},
								{'version':'819G+7-K9','bandwidth':8},
							]

							ce_version_bw = str()
							for x in ce_version_bandwidth:
								if x['version'] in output['output']['model']:
									ce_version_bw = x['bandwidth']			# determine allowed bandwidth
									break

							if ce_version_bw and int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 > ce_version_bw:		# check if new BW X 2 more than allowed bandwidth
								error_message = 'The new bandwidth {} (times 2) is more than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
								if cgate_error_message == 'Failed to create RFC in Cgate':
									error_message = '{} .{}'.format(error_message, cgate_error_message)
								log_text(filename=log_file, content=error_message)
								DB_data = {
									'order_type':'BOD',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':error_message,
									'api_output': 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m'),
									'status':'failed',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'failed'
								api_response['message'] = error_message
								api_response['data'] = 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m')
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							else:
								if ce_version_bw and int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 < ce_version_bw:
									output_message = 'The new bandwidth {} (times 2) is lesser than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
								else:
									output_message = 'The CE model ({}) is not exist in CE version list'.format(output['output']['model'])
								log_text(filename=log_file, content=output_message)
								if cgate_error_message == 'Failed to create RFC in Cgate':
									output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
									
									log_text(filename=log_file, content=output_message)
									DB_data = {
										'order_type':'BOD',
										'service_type':'IPVPN',
										'input_details':serialized_data,
										'message':output_message,
										'api_output':cgate_error_message,
										'status':'failed',
										'log_file': log_file,
										'config_file': config_file + ',' + ce_config_file,
										'user': user
									}
									api_response['status'] = 'failed'
									api_response['message'] = output_message
									api_response['data'] = cgate_error_message
									log_database(data_DB=DB_data)
									return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

								elif cgate_error_message == 'RFC application is not required':
									output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
									db_config_file = ce_config_file

								else:
									output_message = 'Successfully provisioned new bandwidth at PE and CE'
									db_config_file = config_file + ',' + ce_config_file

								log_text(filename=log_file, content=output_message)
								DB_data = {
									'order_type':'BOD',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':output_message,
									'api_output':output_message,
									'status':'success',
									'log_file': log_file,
									'config_file': db_config_file,
									'user': user
								}
								api_response['status'] = 'success'
								api_response['message'] = output_message
								api_response['data'] = output_message
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_200_OK)


		except Exception as error:
			output_error = list()
			exc_type, exc_obj, exc_tb = sys.exc_info()
			output_error.append(str(exc_type))
			output_error.append(str(exc_obj))
			output_error.append(str(exc_tb.tb_lineno))
			error_message = 'Unexpected error has occured while executing API'
			output_message = error_message + '\n[error message: {}]'.format(error)
			log_text(filename=log_file, content=output_message)
			DB_data = {
				'order_type':'BOD',
				'service_type':'IPVPN',
				'input_details':request.data,
				'message':error_message,
				'api_output':str(error),
				'status':'failed',
				'log_file': log_file,
				'config_file': 'Undefined',
				'user': user
			}
			api_response['status'] = 'failed'
			api_response['message'] = error_message
			# api_response['data'] = str(error)
			api_response['data'] = output_error
			log_database(data_DB=DB_data)
			return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

		except OperationalError as operational_error:
			return Response({'status': 'failed', 'message': str(operational_error)},status=status.HTTP_400_BAD_REQUEST)