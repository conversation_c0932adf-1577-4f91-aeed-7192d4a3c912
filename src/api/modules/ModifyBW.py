from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import BodSerializer
import requests, json, time, re, os
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
# from rest_framework.permissions import IsAuthenticated
# from rest_framework.authentication import TokenAuthentication

class API(APIView):

	# authentication_classes = [TokenAuthentication]
	# permission_classes = [IsAuthenticated]

	@swagger_auto_schema(
		request_body=BodSerializer,
		responses={201: BodSerializer()},
	)

	def post(self, request):

		# GET USER ID
		token = request.META.get('HTTP_AUTHORIZATION')
		if token:
			user_id = Token.objects.get(key=token.replace('Token ','')).user_id
			user = User.objects.get(id=user_id).username
		else:
			user = 'not_available'

		#                                           #
		#              VERIFY API INPUT             #
		#                                           #
		input_data = BodSerializer(data=request.data)
		input_data.is_valid()

		if len(input_data.errors) > 0:  # check if passed data contains error and return error message
			output_message = 'incomplete/invalid information fetched by API'
			DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(input_data.data),'message':output_message, 'api_output':str(input_data.errors),'status':'failed','log_file':'not available', 'config_file': 'not available', 'user': user}
			log_database(data_DB=DB_data)
			return Response({
				'status':'failed',
				'output': output_message,
				'data': input_data.errors
			},status=status.HTTP_400_BAD_REQUEST)

		serialized_data = input_data.data   # assign a variable with serialized data
		date_now = datetime.now().strftime('%d-%m-%Y')
		time_now = datetime.now().strftime('%H:%M:%S')
		log_file = 'BOD_{}_{}_{}@{}_by_{}.txt'.format(serialized_data['service_id'], serialized_data['ne_name'], date_now, time_now, user)		# Create Log File

		output_message = 'Information fetched by API contains no error'
		log_text(filename=log_file, content=output_message, ne_output=str(serialized_data))
		url = 'http://pisa-juniper.poc.kubix.tm/juniper/v1/'	# declare main url variable

		#                                                #
		#          GENERATE PE BOD CONFIGURATION         #
		#                                                #
		config_file = 'PE_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ne_name'],  date_now, time_now, user)		#create config file
		interface_split = serialized_data['interface'].split('.')
		port = interface_split[0]
		vlan = interface_split[1]
		parameters = [port, vlan, serialized_data['bandwidth'].lower()]

		payload = {
			'filename': config_file,
			'parameters': json.dumps(parameters)
		}

		response = requests.post(url+'config_modify_bw/', data=payload)
		output = response.json()

		if output['status'] == 'failed':
			error_message = 'An error has occured while generating configurations'
			output_message = error_message + '\n[error message: {}]'.format(output['output'])
			log_text(filename=log_file, content=output_message)
			DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
			log_database(data_DB=DB_data)
			return Response({
				'status':'failed',
				'output': error_message,
				'data': output['output']
			},status=status.HTTP_400_BAD_REQUEST)
		else:
			output_message = output['output']
			log_text(filename=log_file, content=output_message)

		#                                                #
		#        CHECK PE IP AND INTERFACE MATCH         #
		#                                                #
		payload = {
			'ne_ip': serialized_data['ne_ip'],
			'username': serialized_data['username'],
			'password': serialized_data['password'],
			'interface': serialized_data['interface'],
			'pe_ip': serialized_data['pe_ip']
		}
		response = requests.get(url+'check_interface_ip/', params=payload)
		output = response.json()

		if output['status'] == 'failed':
			error_message = 'An error has occured while validating PE IP Address ({}) & Interface ({})'.format(serialized_data['pe_ip'], serialized_data['interface'])
			output_message = error_message + '\n[error message: {}]'.format(output['output'])
			log_text(filename=log_file, content=output_message)
			DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
			log_database(data_DB=DB_data)
			return Response({
				'status':'failed',
				'output': error_message,
				'data': output['output']
			},status=status.HTTP_400_BAD_REQUEST)
		else:
			if output['output'] == 'Interface is not match with IP Address':
				error_message = 'Mismatched PE IP Address ({}) & Interface ({})'.format(serialized_data['pe_ip'], serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
				DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
				log_database(data_DB=DB_data)
				return Response({
					'status':'failed',
					'output':error_message,
					'data':output['output']
				},status=status.HTTP_400_BAD_REQUEST)
			else:
				output_message = 'PE IP Address ({}) match with Interface ({})'.format(serialized_data['pe_ip'], serialized_data['interface'])
				log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

		#                                                #
		#                 CHECK QOS PE                   #
		#                                                #
		payload = {
			'ne_ip': serialized_data['ne_ip'],
			'username': serialized_data['username'],
			'password': serialized_data['password'],
			'interface': serialized_data['interface'],
		}

		response = requests.get(url+'check_qos/',params=payload)
		output = response.json()

		if output['status'] == 'failed':
			error_message = 'An error has occured while retreiving QOS Package from PE Interface ({})'.format(serialized_data['interface'])
			output_message = error_message + '\n[error message: {}]'.format(output['output'])
			log_text(filename=log_file, content=output_message)
			DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
			log_database(data_DB=DB_data)
			return Response({
				'status':'failed',
				'output': error_message,
				'data': output['output']
			},status=status.HTTP_400_BAD_REQUEST)
		else:
			if output['output'] == 'No QOS package is found':
				error_message = 'No QOS package returned from PE Interface ({})'.format(serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
				DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
				log_database(data_DB=DB_data)
				return Response({
					'status':'failed',
					'output':error_message,
					'data':output['output']
				},status=status.HTTP_400_BAD_REQUEST)
			else:
				serialized_data['pe_qos'] = output['output']
				output_message = 'QOS Package ({}) has been successfully retreived from PE'.format(serialized_data['pe_qos'])
				log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

		#                                                #
		#               CHECK BGP Status                 #
		#                                                #
		if re.findall('bgp', serialized_data['routing_protocol'], re.IGNORECASE):	# check if routing-protocol = bgp/BGP

			payload = {
				'ne_ip': serialized_data['ne_ip'],
				'vrf_instance': serialized_data['vrf_instance'],
				'ce_ip': serialized_data['ce_ip'],
				'username': serialized_data['username'],
				'password': serialized_data['password']
			}
			response = requests.get(url+'check_bgp_status/', params=payload)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while retreiving BGP status from PE'
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
				log_database(data_DB=DB_data)
				return Response({
					'status':'failed',
					'output': error_message,
					'data': output['output']
				},status=status.HTTP_400_BAD_REQUEST)
			else:
				if output['output'] != 'Established':
				# if output['output'] != 'Idle':
					output_message = 'BGP status for CE IP Address ({}) returned from PE ({}) is not established'.format(serialized_data['ce_ip'], output['output'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':output_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
					log_database(data_DB=DB_data)
					return Response({
						'status':'failed',
						'output':output_message,
						'data':output['output']
					},status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'BGP status for CE IP Address ({}) returned from PE is ({})'.format(serialized_data['ce_ip'], output['output'])
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

		#                                                #
		#                   CHECK CPU                    #
		#                                                #
		match, count, ne_output, master_util_success, master_util_failed = False, int(), str(), dict(), dict()
		while not match:
			# if failed check 2 times
			if count > 2:
				break

			# create 5s delay to re-chek CPU
			if count > 0:
				time.sleep(5)

			payload = {
				'ne_ip': serialized_data['ne_ip'],
				'username': serialized_data['username'],
				'password': serialized_data['password']
			}

			response = requests.get(url + 'check_cpu/', params=payload)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while retreiving CPU utilization info from PE'
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
				log_database(data_DB=DB_data)
				return Response({
					'status':'failed',
					'message': output_message,
					'data':output['output']
				},status=status.HTTP_400_BAD_REQUEST)
			else:
				output_keys = output['output'].keys()
				for key in output_keys:
					if re.findall('main', key, re.IGNORECASE):
						# if int(output['output'][key]['Memory Utilization']) < 70 and int(output['output'][key]['Idle']) > 10:
						if int(output['output'][key]['Memory Utilization']) < 70 and int(output['output'][key]['Idle']) > 0:
							match = True
							master_util_success[key] = {
								'Memory Utilization':output['output'][key]['Memory Utilization'],
								'Idle':output['output'][key]['Idle']
							}
						else:
							match = False
							master_util_failed[key] = {
								'Memory Utilization':output['output'][key]['Memory Utilization'],
								'Idle':output['output'][key]['Idle']
							}

				# increase counter if does not match threshold
				if not match:
					count += 1
				ne_output += output['ne_response']		# accumulate ne output in a var

		if not match:
			output_message = str()
			for key in master_util_failed:
				if output_message == '':
					output_message = "CPU utilization is outside threshold i.e. {}'s memory utilization: {} and idle: {}".format(key, str(master_util_failed[key]['Memory Utilization'])+'%', str(master_util_failed[key]['Idle'])+'%')
				else:
					output_message += " and {}'s memory utilization: {} and idle: {}".format(key, str(master_util_failed[key]['Memory Utilization'])+'%', str(master_util_failed[key]['Idle'])+'%')
			log_text(filename=log_file, content=output_message, ne_output=ne_output)
			DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':output_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
			log_database(data_DB=DB_data)
			return Response({
				'status':'failed',
				'message':output_message,
				'data':output['output']
			},status=status.HTTP_400_BAD_REQUEST)
		else :
			output_message = str()
			for key in master_util_success:
				if output_message == '':
					output_message = "CPU utilization is within threshold i.e. {}'s memory utilization: {} and idle: {}".format(key, str(master_util_success[key]['Memory Utilization']) + '%' , str(master_util_success[key]['Idle']) + '%')
				else:
					output_message += " and {}'s memory utilization: {} and idle: {}".format(key, str(master_util_success[key]['Memory Utilization'])+'%', str(master_util_success[key]['Idle'])+'%')
			log_text(filename=log_file, content=output_message, ne_output=ne_output)

		#                                                #
		#                  CONFIGURE BW                  #
		#                                                #
		payload = {
			'ne_ip': serialized_data['ne_ip'],
			'username': serialized_data['username'],
			'password': serialized_data['password'],
			'filename': config_file
		}

		response = requests.post(url+'push_config_modify/', data=payload)
		output = response.json()

		if output['status'] == 'failed':
			error_message = 'An error has occured while loading the generated configurations to PE'
			output_message = error_message + '\n[error message: {}]'.format(output['output'])
			log_text(filename=log_file, content=output_message)
			DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
			log_database(data_DB=DB_data)
			return Response({
				'status':'failed',
				'message': error_message,
				'data':output['output']
			},status=status.HTTP_400_BAD_REQUEST)
		else:
			if output['output'] != 'Provisioning is successful':
				error_message = 'Configurations loading process is unsuccessful'
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
				DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':error_message, 'api_output':str(output['output']),'status':'failed','log_file': log_file, 'config_file': config_file, 'user': user}
				log_database(data_DB=DB_data)
				return Response({
					'status':'failed',
					'message': error_message,
					'data':output['output']
				},status=status.HTTP_400_BAD_REQUEST)
			else:
				log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])
				DB_data = {'order_type':'Bandwidth On Demand','service_type':'IPVPN','input_details':str(serialized_data),'message':'Successfully provisioned new bandwidth at PE', 'api_output':str(output['output']),'status':'success','log_file': log_file, 'config_file': config_file, 'user': user}
				log_database(data_DB=DB_data)
				return Response({
					'status':'success',
					'message': output['output'],
				},status=status.HTTP_200_OK)

		# return Response({'status': 'success'})
