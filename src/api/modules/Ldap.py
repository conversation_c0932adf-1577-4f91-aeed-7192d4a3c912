from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.serializers import authLdapSerializer
from drf_yasg.utils import swagger_auto_schema
import ldap

class Authenticate(APIView):
    
    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()
    
    @swagger_auto_schema(
        query_serializer=authLdapSerializer,
        operation_summary="Authenticate user using LDAP"
    )

    def get(self, request):
        
        # verify passed data
        input_data=authLdapSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({
                'status': 'failed', 
                'output': 'incomplete/invalid information fetch by API', 
                'data': input_data.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        else:
            try:
                # Initialize variables using the passed data
                serialized_data = input_data.data

                conn = self.connectLdap()
                conn.simple_bind_s(f"cn={serialized_data['username']}, ou=users, o=data", serialized_data['password'])

                return Response({
                    'status': 'success',
                    'output': 'Successfully authenticated'
                }, status=status.HTTP_200_OK)

            except ldap.INVALID_CREDENTIALS:
                return Response({
                    'status': 'failed',
                    'output': 'Failed to authenticate'
                }, status=status.HTTP_401_UNAUTHORIZED)

            except Exception as error:
                return Response({
                    'status': 'failed', 
                    'output': str(error),
                }, status=status.HTTP_400_BAD_REQUEST)
                
    def connectLdap(self):
        ldap_conn = ldap.initialize('ldap://************')
        ldap_conn.set_option(ldap.OPT_REFERRALS, 0)
        ldap_conn.set_option(ldap.OPT_PROTOCOL_VERSION, 3)
        ldap_conn.simple_bind_s('cn=ipgenldapadmin, ou=serviceAccount, o=Telekom', 'cnNla3V')
        return ldap_conn