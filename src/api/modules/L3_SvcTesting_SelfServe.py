from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import L3ServiceTestingSelfServeSerializer
import requests, json, time, re, os, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps
from concurrent.futures import ThreadPoolExecutor

class API(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        request_body=L3ServiceTestingSelfServeSerializer,
        responses={201: L3ServiceTestingSelfServeSerializer()},
        operation_summary="Workflow for Self Serve Application for both L3 Service Testing"
    )

    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = L3ServiceTestingSelfServeSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            
            # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')

            # juniper show_config
            # {
            # "ne_ip": "string",
            # "commands": [
            #     "string"
            # ],
            # "username": "string",
            # "password": "string"
            # }
            # workflow
            # {
            # "network_type": "pe/nid",
            # "vendor": "juniper/zte/nokia/huawei",
            # "ne_ip": "string",
            # "pe_interface": "string",
            # "pe_ip": "string",
            # "ce_ip": "string",
            # "nid_port_uplink": "string",
            # "service_testing":
            # {
            #     "interface_status": true/false,
            #     "interface_power_level": "true/false,
            #     "bandwidth_subscription": true/false,
            #     "ping-test_pe-ce": true/false,
            #     "ping-gipvpn_pe-ce": true/false,
            #     "traceroute": true/false,
            #     "ping_lan_ip": true/false,
            #     "incoming_route": true/false,
            #     "advertise_route": true/false,
            #     "route_summary": true/false,
            #     "ip_prefix_register": true/false,
            #     "bgp_status": true/false,
            #     "show_routing_table": true/false,
            #     "nid_loopback_checking": true/false,
            #     "l2vpn_mpls_ping-domestic_igw": true/false
            # },
            # "traceroute_lan-ip": array(),
            # "ping_lan-ip": array(),
            # "advertise_route_lan-ip": array(),
            # "show_routing_table_lan-ip": array()
            # }
            # Workflow
            # 0. check require information
            # 1. check network_type
            # 2. check vendor
            # 3. check service_testing
            # 4. display output
            # interface_status {
            # "status": "success/failed",
            # "summary": "physical up",
            # "ne_response": <ne_output>
            # }

            # ************* igw21-glob.pujlab-re0                            Dynamic   - BAU
            # ************* igwnk41.lab                                      Dynamic   - NGT
            # ************* igwnk42.lab                                      Dynamic   - NGT
            # *************4 igwnk43.lab                                      Dynamic   - NGT
            # ************* hsezt41.lab                                      Dynamic   - NGT
            # ************* hsezt42.lab                                      Dynamic   - NGT

            combined_response = []

            if serialized_data['network_type'] == 'pe' and serialized_data['vendor'] == 'juniper':

                # Interface Status
                if serialized_data['service_testing']['interface_status']:

                    # Interface Status Check
                    interface_status = {
                        'interface_status': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    interface_status['interface_status'] = True

                    try:

                        combined_ne_response = ""

                        if re.search(r'\.', serialized_data['pe_interface'], re.IGNORECASE):
                            command = f'show interfaces {serialized_data["pe_interface"].split(".")[0]} | match ae'
                        else:
                            command = f'show interfaces {serialized_data["pe_interface"]} | match ae'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response_ae = requests.get(url_pe, verify=False)
                        response_ae.raise_for_status()  # Check for HTTP errors
                        output_ae = response_ae.json()
                        ne_response_ae = output_ae.get('ne_response', '')
                        combined_ne_response += ne_response_ae + "\n\n"  # Add response to combined response

                        # Check if "Protocol aenet, AE bundle:" in the response
                        ae_bundle_value = ""
                        if "Protocol aenet, AE bundle" in ne_response_ae:
                            # Extract the AE bundle value
                            match = re.search(r'Protocol aenet, AE bundle: (\S+)', ne_response_ae)
                            if match:
                                ae_bundle_value = match.group(1)

                        int_match_physical_int = ''

                        if ae_bundle_value != "":
                            # If ae_bundle_value contains '.', split and use the first part, else use ae_bundle_value directly
                            if '.' in ae_bundle_value:
                                int_match_physical_int = ae_bundle_value.split('.')[0]
                            else:
                                int_match_physical_int = ae_bundle_value
                            command = f'show interfaces {int_match_physical_int}'
                            command_terse = f'show interfaces {int_match_physical_int} terse'
                        else:
                            int_match_physical_int = serialized_data["pe_interface"].split(".")[0]
                            command = f'show interfaces {int_match_physical_int}'
                            command_terse = f'show interfaces {int_match_physical_int} terse'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()

                        # Assuming the relevant output is in output['ne_response']
                        ne_response = output.get('ne_response', '')
  
                        # Extract the desired line
                        lines = ne_response.split('\r\n')
                        interface_status_line = ''
                        for line in lines:
                            if line.startswith(f'Physical interface: {int_match_physical_int},'):
                                interface_status_line = line
                                break

                        # combined_ne_response += ne_response + "\n\n"  # Add response to combined response

                        # Add New Command
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command_terse}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()

                        # Assuming the relevant output is in output['ne_response']
                        ne_response = output.get('ne_response', '')
                        combined_ne_response += ne_response + "\n\n"  # Add response to combined response

                        interface_status.update({'status': 'success', 'data': interface_status_line, 'ne_response': combined_ne_response})

                    except requests.exceptions.RequestException as e:
                        interface_status.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(interface_status)

                # Interface Power Level
                if serialized_data['service_testing']['interface_power_level']:

                    # Interface Power Level Check
                    interface_power_level = {
                        'interface_power_level': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    interface_power_level['interface_power_level'] = True

                    try:
                        command = f'show interfaces diagnostics optics {serialized_data["pe_interface"].split(".")[0]}'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()

                        ne_response = output.get('ne_response', '')
                        lines = ne_response.split('\r\n')
                        power_data = []
                        for line in lines:
                            if 'Laser output power' in line or 'Receiver signal average optical power' in line:
                                power_data.append(line.strip())

                        interface_power_level.update({'status': 'success', 'data': '\n'.join(power_data), 'ne_response': ne_response})

                    except requests.exceptions.RequestException as e:
                        interface_power_level.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(interface_power_level)

                # Bandwidth Subscription
                if serialized_data['service_testing']['bandwidth_subscription']:

                    # Interface Power Level Check
                    bandwidth_subscription = {
                        'bandwidth_subscription': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    bandwidth_subscription['bandwidth_subscription'] = True

                    try:
                        combined_ne_response = ""
                        combined_data_response = {
                            "Description": "",
                            "Policer": "",
                            "Shaping-rate": ""
                        }

                        # Get Interface Description
                        command = f'show configuration interfaces {serialized_data["pe_interface"]} description'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()
                        ne_response = output.get('ne_response', '')
                        combined_ne_response += ne_response + "\n\n"  # Add response to combined response

                        # Split ne_response by newlines
                        ne_response_lines = ne_response.split('\n')

                        # Search for description in each line
                        for line in ne_response_lines:
                            # Check if line contains description
                            if "description" in line:
                                # Extract Description
                                description_match = re.search(r'description\s*"([^"]+)"', line)
                                if description_match:
                                    description = description_match.group(1)
                                    # Update combined_data_response with the extracted description
                                    combined_data_response["Description"] = description
                                    # No need to search further, exit the loop
                                    break

                        # Check ae interface
                        if re.search(r'\.', serialized_data['pe_interface'], re.IGNORECASE):
                            pe_interface_base = serialized_data["pe_interface"].split(".")[0]
                            command = f'show interfaces {pe_interface_base} | match ae'
                        else:
                            pe_interface_base = serialized_data["pe_interface"]
                            command = f'show interfaces {pe_interface_base} | match ae'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response_ae = requests.get(url_pe, verify=False)
                        response_ae.raise_for_status()  # Check for HTTP errors
                        output_ae = response_ae.json()
                        ne_response_ae = output_ae.get('ne_response', '')
                        combined_ne_response += ne_response_ae + "\n\n"  # Add response to combined response

                        # Check if "Protocol aenet, AE bundle: not found" is in the response
                        ae_bundle_value = ""
                        if "Protocol aenet, AE bundle" in ne_response_ae:
                            # Extract the AE bundle value
                            match = re.search(r'Protocol aenet, AE bundle: (\S+)', ne_response_ae)
                            if match:
                                ae_bundle_value = match.group(1)

                        # Show Interface Ae or PE Interface
                        if ae_bundle_value:
                            ae_base, ae_unit = ae_bundle_value.split(".") if "." in ae_bundle_value else (ae_bundle_value, "")
                            command = f'show configuration interfaces {ae_base} unit {ae_unit} | match policer'
                        else:
                            command = f"show configuration interfaces {serialized_data['pe_interface']} | match policer"

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response_policer = requests.get(url_pe, verify=False)
                        response_policer.raise_for_status()  # Check for HTTP errors
                        output_policer = response_policer.json()
                        ne_response_policer = output_policer.get('ne_response', '')
                        combined_ne_response += ne_response_policer + "\n\n"  # Add response to combined response

                        # Extract Policer (input only)
                        policer_match = re.findall(r'input (\S+);', ne_response_policer)
                        if policer_match:
                            combined_data_response["Policer"] = ", ".join(policer_match)


                        if not ae_bundle_value and re.search(r'\.', serialized_data["pe_interface"], re.IGNORECASE):
                            # Shaping rate
                            pe_interface_split = serialized_data["pe_interface"].split(".")
                            if len(pe_interface_split) > 1:
                                pe_interface_base, pe_interface_unit = pe_interface_split
                                command = f'show configuration class-of-service interfaces {pe_interface_base} unit {pe_interface_unit}'
                                url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                                response_shaping_rate = requests.get(url_pe, verify=False)
                                response_shaping_rate.raise_for_status()  # Check for HTTP errors
                                output_shaping_rate = response_shaping_rate.json()
                                ne_response_shaping_rate = output_shaping_rate.get('ne_response', '')

                                combined_ne_response += ne_response_shaping_rate + "\n\n"  # Add response to combined response

                                # Extract Shaping-rate
                                shaping_rate_match = re.search(r'shaping-rate (.+)', ne_response_shaping_rate)
                                if shaping_rate_match:
                                    combined_data_response["Shaping-rate"] = shaping_rate_match.group(1)

                        bandwidth_subscription.update({'status': 'success', 'data': combined_data_response, 'ne_response': combined_ne_response})

                    except requests.exceptions.RequestException as e:
                        bandwidth_subscription.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(bandwidth_subscription)

                # Ping
                if serialized_data['service_testing']['ping_test_pe_ce']:

                    ping_test_pe_ce = {
                        'ping_test_pe_ce': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    ping_test_pe_ce['ping_test_pe_ce'] = True

                    try:

                        command = f'ping {serialized_data["ce_ip"].split("/")[0]} source-address {serialized_data["pe_ip"].split("/")[0]} count 10'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()
                        ne_response = output.get('ne_response', '')

                        # Check for syntax error
                        if "syntax error" in ne_response:

                            # Run alternative command with source-address
                            command = f'ping {serialized_data["ce_ip"].split("/")[0]} source {serialized_data["pe_ip"].split("/")[0]} count 10'

                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()
                            ne_response = output.get('ne_response', '')

                        # Extract packet loss percentage using regex
                        match = re.search(r'(\d+\.?\d*)% packet loss', ne_response)
                        if match:
                            packet_loss = match.group(1)
                        else:
                            packet_loss = 'unknown'

                        ping_test_pe_ce.update({'status': 'success', 'data': f'{packet_loss}% packet loss', 'ne_response': ne_response})

                    except Exception as e:

                        ping_test_pe_ce.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(ping_test_pe_ce)

                # Traceroute
                if serialized_data['service_testing']['traceroute']:

                    # Traceroute Check
                    traceroute = {
                        'traceroute': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    traceroute_results = []
                    combined_ne_response = ""

                    for ip in serialized_data.get('traceroute_lan_ip', []):
                        traceroute = {
                            'traceroute': True,
                            'status': 'not_checked',
                            'data': None,
                            'ne_response': 'n/a'
                        }

                        try:
                            command = f'traceroute {ip}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()

                            # traceroute.update({'status': 'success', 'data': 'n/a', 'ne_response': output.get('ne_response', 'n/a')})
                            traceroute.update({'status': 'success', 'ne_response': output.get('ne_response', 'n/a')})

                            # Append ne_response to combined_ne_response
                            combined_ne_response += traceroute['ne_response'] + "\n\n"

                        except requests.exceptions.RequestException as e:
                            traceroute.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                        traceroute_results.append(traceroute)

                    # Construct the final response
                    final_response = {
                        'traceroute': True,
                        'status': 'success',  # Assuming all traceroutes are successful
                        'data': 'n/a',
                        'ne_response': combined_ne_response
                    }

                    # Append final_response to combined_response
                    combined_response.append(final_response)

                # Ping IP
                if serialized_data['service_testing']['ping_lan_ip']:

                    ping_lan_ip_results = []
                    combined_ne_response = ""

                    for ip in serialized_data.get('ping_lan_ip', []):
                        ping_lan_ip = {
                            'ping_lan_ip': True,
                            'status': 'not_checked',
                            'data': 'n/a',
                            'ne_response': 'n/a'
                        }

                        try:
                            command = f'ping {ip} source {serialized_data["pe_ip"].split("/")[0]} count 10'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()

                            ne_response = output.get('ne_response', '')

                            # Extract packet loss percentage using regex
                            match = re.search(r'(\d+\.?\d*)% packet loss', ne_response)
                            if match:
                                packet_loss = match.group(1)
                            else:
                                packet_loss = 'unknown'

                            ping_lan_ip.update({'status': 'success', 'data': f'{packet_loss}% packet loss', 'ne_response': ne_response})

                            # Append ne_response to combined_ne_response
                            combined_ne_response += ping_lan_ip['ne_response'] + "\n\n"

                        except requests.exceptions.RequestException as e:
                            ping_lan_ip.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                        ping_lan_ip_results.append(ping_lan_ip)

                    # Construct the final response
                    final_response = {
                        'ping_lan_ip': True,
                        'status': 'success',  # Assuming all ping_lan_ip are successful
                        'data': 'n/a',
                        'ne_response': combined_ne_response
                    }

                    # Append final_response to combined_response
                    combined_response.append(final_response)
 
                # Incoming Route
                if serialized_data['service_testing']['incoming_route']:

                    # Incoming Route Check
                    incoming_route = {
                        'incoming_route': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    incoming_route['incoming_route'] = True
                    # Define a regular expression pattern to match the desired information
                    pattern = re.compile(r'^\*\s+([\d./]+)\s+([\d.]+)\s+(\d+)\s+(\d+)\s+(.+)$')

                    try:

                        start_extraction = False  # Initialize start_extraction here

                        command = f'show route receive-protocol bgp {serialized_data["ce_ip"].split("/")[0]}'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()

                        # Inside the try block
                        # ne_response = json.dumps(output)  # Convert the dictionary output to a JSON string
                        # lines = ne_response.split('\r\n')
                        # power_data = []

                        # # Iterate over each line in the output
                        # for line in lines:
                        #     if start_extraction:
                        #         match = pattern.match(line)
                        #         if match:
                        #             # Extract the desired information and append it to power_data
                        #             prefix, nexthop, med, lclpref, as_path = match.groups()
                        #             power_data.append({
                        #                 'Prefix': prefix,
                        #                 'Nexthop': nexthop,
                        #                 'MED': int(med),
                        #                 'Lclpref': int(lclpref),
                        #                 'AS path': as_path
                        #             })
                        #     elif 'Prefix                  Nexthop              MED     Lclpref    AS path' in line:
                        #         start_extraction = True

                        incoming_route.update({'status': 'success', 'data': 'n/a', 'ne_response': output.get('ne_response', '')})

                    except requests.exceptions.RequestException as e:
                        incoming_route.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(incoming_route)

                # Advertise Route
                if serialized_data['service_testing']['advertise_route']:

                    advertise_route_results = []
                    combined_ne_response = ""

                    for ip in serialized_data.get('advertise_route_lan_ip', []):
                        advertise_route = {
                            'advertise_route': True,
                            'status': 'not_checked',
                            'data': 'n/a',
                            'ne_response': 'n/a'
                        }

                        try:
                            command = f'show route advertising-protocol bgp {serialized_data["ce_ip"].split("/")[0]} match-prefix {ip}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()

                            advertise_route.update({'status': 'success', 'ne_response': output.get('ne_response', 'n/a')})

                            # Append ne_response to combined_ne_response
                            combined_ne_response += advertise_route['ne_response'] + "\n\n"

                        except requests.exceptions.RequestException as e:
                            advertise_route.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                        advertise_route_results.append(advertise_route)

                    # Construct the final response
                    final_response = {
                        'advertise_route': True,
                        'status': 'success',  # Assuming all advertise_route_lan_ip are successful
                        'data': 'n/a',
                        'ne_response': combined_ne_response
                    }

                    # Append final_response to combined_response
                    combined_response.append(final_response)

                # Route Summary
                if serialized_data['service_testing']['route_summary']:

                    # Route summary Check
                    route_summary = {
                        'route_summary': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    route_summary['route_summary'] = True

                    try:
                        command = f'show bgp neighbor {serialized_data["ce_ip"].split("/")[0]} | match prefix'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()

                        route_summary.update({'status': 'success', 'data': 'n/a', 'ne_response': output.get('ne_response', '')})

                    except requests.exceptions.RequestException as e:
                        route_summary.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(route_summary)

                # IP Prefix Register
                if serialized_data['service_testing']['ip_prefix_register']:
                    # IP Prefix Register Check
                    ip_prefix_register = {
                        'ip_prefix_register': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    ip_prefix_register['ip_prefix_register'] = True

                    try:

                        combined_ne_response = ""

                        command = f'show configuration | match {serialized_data["ce_ip"].split("/")[0]} | match IP_TRANSIT | display set'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()
                        ne_response = output.get('ne_response', '')
                        combined_ne_response += ne_response + "\n\n"  # Add response to combined response

                        # Extract policy name from the first command's output
                        match = re.search(r'import (\S+)', ne_response)
                        if match:
                            policy_name = match.group(1)
                        else:
                            policy_name = None

                        # If the policy name was found, execute the second command
                        if policy_name:

                            command = f'show configuration policy-options policy-statement {policy_name}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()

                            # Assuming the relevant output is in output['ne_response']
                            ne_response = output.get('ne_response', '')

                            combined_ne_response += ne_response + "\n\n"  # Add response to combined response
                            
                            ip_prefix_register.update({'status': 'success', 'data': 'n/a', 'ne_response': combined_ne_response})

                        else:
                            
                            ip_prefix_register.update({'status': 'failed', 'data': 'Policy name not found in the first command output', 'ne_response': combined_ne_response})

                    except requests.exceptions.RequestException as e:
                        ip_prefix_register.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(ip_prefix_register)

                # BGP Status
                if serialized_data['service_testing']['bgp_status']:

                    bgp_status = {
                        'bgp_status': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    bgp_status['bgp_status'] = True

                    try:
                        command = f'show bgp summary | match {serialized_data["ce_ip"].split("/")[0]}'

                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()  # Check for HTTP errors
                        output = response.json()
                        ne_response = output.get('ne_response', '')

                        # Extract the BGP session state using regex
                        match = re.search(r'\b(Establ|Established|Active|Open)\b', ne_response)
                        bgp_state = match.group(0) if match else 'unknown'

                        # Update the bgp_status dictionary
                        bgp_status.update({'status': 'success', 'data': bgp_state, 'ne_response': ne_response})

                    except requests.exceptions.RequestException as e:
                        bgp_status.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    combined_response.append(bgp_status)


                # Show Routing Table
                if serialized_data['service_testing']['show_routing_table']:

                    show_routing_table_results = []
                    combined_ne_response = ""

                    for ip in serialized_data.get('show_routing_table_lan_ip', []):
                        show_routing_table = {
                            'show_routing_table': True,
                            'status': 'not_checked',
                            'data': 'n/a',
                            'ne_response': 'n/a'
                        }

                        try:
                            command = f'show route {ip}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()

                            show_routing_table.update({'status': 'success', 'ne_response': output.get('ne_response', 'n/a')})

                            # Append ne_response to combined_ne_response
                            combined_ne_response += show_routing_table['ne_response'] + "\n\n"

                        except requests.exceptions.RequestException as e:
                            show_routing_table.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                        show_routing_table_results.append(show_routing_table)

                    # Construct the final response
                    final_response = {
                        'show_routing_table': True,
                        'status': 'success',  # Assuming all show_routing_table_lan_ip are successful
                        'data': 'n/a',
                        'ne_response': combined_ne_response
                    }

                    # Append final_response to combined_response
                    combined_response.append(final_response)

                # NID Loopback Checking
                if serialized_data['service_testing']['nid_loopback_checking']:

                    nid_loopback_checking = {
                        'nid_loopback_checking': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    nid_loopback_checking['nid_loopback_checking'] = True

                    if serialized_data['nid_model'] != '' and serialized_data['nid_device_id'] != '':

                        if serialized_data['nid_model'] == 'RAISECOM':

                            try:
                                command = f'show interface nni 1 loopback'

                                url_pe = f"http://pisa-raisecom-svc/raisecom/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                                response = requests.get(url_pe, verify=False)
                                response.raise_for_status()  # Check for HTTP errors
                                output = response.json()
                                ne_response = output.get('ne_response', '')

                                output = {
                                    "loopback_status": None,
                                    "loopback_rule": None
                                }

                                # Regular expressions to match the desired values in the response
                                loopback_status_pattern = re.compile(r"Loopback status:\s+(\d+)")
                                loopback_rule_pattern = re.compile(r"Loopback rule:\s+(\d+)")

                                # Extract the Loopback Status
                                loopback_status_match = loopback_status_pattern.search(ne_response)
                                if loopback_status_match:
                                    output["loopback_status"] = loopback_status_match.group(1)

                                # Extract the loopback rule
                                loopback_rule_match = loopback_rule_pattern.search(ne_response)
                                if loopback_rule_match:
                                    output["loopback_rule"] = loopback_rule_match.group(1)

                                # Update the nid_loopback_checking dictionary
                                nid_loopback_checking.update({'status': 'success', 'data': output, 'ne_response': ne_response})

                            except requests.exceptions.RequestException as e:
                                nid_loopback_checking.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})


                        elif serialized_data['nid_model'] == 'HFR':

                            try:

                                command = f'show ethernet sat loopback'

                                url_pe = f"http://pisa-hfr-svc/hfr/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                                response = requests.get(url_pe, verify=False)
                                response.raise_for_status()  # Check for HTTP errors
                                output = response.json()
                                ne_response = output.get('ne_response', '')

                                output = {
                                    "loopback_status": None
                                }

                                # Regular expressions to match the desired values in the response
                                loopback_status_pattern = re.compile(r"LOOPBACK Status:\s+(\d+)")

                                # Extract the Loopback Status
                                loopback_status_match = loopback_status_pattern.search(ne_response)
                                if loopback_status_match:
                                    output["loopback_status"] = loopback_status_match.group(1)

                                # Update the nid_loopback_checking dictionary
                                nid_loopback_checking.update({'status': 'success', 'data': output, 'ne_response': ne_response})

                            except requests.exceptions.RequestException as e:
                                nid_loopback_checking.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                        else:

                            nid_loopback_checking.update({'status': 'failed', 'data': 'Invalid NID model (Only Raisecom and HFR)', 'ne_response': 'n/a'})

                    else:

                        nid_loopback_checking.update({'status': 'failed', 'data': 'Incomplete/invalid information fetched by API for NID Testing', 'ne_response': 'n/a'})

                    
                    combined_response.append(nid_loopback_checking)


                # L2VPN MPLS Ping (Domestic IGW)
                # if serialized_data['service_testing']['l2vpn_mpls_ping_domestic_igw']:




                return Response({'status': 'success', 'data': combined_response}, status=status.HTTP_200_OK)

            else:
                api_response['status'] = 'failed'
                api_response['message'] = 'Network type or vendor is not supported'
                api_response['data'] = 'n/a'
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)


            # api_details = []
            # for command in serialized_data['command']:

            #     if serialized_data['network_type'] == 'pe' and serialized_data['vendor'] == 'juniper':

            #         url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
            #         api_details.append({
            #             "url": url_pe,
            #         })

            # # create a thread pool executor with 5 worker threads
            # executor = ThreadPoolExecutor(max_workers=2)

            # # submit the requests to the executor and get the results
            # results = list(executor.map(self.invoke_api, api_details))

            # # Initialize a list to store the responses
            # responses = []

            # for result, command in zip(results, serialized_data['command']):
            #     response = {
            #         'command': command,
            #         'status': result.get('status', 'unknown'),
            #         'ne_response': result.get('ne_response', '')
            #     }
            #     responses.append(response)

            # # Check the status of each individual response
            # success_statuses = [response['status'] == 'success' for response in responses]

            # # Initialize an API response dictionary
            # api_response = {}

            # # Check if all responses have 'success' status
            # if all(success_statuses):
            #     output_message = 'Successfully retrieve information from network element'
            #     api_response['status'] = 'success'
            #     api_response['message'] = output_message
            #     api_response['data'] = {
            #         'responses': responses
            #     }
            #     return Response(api_response, status=status.HTTP_200_OK)
            # else:
            #     # If any response has a status other than 'success'
            #     error_message = 'Failed to retrieve information from network element'
            #     api_response['status'] = 'error'
            #     api_response['message'] = error_message
            #     # Include more detailed error information if needed
            #     api_response['data'] = {
            #         'responses': responses
            #     }
            #     return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            # log_text(filename=log_file, content=output_message)
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            api_response['data'] = output_error
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)


    # define a function to invoke API
    def invoke_api(self, info):
        response = requests.get(
            info['url'], 
            # data=info['payload'], 
            verify=False, 
            # headers={ 'Authorization': info['token'] }
        )
        return response.json()


