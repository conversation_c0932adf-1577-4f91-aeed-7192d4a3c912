from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import L2L3SelfServeSerializer
import requests, sys
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps
from concurrent.futures import ThreadPoolExecutor


class API(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        request_body=L2L3SelfServeSerializer,
        responses={201: L2L3SelfServeSerializer()},
        operation_summary="Workflow for Self Serve Application for both L2 & L3 NE"
    )

    def post(self, request):
        try:
            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = L2L3SelfServeSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            
             # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data

            api_details = list()

            for command in serialized_data['commands']:
                
                if serialized_data['network_type'] == 'pe' and serialized_data['vendor'] == 'juniper':
                    url = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                
                elif serialized_data['network_type'] == 'ce' and serialized_data['vendor'] == 'juniper':
                    url = f"http://pisa-juniper-svc/juniper/ce/noproxy/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                elif serialized_data['network_type'] == 'ce' and serialized_data['vendor'] == 'cisco':
                    url = f"http://pisa-cisco-svc/cisco/ce/v1/show_config/?ce_ip={serialized_data['ne_ip']}&command={command}"

                elif serialized_data['network_type'] == 'nid' and serialized_data['vendor'] == 'raisecom':
                    url = f"http://pisa-raisecom-svc/raisecom/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                elif serialized_data['network_type'] == 'nid' and serialized_data['vendor'] == 'hfr':
                    url = f"http://pisa-hfr-svc/hfr/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                elif (serialized_data['network_type'] == 'npe' or serialized_data['network_type'] == 'epe') and serialized_data['vendor'] == 'alcatel-lucent':
                    url = f"http://pisa-nokia-svc/nokia/npe_epe/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                elif (serialized_data['network_type'] == 'npe' or serialized_data['network_type'] == 'epe') and serialized_data['vendor'] == 'huawei':
                    url = f"http://pisa-huawei-svc/huawei/npe_epe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                elif (serialized_data['network_type'] == 'npe' or serialized_data['network_type'] == 'epe') and serialized_data['vendor'] == 'zte':
                    url = f"http://pisa-zte-svc/zte/npe_epe/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                api_details.append({
                    "url": url,
                })

            # create a thread pool executor with 5 worker threads
            executor = ThreadPoolExecutor(max_workers=len(api_details))

            # submit the requests to the executor and get the results
            results = list(executor.map(self.invoke_api, api_details))

            # Initialize a list to store the responses
            responses = []

            for result, command in zip(results, serialized_data['commands']):
                response = {
                    'command': command,
                    'status': result.get('status', 'unknown'),
                    'ne_response': result.get('ne_response', '')
                }
                responses.append(response)

            # Check the status of each individual response
            success_statuses = [response['status'] == 'success' for response in responses]

            # Initialize an API response dictionary
            api_response = {}

            # Check if all responses have 'success' status
            if all(success_statuses):
                output_message = 'Successfully retrieved configurations from network element'
                api_response['status'] = 'success'
                api_response['message'] = output_message
                api_response['data'] = responses
                return Response(api_response, status=status.HTTP_200_OK)
            
            else:
                # If any response has a status other than 'success'
                error_message = 'Failed to retrieve configurations from network element'
                api_response['status'] = 'error'
                api_response['message'] = error_message
                # Include more detailed error information if needed
                api_response['data'] = responses
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            # log_text(filename=log_file, content=output_message)
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            api_response['data'] = output_error
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)


    # define a function to invoke API
    def invoke_api(self, info):
        response = requests.get(
            info['url'], 
            # data=info['payload'], 
            verify=False, 
            # headers={ 'Authorization': info['token'] }
        )
        return response.json()