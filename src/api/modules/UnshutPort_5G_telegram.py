from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.serializers import UnShutPort5GSerializer
import requests, time, re, sys, os
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from api.libs import log_text, log_database
from json import dumps
from dotenv import load_dotenv          # to retrieve data from .env file
load_dotenv()



class API(APIView):
	
	authentication_classes = ()  # exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		request_body=UnShutPort5GSerializer,
		responses={201: UnShutPort5GSerializer()},
		operation_summary="Workflow for 5G UnShut Port at AGG"
	)
	def post(self, request):

		try:
			# GET USER ID
			token = request.META.get('HTTP_AUTHORIZATION')
			if token:
				user_id = Token.objects.get(
					key=token.replace('Token ', '')).user_id
				user = User.objects.get(id=user_id).username
			else:
				user = 'not_available'

			#                                           #
			#              VERIFY API INPUT             #
			#                                           #
			input_data = UnShutPort5GSerializer(data=request.data)
			input_data.is_valid()

			# initiate variable
			api_response = dict()

			if len(input_data.errors) > 0:  # check if passed data contains error and return error
				output_message = 'Incomplete/invalid information fetched by API'
				DB_data = {
					'order_type': 'Un-shut Interface',
					'service_type': '5G',
					'input_details': request.data,
					'message': output_message,
					'api_output': str(input_data.errors),
					'status': 'failed',
					'log_file': 'N/A',
					'config_file': 'undefined',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = input_data.errors
				api_response['log_file'] = ''
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			# assign a variable with serialized data
			serialized_data = input_data.validated_data
			date_now = datetime.now().strftime('%d-%m-%Y')
			time_now = datetime.now().strftime('%H:%M:%S')
			log_file = f"5G_UnShutInterface_{serialized_data['agg_hostname'].replace('.', '')}_{serialized_data['interface'].replace('/', '_')}_{date_now}@{time_now}_by_{user}.txt"		# Create Log File

			# LOG CREATED TIME #
			output_message = "File created at {} {}".format(date_now, time_now)
			log_text(filename=log_file, content=output_message)

			output_message = 'Information fetched by API contains no error'
			log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

			#                                              #
			#              Get Token IPSDNC                #
			#                                              #

			url_ipsdnc = 'https://**********:26335/'

			payload = {
				'grantType': 'password',
				'userName': os.getenv('IPSDNC_USERNAME'),
				'value': os.getenv('IPSDNC_PASSWORD')
			}
			response = requests.put(url_ipsdnc+'rest/plat/smapp/v1/oauth/token', json=payload, headers={'Content-Type': 'application/json'}, verify=False)
			output = response.json()

			if response.status_code == 200 :
				token = output['accessSession']
				output_message = f"Successfully authenticated to IPSDNC server to request token ({token})"
				# log_text(filename=log_file, content=output_message, ne_output=dumps(output, indent=4, separators=(',', ': ')))
				log_text(filename=log_file, content=output_message)
			else:
				error_message = f"Failed to authenticate to IPSDNC server to request token"
				output_message = error_message + \
					'\n[error message: {}]'.format(output)
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Un-shut Interface',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output),
					'status': 'failed',
					'log_file': log_file,
					'config_file': 'undefined',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
			
			#                                                 #
			#              Get AGG Loopback IP                #
			#                                                 #

			payload = {
				'huawei-nce-resource-inventory-aoc-devicemgr:input' : {
					'offset' : 0,
					'limit' : 10,
					'ascend' : True,
					'sort-column' : 'ip',
					'filter-conditions' : [
						{
							'filter-name' : 'device-name',
							'comparison-operator' : 'like',
							'filter-value' : serialized_data['agg_hostname']
						}
					]
				}
			}
			headers = {
				'Content-Type': 'application/json',
				'Accept' : 'application/json',
				'accessSession' : token,
			}
			response = requests.post(url_ipsdnc+'restconf/operations/huawei-nce-resource-inventory-aoc-devicemgr:query-devices', json=payload, headers=headers, verify=False)
			output = response.json()

			if response.status_code == 200 :
				result = output['huawei-nce-resource-inventory-aoc-devicemgr:output']
				if result['total-num'] == 0:
					output_message = f"Not matching {serialized_data['agg_hostname']} in IPSDNC database"
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type': 'Un-shut Interface',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': str(output),
						'status': 'failed',
						'log_file': log_file,
						'config_file': 'undefined',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
				else:
					# determine index of matching output ie agg hostname
					for i in range(len(result['devices'])):
						if re.search(f"{serialized_data['agg_hostname']}$", result['devices'][i]['device-name'], re.IGNORECASE):
							index_agg_name = i
							break
					
					serialized_data['agg_loopback_ip'] = result['devices'][index_agg_name]['device-ip']
					serialized_data['agg_vendor'] = result['devices'][index_agg_name]['manufacturer']
					output_message = f"Successfully retrieved {serialized_data['agg_hostname']}'s loopback IP ({serialized_data['agg_loopback_ip']}) and vendor ({serialized_data['agg_vendor']})"
					log_text(filename=log_file, content=output_message, ne_output=dumps(result['devices'][index_agg_name], indent=4, separators=(',', ': ')))
			else:
				error_message = f"Failed to retrieve AGG ({serialized_data['agg_hostname']})'s loopback IP"
				output_message = error_message + \
					'\n[error message: {}]'.format(output)
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Un-shut Interface',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output),
					'status': 'failed',
					'log_file': log_file,
					'config_file': 'undefined',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
			
			#                                                 #
			#              Check Vendor is ZTE                #
			#                                                 #

			if not re.search('zte', serialized_data['agg_vendor'], re.IGNORECASE):
				output_message = "Only vendor ZTE is allowed"
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Un-shut Interface',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': output_message,
					'api_output': output_message,
					'status': 'failed',
					'log_file': log_file,
					'config_file': 'undefined',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = output_message
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
			
			# declare main url variable
			url = 'http://pisa-zte-svc/zte/agg/'
		
			#                                                                      #
			#              Pre-verification Interface Status (Down)                #
			#                                                                      #

			payload = {
				'ne_ip': serialized_data['agg_loopback_ip'],
				'filter': serialized_data['interface']
			}

			response = requests.get(url+'v1/show_interface_brief', params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = f"An error has occured while retrieving interface {serialized_data['interface']} status"
				output_message = error_message + \
					'\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Un-shut Interface',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output['output']),
					'status': 'failed',
					'log_file': log_file,
					'config_file': 'undefined',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			else:
				if output['output'] == "There is no output returned from NE":
					output_message = f"There is no interface {serialized_data['interface']} status returned from NE"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {
						'order_type': 'Un-shut Interface',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': output_message,
						'status': 'failed',
						'log_file': log_file,
						'config_file': 'undefined',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

				else:
					# determine index of matching output ie interface
					isDown = None
					for i in range(len(output['output'])):
						if re.search(f"{serialized_data['interface']}$", output['output'][i]['interface'], re.IGNORECASE):
							if output['output'][i]['admin'] == 'down' and output['output'][i]['phy'] == 'down' and output['output'][i]['prot'] == 'down':
								isDown = True
							# elif output['output'][i]['admin'] == 'down' and output['output'][i]['phy'] == 'up' and output['output'][i]['prot'] == 'down':
							# 	isDown = 'check'
							# elif output['output'][i]['admin'] == 'down' and output['output'][i]['phy'] == 'up' and output['output'][i]['prot'] == 'up':
							# 	isDown = 'check'
							elif output['output'][i]['admin'] == 'up' and output['output'][i]['phy'] == 'up' and output['output'][i]['prot'] == 'down':
								isDown = 'check'
							elif output['output'][i]['admin'] == 'up' and output['output'][i]['phy'] == 'down' and output['output'][i]['prot'] == 'down':
								isDown = False
							elif output['output'][i]['admin'] == 'up' and output['output'][i]['phy'] == 'up' and output['output'][i]['prot'] == 'up':
								isDown = 'check'
							index_interface = i
							break

					output_message = f"The interface {serialized_data['interface']} status are {output['output'][index_interface]['admin']} (admin), {output['output'][index_interface]['phy']} (physical) and {output['output'][index_interface]['prot']} (protocol)"

					if isDown:
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

					elif not isDown:
						output_message = f"{output_message}. Interface is already up"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type': 'Un-shut Interface',
							'service_type': '5G',
							'input_details': serialized_data,
							'message': output_message,
							'api_output': output_message,
							'status': 'failed',
							'log_file': log_file,
							'config_file': 'undefined',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = output_message
						api_response['log_file'] = log_file
						log_database(data_DB=DB_data)
						return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

					else:
						output_message = f"{output_message}. Please check physical connection"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type': 'Un-shut Interface',
							'service_type': '5G',
							'input_details': serialized_data,
							'message': output_message,
							'api_output': output_message,
							'status': 'failed',
							'log_file': log_file,
							'config_file': 'undefined',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = output_message
						api_response['log_file'] = log_file
						log_database(data_DB=DB_data)
						return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			#                                           #
			#          Undo Shutdown Interface          #
			#                                           #
			payload = {
				'ne_ip': serialized_data['agg_loopback_ip'],
				'interface': serialized_data['interface']
			}

			response = requests.post(
				url+'v1/unshut_interface/', data=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = f"An error has occured while trying to unshut the interface ({serialized_data['interface']})"
				output_message = error_message + \
					'\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'TBU',
					'service_type': '1GOV',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output['output']),
					'status': 'failed',
					'log_file': log_file,
					'config_file': 'undefined',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			else:
				if output['output'] != 'Successfully unshut the interface':
					output_message = f"Failed to unshut the interface ({serialized_data['interface']})"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {
						'order_type': 'Un-shut Interface',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': output_message,
						'status': 'failed',
						'log_file': log_file,
						'config_file': 'undefined',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
				
				else:
					output_message = f"Successfully unshut the interface ({serialized_data['interface']})"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

			#                                                                     #
			#              Post-verification Interface Status (Up)                #
			#                                                                     #

			payload = {
				'ne_ip': serialized_data['agg_loopback_ip'],
				'filter': serialized_data['interface']
			}

			response = requests.get(url+'v1/show_interface_brief', params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = f"An error has occured while retrieving interface {serialized_data['interface']} status"
				output_message = error_message + \
					'\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Un-shut Interface',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output['output']),
					'status': 'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			else:
				if output['output'] == "There is no output returned from NE":
					output_message = f"There is no interface {serialized_data['interface']} status returned from NE"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					DB_data = {
						'order_type': 'Un-shut Interface',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': output_message,
						'status': 'failed',
						'log_file': log_file,
						'config_file': 'undefined',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

				else:
					# determine index of matching output ie interface
					isUp = False
					for i in range(len(output['output'])):
						if re.search(f"{serialized_data['interface']}$", output['output'][i]['interface'], re.IGNORECASE):
							if output['output'][i]['admin'] == 'up':
								isUp = True
							index_interface = i
							break

					output_message = f"The admin status for interface {serialized_data['interface']} is {output['output'][index_interface]['admin']} (post-verification)"

					if isUp:
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

					else:
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type': 'Un-shut Interface',
							'service_type': '5G',
							'input_details': serialized_data,
							'message': output_message,
							'api_output': output_message,
							'status': 'failed',
							'log_file': log_file,
							'config_file': 'undefined',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = output_message
						api_response['log_file'] = log_file
						log_database(data_DB=DB_data)
						return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
					
			#                                                #
			#              OUTPUT FINAL MESSAGE              #
			#                                                #
			output_message = f"Successfully undo shutdown and turn on laser for interface {serialized_data['interface']} at {serialized_data['agg_hostname']} and run post-verification command"
			# log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
			DB_data = {
				'order_type': 'Un-shut Interface',
				'service_type': '5G',
				'input_details': serialized_data,
				'message': output_message,
				'api_output': 'No error',
				'status': 'success',
				'log_file': log_file,
				'config_file': 'undefined',
				'user': user
			}
			api_response['status'] = 'success'
			api_response['message'] = output_message
			api_response['data'] = 'No error'
			api_response['log_file'] = log_file
			log_database(data_DB=DB_data)
			return Response(api_response, status=status.HTTP_200_OK)

		except Exception as error:
			output_error = list()
			exc_type, exc_obj, exc_tb = sys.exc_info()
			output_error.append(str(exc_type))
			output_error.append(str(exc_obj))
			output_error.append(str(exc_tb.tb_lineno))
			error_message = 'Unexpected error has occured while executing API'
			output_message = error_message + \
				'\n[error message: {}]'.format(error)
			log_text(filename=log_file, content=output_message)
			DB_data = {
				'order_type': 'Un-shut Interface',
				'service_type': '5G',
				'input_details': request.data,
				'message': error_message,
				'api_output': str(error),
				'status': 'failed',
				'log_file': log_file,
				'config_file': 'undefined',
				'user': user
			}
			api_response['status'] = 'failed'
			api_response['message'] = error_message
			# api_response['data'] = str(error)
			api_response['data'] = output_error
			if 'log_file' in locals():
				api_response['log_file'] = log_file
			else:
				api_response['log_file'] = ''
			log_database(data_DB=DB_data)
			return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
					



