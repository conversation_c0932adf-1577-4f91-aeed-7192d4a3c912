from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.serializers import SelfVerificationBot5GSerializer
import requests, time, re, sys, os
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from api.libs import log_text, log_database
from json import dumps
from dotenv import load_dotenv          # to retrieve data from .env file
load_dotenv()



class API(APIView):
	
	authentication_classes = ()  # exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		request_body=SelfVerificationBot5GSerializer,
		responses={201: SelfVerificationBot5GSerializer()},
		operation_summary="Workflow for 5G Self Verification Bot for Telegram Bot"
	)
	def post(self, request):

		try:
			# GET USER ID
			token = request.META.get('HTTP_AUTHORIZATION')
			if token:
				user_id = Token.objects.get(
					key=token.replace('Token ', '')).user_id
				user = User.objects.get(id=user_id).username
			else:
				user = 'not_available'

			#                                           #
			#              VERIFY API INPUT             #
			#                                           #
			input_data = SelfVerificationBot5GSerializer(data=request.data)
			input_data.is_valid()

			# initiate variable
			api_response = dict()

			if len(input_data.errors) > 0:  # check if passed data contains error and return error
				output_message = 'Incomplete/invalid information fetched by API'
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': request.data,
					'message': output_message,
					'api_output': str(input_data.errors),
					'status': 'failed',
					'log_file': 'N/A',
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = input_data.errors
				api_response['log_file'] = ''
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			# serialized_data = input_data.data[]   # assign a variable with serialized data
			serialized_data = input_data.validated_data
			date_now = datetime.now().strftime('%d-%m-%Y')
			time_now = datetime.now().strftime('%H:%M:%S')
			log_file = f"5G_NewVerificationBot_{serialized_data['agg_hostname'].replace('.', '')}_{serialized_data['csr_hostname'].replace('.', '')}_{date_now}@{time_now}_by_{user}.txt"		# Create Log File

			# LOG CREATED TIME #
			output_message = "File created at {} {} for 5G New Site Verification Bot Log".format(date_now, time_now)
			log_text(filename=log_file, content=output_message)

			output_message = 'Information fetched by API contains no error'
			log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

			# for purpose of displaying the info in ipgenv2
			serialized_data['service_id'] = serialized_data['site_id'].upper()

			# Determine csr vendor
			if re.search('zt', serialized_data['csr_hostname'], re.IGNORECASE):
				serialized_data['csr_vendor'] = 'ZTE'
			else:
				serialized_data['csr_vendor'] = 'Huawei'


			# Testing data (Huawei)
			# agghw21.lab - ************ (25GE2/0/15) <> csrhw21.lab - ************* (GigabitEthernet0/2/1)

			# 1 + 0
			# {
			# "site_id": "DMAGJ0052_TMIBUSAWATALORGAJAH",
			# "agg_hostname": "AGGHW41.AG",
			# "agg_port_main": "25GE3/0/11",
			# "agg_wan_ip": "************",
			# "csr_hostname": "CSRHW41.DMAGJ0052",
			# "csr_loopback_ip": "*************",
			# "csr_wan_ip": "************"
			# }

			# 1 + 1
			# {
			# "site_id": "DMAGJ0052_TMIBUSAWATALORGAJAH",
			# "agg_hostname": "AGGHW41.AG",
			# "agg_port_main": "25GE3/0/11",
			# "agg_port_protect": "25GE4/0/11",
			# "agg_wan_ip": "************",
			# "csr_hostname": "CSRHW41.DMAGJ0052",
			# "csr_loopback_ip": "*************",
			# "csr_wan_ip": "************"
			# }

			# Testing data (ZTE)
			# agghw21.lab - ************ <> csrzt21.lab - *************

			# 1 + 0
			# {
			# "site_id": "DJKLU0850_57JLNHAJIABDULAZIZAWAB",
			# "agg_hostname": "AGGZT41.KU",
			# "agg_port_main": "xgei-0/2/0/15",
			# "agg_port_protect": "xgei-0/3/0/15",
			# "agg_wan_ip": "************",
			# "csr_hostname": "CSRZT41.DJKLU0850",
			# "csr_loopback_ip": "************",
			# "csr_wan_ip": "************"
			# }

			#                                              #
			#              Get Token IPSDNC                #
			#                                              #

			url_ipsdnc = 'https://**********:26335/'

			payload = {
				'grantType': 'password',
				'userName': os.getenv('IPSDNC_USERNAME'),
				'value': os.getenv('IPSDNC_PASSWORD')
			}
			response = requests.put(url_ipsdnc+'rest/plat/smapp/v1/oauth/token', json=payload, headers={'Content-Type': 'application/json'}, verify=False)
			output = response.json()

			if response.status_code == 200 :
				token = output['accessSession']
				output_message = f"Successfully authenticated to IPSDNC server to request token ({token})"
				# log_text(filename=log_file, content=output_message, ne_output=dumps(output, indent=4, separators=(',', ': ')))
				log_text(filename=log_file, content=output_message)
			else:
				error_message = f"Failed to authenticate to IPSDNC server to request token"
				output_message = error_message + \
					'\n[error message: {}]'.format(output)
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output),
					'status': 'failed',
					'log_file': log_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
			
			#                                                 #
			#              Get AGG Loopback IP                #
			#                                                 #

			payload = {
				'huawei-nce-resource-inventory-aoc-devicemgr:input' : {
					'offset' : 0,
					'limit' : 10,
					'ascend' : True,
					'sort-column' : 'ip',
					'filter-conditions' : [
						{
							'filter-name' : 'device-name',
							'comparison-operator' : 'like',
							'filter-value' : serialized_data['agg_hostname']
						}
					]
				}
			}
			headers = {
				'Content-Type': 'application/json',
				'Accept' : 'application/json',
				'accessSession' : token,
			}
			response = requests.post(url_ipsdnc+'restconf/operations/huawei-nce-resource-inventory-aoc-devicemgr:query-devices', json=payload, headers=headers, verify=False)
			output = response.json()

			if response.status_code == 200 :
				result = output['huawei-nce-resource-inventory-aoc-devicemgr:output']
				if result['total-num'] == 0:
					output_message = f"Not matching {serialized_data['agg_hostname']} in IPSDNC database"
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type': 'Verification',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': str(output),
						'status': 'failed',
						'log_file': log_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
				else:
					# determine index of matching output ie agg hostname
					for i in range(len(result['devices'])):
						if re.search(f"{serialized_data['agg_hostname']}$", result['devices'][i]['device-name'], re.IGNORECASE):
							index_agg_name = i
							break
					
					serialized_data['agg_loopback_ip'] = result['devices'][index_agg_name]['device-ip']
					serialized_data['agg_vendor'] = result['devices'][index_agg_name]['manufacturer']
					output_message = f"Successfully retrieved {serialized_data['agg_hostname']}'s loopback IP ({serialized_data['agg_loopback_ip']}) and vendor ({serialized_data['agg_vendor']})"
					log_text(filename=log_file, content=output_message, ne_output=dumps(result['devices'][index_agg_name], indent=4, separators=(',', ': ')))
			else:
				error_message = f"Failed to retrieve AGG ({serialized_data['agg_hostname']})'s loopback IP"
				output_message = error_message + \
					'\n[error message: {}]'.format(output)
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output),
					'status': 'failed',
					'log_file': log_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
			

			# for final API output
			final_output, agg_output, api_output, csr_output = list(), dict(), dict(), dict()

			#                                                  #
			#              LOGIC FOR AGG Huawei                #
			#                                                  #

			if re.search('huawei', serialized_data['agg_vendor'], re.IGNORECASE):

				all_port = list()

				if serialized_data['csr_vendor'] == 'Huawei':
					if serialized_data['agg_port_main']:
						all_port.append(
							{
							"port": serialized_data['agg_port_main'],
							"type": "main",
							"csr_port": "0/2/0"		# correct value = "0/2/0"
							}
						)
					if serialized_data['agg_port_protect']:
						all_port.append(
							{
							"port": serialized_data['agg_port_protect'],
							"type": "protect",
							"csr_port": "0/2/1"		# correct value = "0/2/1"
							}
						)

				else:
					if serialized_data['agg_port_main']:
						all_port.append(
							{
							"port": serialized_data['agg_port_main'],
							"type": "main",
							"csr_port": "1/1/0/1"		# correct value = "1/1/0/1"
							}
						)
					if serialized_data['agg_port_protect']:
						all_port.append(
							{
							"port": serialized_data['agg_port_protect'],
							"type": "protect",
							"csr_port": "1/1/0/2"		# correct value = "1/1/0/2"
							}
						)

				# declare main url variable
				url = 'http://pisa-huawei-svc/huawei/agg/'


				#                                                  #
				#              Verify Power Reading                #
				#                                                  #
				j = 0	# for assignment of the sfp distance
				for element in all_port:

					# get only the port number without port type e.g. 3/0/11 from 25GE3/0/11
					port_number = re.split(r"[a-zA-Z]+", element['port'])[1].strip()

					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'filter': port_number
					}

					response = requests.get(url+'v1/display_optical_module_brief/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving fiber reading for {element['type']} port ({element['port']})"
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message
					
					else:
						if output['output'] == "There is no output returned from NE":
							output_message = f"There is no matching {element['type']} port {element['port']} configuration returned while checking fiber reading"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message
						
						else:

							# determine index of matching output ie port number
							for i in range(len(output['output'])):
								if re.search(f"{port_number}$", output['output'][i]['port'], re.IGNORECASE):
									index_fiber = i
									break

							rx_power, tx_power = False, False
							threshold_fiber_reading = {
								"10km": {
									'RxPower': [-14, 0.5],
									'TxPower': [-7.898, -0.499],
								},
								"40km": {
									'RxPower': [-15, -1],
									'TxPower': [-4.4, 3.999],
								},
								"80km": {
									'RxPower': [-23.1, -7],
									'TxPower': [-0.3, 5],
								},
							}

							# verify fiber reading based on distance
							for i in list(threshold_fiber_reading.keys()):
								if re.search(i, output['output'][index_fiber]['type']):
									serialized_data['agg_distance'] = i
									all_port[j]['sfp_distance'] = i
									j += 1
									if threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][0] < output['output'][index_fiber]['RxPower'] < threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][1]:
										rx_power = True
									if threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][0] < output['output'][index_fiber]['TxPower'] < threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][1]:
										tx_power = True
									break

							# if rx_power = False and tx_power = False
							if not rx_power and not tx_power:
								output_message = f"Fiber reading (RxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][1]}dBm"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							# if rx_power = True and tx_power = False
							elif rx_power and not tx_power:
								output_message = f"Fiber reading (TxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][1]}dBm"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							# if rx_power = False and tx_power = True
							elif not rx_power and tx_power:
								output_message = f"Fiber reading (RxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][1]}dBm"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message
							
							# if both is True
							elif rx_power and tx_power:
								output_message = f"Fiber readings (RxPower ({output['output'][index_fiber]['RxPower']}dBm) and TxPower ({output['output'][index_fiber]['TxPower']}dBm)) for {element['type']} port {element['port']} are within thresholds"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

				agg_output = {'agg': { 'power_reading': api_output }}
				api_output = dict()

				#                                                  #
				#              Verify LLDP neighbour               #
				#                                                  #

				for element in all_port:

					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'filter': element['port'].upper() if re.search(r'ge', element['port'], re.IGNORECASE) else element['port']   # change to cater uppercase for 25GE and normalcase for GigabitEthernet
					}

					response = requests.get(url+'v1/display_lldp_neighbor_brief/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving lldp neighbor for {element['type']} port ({element['port']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message

					else:
						if output['output'] == "The port does not exist":
							output_message = f"There is no matching {element['type']} port {element['port']} configuration returned while checking lldp neighbor"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message
						
						else:
							match = False
							# determine index of matching output ie port number
							for i in range(len(output['output'])):
								if re.search(f"{element['port']}$", output['output'][i]['local_intf'], re.IGNORECASE):
									if re.search(f"{element['csr_port']}$", output['output'][i]['neighbor_intf'], re.IGNORECASE):
										match = True
									index_lldp = i
									break

							if match:
								output_message = f"CSR port ({output['output'][index_lldp]['neighbor_intf']}) returned from LLDP neighbor configurations match with default value {element['csr_port']} for {element['type']} port ({element['port']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

							else:
								output_message = f"CSR port ({output['output'][index_lldp]['neighbor_intf']}) returned from LLDP neighbor configurations not match with default value {element['csr_port']} for {element['type']} port ({element['port']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

				agg_output['agg']['lldp_neighbor'] = api_output
				api_output = dict()

				#                                                     #
				#              Verify CRC error packet                #
				#                                                     #

				for element in all_port:

					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'interface': element['port']
					}

					response = requests.get(url+'v1/verify_crc_packet_interface/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving crc reading for {element['type']} port ({element['port']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message
					
					else:
						if output['output'] == "The crc output does not exist":
							output_message = f"There is no matching {element['type']} port {element['port']} configuration returned while checking crc reading"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message
						
						else:
							# check if crc = 0 
							if output['output']['crc'] == 0:
								output_message = f"CRC error packet for {element['type']} port ({element['port']}) is {output['output']['crc']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								# api_output[element['type']]['crc_reading'] = output['output']['crc']
								api_output[element['type']]['details'] = output_message

							else:
								output_message = f"CRC error packet for {element['type']} port ({element['port']}) is greater than zero ({output['output']['crc']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								# api_output[element['type']]['crc_reading'] = output['output']['crc']
								api_output[element['type']]['details'] = output_message

				agg_output['agg']['crc'] = api_output
				api_output = dict()

				#                                                #
				#              Verify BFD session                #
				#                                                #
				
				# to find CSR WAN IP: display bfd session all | i <AGG PORT>

				payload = {
					'ne_ip': serialized_data['agg_loopback_ip'],
					'filter': serialized_data['csr_wan_ip']
				}

				response = requests.get(url+'v1/display_bfd_session_all/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving BFD session configurations that matches CSR WAN IP ({serialized_data['csr_wan_ip']})"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					if len(all_port) == 2:
						api_output['main'] = { 'status': 'undefined' }
						api_output['main']['details'] = error_message
						api_output['protect'] = { 'status': 'undefined' }
						api_output['protect']['details'] = error_message

					else:
						api_output['main'] = { 'status': 'undefined' }
						api_output['main']['details'] = error_message
				
				else:
					if output['output'] == "The bfd session does not exist" or output['output'] == "There is no output returned from NE":
						output_message = f"There is no matching CSR WAN IP ({serialized_data['csr_wan_ip']}) configurations returned while checking bfd session"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						if len(all_port) == 2:
							api_output['main'] = { 'status': 'undefined' }
							api_output['main']['details'] = output_message
							api_output['protect'] = { 'status': 'undefined' }
							api_output['protect']['details'] = output_message

						else:
							api_output['main'] = { 'status': 'undefined' }
							api_output['main']['details'] = output_message
					
					else:
						# check main and protect port 
						for element in all_port:
							up = False
							# determine index of matching output ie port and state
							for i in range(len(output['output'])):
								if re.search(f"{element['port']}$", output['output'][i]['int_name'], re.IGNORECASE):
									index_bfd = i
									if re.search('up', output['output'][i]['state'], re.IGNORECASE):
										up = True
									break

							if up:
								output_message = f"BFD session state for {element['type']} port ({element['port']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd]['state']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

							elif not up and 'index_bfd' not in locals():
								output_message = f"No matching port for {element['type']} port ({element['port']}) in the returned BFD session configurations"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': 'undefined' }
								api_output[element['type']]['details'] = output_message

							else:
								output_message = f"BFD session state for {element['type']} port ({element['port']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd]['state']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

						# check virtual port
						agg_virtual_up = False
						# determine index of matching output ie port and state
						for i in range(len(output['output'])):
							if re.search(f"eth-trunk", output['output'][i]['int_name'], re.IGNORECASE):
								index_agg_virtual_bfd = i
								if re.search('up', output['output'][i]['state'], re.IGNORECASE):
									agg_virtual_up = True
								break

						if agg_virtual_up:
							output_message = f"BFD session state for virtual port ({output['output'][index_agg_virtual_bfd]['int_name']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_agg_virtual_bfd]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output['virtual'] = { 'status': True }
							api_output['virtual']['details'] = output_message

						elif not agg_virtual_up and 'index_agg_virtual_bfd' not in locals():
							output_message = f"No matching port for virtual port ({output['output'][index_agg_virtual_bfd]['int_name']}) in the returned BFD session configurations"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output['virtual'] = { 'status': 'undefined' }
							api_output['virtual']['details'] = output_message

						else:
							output_message = f"BFD session state for virtual port ({output['output'][index_agg_virtual_bfd]['int_name']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_agg_virtual_bfd]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output['virtual'] = { 'status': False }
							api_output['virtual']['details'] = output_message

				agg_output['agg']['bfd'] = api_output
				api_output = dict()

				#                                                #
				#              Verify ISIS Status                #
				#                                                #
				
				payload = {
					'ne_ip': serialized_data['agg_loopback_ip'],
					'filter': serialized_data['csr_hostname'].lower()
				}

				response = requests.get(url+'v1/verify_isis_status/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving ISIS status"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output = { 'status': 'undefined' }
					api_output['details'] = error_message
				
				else:
					if output['output'] == "The isis peer does not exist":
						output_message = f"There is no matching csr hostname ({serialized_data['csr_hostname'].lower()}) configurations returned while checking ISIS status"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output = { 'status': 'undefined' }
						api_output['details'] = output_message

					else:
						up = False
						# determine index of matching output ie port and state
						for i in range(len(output['output'])):
							if re.search(f"{serialized_data['csr_hostname']}$", output['output'][i]['system_id'], re.IGNORECASE):
								index_isis = i
								if re.search('up', output['output'][i]['state'], re.IGNORECASE):
									up = True
								break

						if up:
							output_message = f"ISIS status state for csr hostname ({serialized_data['csr_hostname'].lower()}) is {output['output'][index_isis]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': True }
							api_output['details'] = output_message

						elif not up and 'index_isis' not in locals():
							output_message = f"No matching csr hostname ({serialized_data['csr_hostname'].lower()}) in the returned ISIS status configurations"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': 'undefined' }
							api_output['details'] = output_message

						else:
							output_message = output_message = f"ISIS status state for csr hostname ({serialized_data['csr_hostname'].lower()}) is {output['output'][index_isis]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': False }
							api_output['details'] = output_message

				agg_output['agg']['isis'] = api_output
				api_output = dict()


			#                                               #
			#              LOGIC FOR AGG ZTE                #
			#                                               #

			elif re.search('zte', serialized_data['agg_vendor'], re.IGNORECASE):

				all_port = list()

				# for CSR Huawei
				if serialized_data['csr_vendor'] == 'Huawei':
					if serialized_data['agg_port_main']:
						all_port.append(
							{
							"port": serialized_data['agg_port_main'].lower(),
							"type": "main",
							"csr_port": "0/2/0"		# correct value = "0/2/0"
							}
						)
					if serialized_data['agg_port_protect']:
						all_port.append(
							{
							"port": serialized_data['agg_port_protect'].lower(),
							"type": "protect",
							"csr_port": "0/2/1"		# correct value = "0/2/1"
							}
						)
				# for CSR ZTE
				else:
					if serialized_data['agg_port_main']:
						all_port.append(
							{
							"port": serialized_data['agg_port_main'].lower(),
							"type": "main",
							"csr_port": "1/1/0/1"		# correct value = "1/1/0/1"
							}
						)
					if serialized_data['agg_port_protect']:
						all_port.append(
							{
							"port": serialized_data['agg_port_protect'].lower(),
							"type": "protect",
							"csr_port": "1/1/0/2"		# correct value = "1/1/0/2"
							}
						)

				# declare main url variable
				url = 'http://pisa-zte-svc/zte/agg/'


				#                                                  #
				#              Verify Power Reading                #
				#                                                  #
				j = 0	# for assignment of the sfp distance
				for element in all_port:

					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'filter': element['port']
					}

					response = requests.get(url+'v1/show_optical_info_brief/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving fiber reading for {element['type']} port ({element['port']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message
					
					else:
						if output['output'] == "There is no output returned from NE":
							output_message = f"There is no matching {element['type']} port {element['port']} configurations returned while checking fiber reading"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message
						
						else:
							# determine index of matching output ie port number
							for i in range(len(output['output'])):
								if 'port' in output['output'][i]:
									if re.search(f"{element['port']}$", output['output'][i]['port'], re.IGNORECASE):
										index_fiber = i
								else:
									continue

							rx_power, tx_power = False, False
							threshold_fiber_reading = {
								"10km": {
									'RxPower': [-14, 0.5],
									'TxPower': [-7.9, 1.5],
								},
								"40km": {
									'RxPower': [-15, -1],
									'TxPower': [-4.4, 5],
								},
								"80km": {
									'RxPower': [-23.1, -7],
									'TxPower': [-0.3, 5],
								},
							}

							# verify fiber reading based on distance
							for i in list(threshold_fiber_reading.keys()):
								if re.search(i, output['output'][index_fiber]['type']):
									serialized_data['agg_distance'] = i
									all_port[j]['sfp_distance'] = i
									j += 1
									if threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][0] < output['output'][index_fiber]['RxPower'] < threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][1]:
										rx_power = True
									if threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][0] < output['output'][index_fiber]['TxPower'] < threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][1]:
										tx_power = True
									break

							# if rx_power = False and tx_power = False
							if not rx_power and not tx_power:
								output_message = f"Fiber reading (RxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][1]}dBm"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							# if rx_power = True and tx_power = False
							elif rx_power and not tx_power:
								output_message = f"Fiber reading (TxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['TxPower'][1]}dBm"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							# if rx_power = False and tx_power = True
							elif not rx_power and tx_power:
								output_message = f"Fiber reading (RxPower) for {element['type']} port {element['port']} ie {output['output'][index_fiber]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['agg_distance']]['RxPower'][1]}dBm"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							# if both is True
							elif rx_power and tx_power:
								output_message = f"Fiber readings (RxPower ({output['output'][index_fiber]['RxPower']}dBm) and TxPower ({output['output'][index_fiber]['TxPower']}dBm)) for {element['type']} port {element['port']} are within thresholds"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

				agg_output = {'agg': { 'power_reading': api_output }}
				api_output = dict()


				#                                                  #
				#              Verify LLDP neighbour               #
				#                                                  #

				for element in all_port:

					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'port': element['port']
					}

					response = requests.get(url+'v1/show_lldp_entry_interface/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving lldp neighbor for {element['type']} port ({element['port']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message
					
					else:
						if output['output'] == "There is no output returned from NE" or output['output'] == "There is error returned from NE":
							output_message = f"There is no matching {element['type']} port {element['port']} configuration returned while checking lldp neighbor"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message
						
						else:
							if re.search(f"{element['csr_port']}$", output['output']['peer_port'], re.IGNORECASE):
								output_message = f"CSR port ({output['output']['peer_port']}) returned from LLDP neighbor configurations match with default value {element['csr_port']} for {element['type']} port ({element['port']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

							else:
								output_message = f"CSR port ({output['output']['peer_port']}) returned from LLDP neighbor configurations not match with default value {element['csr_port']} for {element['type']} port ({element['port']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

				agg_output['agg']['lldp_neighbor'] = api_output
				api_output = dict()

				#                                                     #
				#              Verify CRC error packet                #
				#                                                     #

				for element in all_port:

					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'port': element['port']
					}

					response = requests.get(url+'v1/show_port_crc/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving crc reading for {element['type']} port ({element['port']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message

					else:
						if output['output'] == "There is error returned from NE":
							output_message = f"There is no matching {element['type']} port {element['port']} configuration returned while checking crc reading"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						else:
							# check if crc = 0 
							if output['output']['In_CRC_ERROR'] == 0:
								output_message = f"CRC error packet for {element['type']} port ({element['port']}) is {output['output']['In_CRC_ERROR']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								# api_output[element['type']]['crc_reading'] = output['output']['In_CRC_ERROR']
								api_output[element['type']]['details'] = output_message

							else:
								output_message = f"CRC error packet for {element['type']} port ({element['port']}) is greater than zero ({output['output']['In_CRC_ERROR']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								# api_output[element['type']]['crc_reading'] = output['output']['In_CRC_ERROR']
								api_output[element['type']]['details'] = output_message

				agg_output['agg']['crc'] = api_output
				api_output = dict()

				#                                                #
				#              Verify BFD session                #
				#                                                #
				for element in all_port:
					payload = {
						'ne_ip': serialized_data['agg_loopback_ip'],
						'filter': serialized_data['agg_wan_ip']
					}

					response = requests.get(url+'v1/show_bfd_neighbor_all_brief/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving BFD session configurations that matches {element['type']} port {element['port']}"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message

					else:
						if output['output'] == "There is no output returned from NE":
							output_message = f"There is no matching {element['type']} port {element['port']} configurations returned while checking BFD session"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						else:
							up = False
							# determine index of matching output ie port and state
							for i in range(len(output['output'])):
								# if serialized_data['csr_wan_ip'] == output['output'][i]['neighbor'] and re.search(f"{element['port']}$", output['output'][i]['interface'], re.IGNORECASE):
								if re.search(f"{element['port']}$", output['output'][i]['interface'], re.IGNORECASE):
									index_bfd = i
									if re.search('up', output['output'][i]['state'], re.IGNORECASE):
										up = True
									break

							if up:
								output_message = f"BFD session state for {element['type']} port ({element['port']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd]['state']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

							elif not up and 'index_bfd' not in locals():
								output_message = f"No matching CSR WAN IP for {element['type']} port ({element['port']}) in the returned BFD session configurations"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': 'undefined' }
								api_output[element['type']]['details'] = output_message

							else:
								output_message = f"BFD session state for {element['type']} port ({element['port']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd]['state']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

				agg_virtual_up = False
				# determine index of matching output ie port and state
				for i in range(len(output['output'])):
					if re.search(r"smartgroup", output['output'][i]['interface'], re.IGNORECASE):
						index_agg_virtual_bfd = i
						if re.search('up', output['output'][i]['state'], re.IGNORECASE):
							agg_virtual_up = True
						break

				if agg_virtual_up:
					output_message = f"BFD session state for virtual port ({output['output'][index_agg_virtual_bfd]['interface']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_agg_virtual_bfd]['state']}"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					api_output['virtual'] = { 'status': True }
					api_output['virtual']['details'] = output_message

				elif not agg_virtual_up and 'index_agg_virtual_bfd' not in locals():
					output_message = f"No matching CSR WAN IP for virtual port in the returned BFD session configurations"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					api_output['virtual'] = { 'status': 'undefined' }
					api_output['virtual']['details'] = output_message

				else:
					output_message = f"BFD session state for virtual port ({output['output'][index_agg_virtual_bfd]['interface']}) and CSR WAN IP ({serialized_data['csr_wan_ip']}) is {output['output'][index_agg_virtual_bfd]['state']}"
					log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					api_output['virtual'] = { 'status': False }
					api_output['virtual']['details'] = output_message

				agg_output['agg']['bfd'] = api_output
				api_output = dict()

				#                                                #
				#              Verify ISIS Status                #
				#                                                #
				
				payload = {
					'ne_ip': serialized_data['agg_loopback_ip'],
					'filter': serialized_data['csr_hostname'].lower()
				}

				response = requests.get(url+'v1/show_isis_adjacency/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving ISIS status"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output = { 'status': 'undefined' }
					api_output['details'] = error_message

				else:
					if output['output'] == "There is no output returned from NE":
						output_message = f"There is no matching CSR hostname ({serialized_data['csr_hostname'].lower()}) configurations returned while checking ISIS status"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output = { 'status': 'undefined' }
						api_output['details'] = output_message

					else:
						up = False
						# determine index of matching output ie port and state
						for i in range(len(output['output'])):
							if re.search(output['output'][i]['system_id'].replace('...', ''), serialized_data['csr_hostname'],  re.IGNORECASE):
								index_isis = i
								if re.search('up', output['output'][i]['state'], re.IGNORECASE):
									up = True
								break

						if up:
							output_message = f"ISIS status state for CSR hostname ({serialized_data['csr_hostname'].lower()}) is {output['output'][index_isis]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': True }
							api_output['details'] = output_message

						elif not up and 'index_isis' not in locals():
							output_message = f"No matching CSR hostname ({serialized_data['csr_hostname'].lower()}) in the returned ISIS status configurations"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': 'undefined' }
							api_output['details'] = output_message

						else:
							output_message = output_message = f"ISIS status state for CSR hostname ({serialized_data['csr_hostname'].lower()}) is {output['output'][index_isis]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': False }
							api_output['details'] = output_message

				agg_output['agg']['isis'] = api_output
				api_output = dict()


			#                                                  #
			#              LOGIC FOR CSR Huawei                #
			#                                                  #

			if serialized_data['csr_vendor'] == 'Huawei':

				all_ports_csr = list()

				if serialized_data['agg_port_main']:
					all_ports_csr.append(
						{
							"agg_port": serialized_data['agg_port_main'],
							"type": "main",
							"csr_port_down": "ETH0/2/0",		# correct value = "ETH0/2/0"
							"csr_port_up": "0/2/0"		# correct value = "0/2/0"
						}
					)
				if serialized_data['agg_port_protect']:
					all_ports_csr.append(
						{
							"agg_port": serialized_data['agg_port_protect'],
							"type": "protect",
							"csr_port_down": "ETH0/2/1",		# correct value = "ETH0/1/0"
							"csr_port_up": "0/2/1"		# correct value = "0/2/1"
						}
					)

				# declare main url variable
				url = 'http://pisa-huawei-svc/huawei/csr/'


				#                                                  #
				#              Verify Power Reading                #
				#                                                  #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/display_optical_module_brief/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving fiber reading for {serialized_data['csr_hostname'].upper()}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					# if len(all_ports_csr) == 2:
					# 	api_output['main'] = { 'status': 'undefined' }
					# 	api_output['main']['details'] = error_message
					# 	api_output['protect'] = { 'status': 'undefined' }
					# 	api_output['protect']['details'] = error_message

					# else:
					# 	api_output['main'] = { 'status': 'undefined' }
					# 	api_output['main']['details'] = error_message
					
					#                                                                       #
					#              Stop verification connection error at CSR                #
					#                                                                       #
					final_output.append(agg_output)
					output_message = f"Successfully run verifications on AGG but failed on CSR"
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type': 'Verification',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': str(final_output),
						'status': 'failed',
						'log_file': log_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = final_output
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_200_OK)
					
				else:
					j = 0	# for sfp distance
					for element in all_ports_csr:
						# determine index of matching output ie port number
						for i in range(len(output['output'])):
							if re.search(f"{element['csr_port_down']}$", output['output'][i]['port'], re.IGNORECASE):
								index_fiber_csr = i
								break

						rx_power, tx_power = False, False
						threshold_fiber_reading = {
							"10km": {
								'RxPower': [-14, 0.499],
								'TxPower': [-7.898, -0.499],
							},
							"40km": {
								'RxPower': [-15, -1],
								'TxPower': [-4.4, 4],
							},
							"80km": {
								'RxPower': [-23.979, -7],
								'TxPower': [0, 4],
							}
						}

						# verify fiber reading based on distance
						for i in list(threshold_fiber_reading.keys()):
							if re.search(i, output['output'][index_fiber_csr]['type']):
								serialized_data['csr_distance'] = i
								all_ports_csr[j]['sfp_distance'] = i
								j += 1
								if threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][0] < output['output'][index_fiber_csr]['RxPower'] < threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][1]:
									rx_power = True
								if threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][0] < output['output'][index_fiber_csr]['TxPower'] < threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][1]:
									tx_power = True
								break

						# if rx_power = False and tx_power = False
						if not rx_power and not tx_power:
							output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][1]}dBm"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						# if rx_power = True and tx_power = False
						elif rx_power and not tx_power:
							output_message = f"Fiber reading (TxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][1]}dBm"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						# if rx_power = False and tx_power = True
						elif not rx_power and tx_power:
							output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][1]}dBm"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message
						
						# if both is True
						elif rx_power and tx_power:
							output_message = f"Fiber readings (RxPower ({output['output'][index_fiber_csr]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_csr]['TxPower']}dBm)) for CSR {element['type']} port {element['csr_port_down']} are within thresholds"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

				csr_output = {'csr': { 'power_reading': api_output }}
				api_output = dict()

				#                                                         #
				#              Compare SFP Type (AGG & CSR)               #
				#                                                         #

				for i in range(len(all_port)):
					if all_port[i]['sfp_distance'] == all_ports_csr[i]['sfp_distance']:
						output_message = f"SFP type for {all_port[i]['type']} port match for both AGG ({all_port[i]['sfp_distance']}) and CSR ({all_ports_csr[i]['sfp_distance']})"
						# log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						log_text(filename=log_file, content=output_message)
						# api_output[all_port[i]['type']] = True
						api_output[all_port[i]['type']] = { 'status': True }
						api_output[all_port[i]['type']]['details'] = output_message

					else:
						output_message = f"SFP type for {all_port[i]['type']} port mismatch between AGG ({all_port[i]['sfp_distance']}) and CSR ({all_ports_csr[i]['sfp_distance']})"
						log_text(filename=log_file, content=output_message)
						# api_output[all_port[i]['type']] = False
						api_output[all_port[i]['type']] = { 'status': False }
						api_output[all_port[i]['type']]['details'] = output_message

				csr_output['csr']['sfp'] = api_output
				agg_output['agg']['sfp'] = api_output
				api_output = dict()

				#                                                  #
				#              Verify LLDP neighbour               #
				#                                                  #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/display_lldp_neighbor_brief/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving lldp neighbor details for {serialized_data['csr_hostname'].upper()}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output[element['type']] = { 'status': 'undefined' }
					api_output[element['type']]['details'] = error_message
	
				else:
					for element in all_ports_csr:

						match = False

						for i in range(len(output['output'])):
							if re.search(f"{element['csr_port_up']}$", output['output'][i]['LocalIntf'], re.IGNORECASE):
								index_lldp_csr = i
								if re.search(f"{serialized_data['agg_hostname']}$", output['output'][i]['NeighborDev'], re.IGNORECASE) and re.search(f"{element['agg_port']}$", output['output'][i]['NeighborIntf'], re.IGNORECASE):
									match = True
									break

						if not match and "index_lldp_csr" not in locals():
							output_message = f"CSR {element['type']} port returned from LLDP neighbor configurations does not match with default value {element['csr_port_up']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						elif not match:
							output_message = f"AGG {element['type']} port ({output['output'][index_lldp_csr]['NeighborIntf']}) and AGG hostname ({output['output'][index_lldp_csr]['NeighborDev']}) returned from LLDP neighbor configurations mismatch with CSR {element['type']} port ({output['output'][index_lldp_csr]['LocalIntf']})"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						elif match:
							output_message = f"AGG {element['type']} port ({output['output'][index_lldp_csr]['NeighborIntf']}) and AGG hostname ({output['output'][index_lldp_csr]['NeighborDev']}) returned from LLDP neighbor configurations match with CSR {element['type']} port ({output['output'][index_lldp_csr]['LocalIntf']})"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

				csr_output['csr']['lldp_neighbor'] = api_output
				api_output = dict()

				#                                                          #
				#              Verify Interface Description                #
				#                                                          #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/display_interface_description/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving all ports' description for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output[element['type']] = { 'status': 'undefined' }
					api_output[element['type']]['details'] = error_message
					
				else:
					k = 0	# for multiple sfp distance
					for element in all_ports_csr:
						# determine index of matching output ie port number
						for i in range(len(output['output'])):
							# if re.search(f"{element['csr_port_up']}$", output['output'][i]['interface'], re.IGNORECASE) or re.search(f"{element['csr_port_up']}(10G)$", output['output'][i]['interface'], re.IGNORECASE):
							pattern = f"{element['csr_port_up']}(?!\d)"	# not allow digit after port e.g. 1/0/11 will not match 1/0/1
							if re.search(pattern, output['output'][i]['interface'], re.IGNORECASE):
								index_desc_csr = i
								break

						port_description = output['output'][index_desc_csr]['description']
						check_info = False
						check_distance = False
						sfp_distance = None
						csr_port = f"GE{element['csr_port_up']}"

						# check csr and agg hostname and port
						if re.search(element['csr_port_up'], port_description, re.IGNORECASE) and re.search(serialized_data['csr_hostname'], port_description, re.IGNORECASE) and re.search(element['agg_port'], port_description, re.IGNORECASE) and re.search(serialized_data['agg_hostname'], port_description, re.IGNORECASE):
							check_info = True

						# check fiber info
						match = re.search(r'\[(.*?)\]', port_description)
						# check fiber info contains []
						if match:
							fiber_info, sfp_distance = match.group(1), None
							# standard fiber
							if not '|' in fiber_info:
								if re.match(r"(?=.*[_])(?=.*[,])", fiber_info):	# must contain , and _
									if len(fiber_info.split(',')[1].strip().split('_')) > 1:	# check if there is _
										if re.match(r'^[0-9\.]+km$', fiber_info.split(',')[1].strip().split('_')[1].lower()):	# must contain number and km
											sfp_distance = round(float(re.sub('km', '', fiber_info.split(',')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)
								elif re.match(r"(?=.*[_])(?=.*[;])", fiber_info):	# must contain ; and _
									if len(fiber_info.split(';')[1].strip().split('_')) > 1:	# check if there is _
										if re.match(r'^[0-9\.]+km$', fiber_info.split(';')[1].strip().split('_')[1].lower()):	# must contain number and km
											sfp_distance = round(float(re.sub('km', '', fiber_info.split(';')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)

							else:
								# fiber junction
								# if not re.search(r'dwdm', fiber_info, re.IGNORECASE):
								# sfp_distance = 0
								# distance = list()
								split_fiber_info = fiber_info.split('|')
								for i in range(len(split_fiber_info)):
									if re.match(r"(?=.*[_])(?=.*[,])", split_fiber_info[i].strip()):	# must contain , and _
										if len(split_fiber_info[i].strip().split(',')[1].split('_')) > 1:	# check if there is _
											if re.match(r'^[0-9\.]+km$', split_fiber_info[i].strip().split(',')[1].split('_')[1].lower()):	# must contain number and km
												if sfp_distance is None:
													sfp_distance = round(float(re.sub('km', '', split_fiber_info[i].split(',')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)
												else:
													sfp_distance += round(float(re.sub('km', '', split_fiber_info[i].split(',')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)
													sfp_distance = round(sfp_distance, 2)
									elif re.match(r"(?=.*[_])(?=.*[;])", split_fiber_info[i].strip()):	# must contain ; and _
										if len(split_fiber_info[i].strip().split(';')[1].split('_')) > 1:	# check if there is _
											if re.match(r'^[0-9\.]+km$', split_fiber_info[i].strip().split(';')[1].split('_')[1].lower()):	# must contain number and km
												if sfp_distance == 0:
													sfp_distance = round(float(re.sub('km', '', split_fiber_info[i].split(';')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)
												else:
													sfp_distance += round(float(re.sub('km', '', split_fiber_info[i].split(';')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)
													sfp_distance = round(sfp_distance, 2)

								# dwdm
								# else:
								# 	split_fiber_info = fiber_info.split('|')
								# 	if re.match(r'^.*[,_].*[,_].*$', split_fiber_info[0].strip()):	# must contain , and _
								# 		if len(split_fiber_info[0].strip().split(',')[1].split('_')) > 1:	# check if there is _
								# 			if re.match(r'^[0-9\.]+km$', split_fiber_info[0].strip().split(',')[1].split('_')[1].lower()):	# must contain number and km
								# 				sfp_distance = round(float(re.sub('km', '', split_fiber_info[0].strip().split(',')[1].split('_')[1], flags=re.IGNORECASE)), 3)
						# else:
						# 	output_message = f"The CSR port description ({port_description}) for {element['csr_port_up']} does not contain fiber details"
						# 	log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						# 	api_output[element['type']] = { 'status': 'undefined' }
						# 	api_output[element['type']]['details'] = output_message
						
						fiber_reading_range = {
							"10km": [0, 10],
							"40km": [10.1, 40],
							"80km": [40.1, 80],
						}

						#  verify sfp distance
						if 'sfp_distance' is not None and isinstance(sfp_distance, float):
							if fiber_reading_range[all_ports_csr[k]['sfp_distance']][0] <= sfp_distance <= fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]:
								check_distance = True

							# check info = True and check distance = True
							if check_info and check_distance:
								output_message = f"The CSR port description ({port_description}) for port {element['csr_port_up']} contains complete information and the specified distance ({sfp_distance}km) is within the range {fiber_reading_range[all_ports_csr[k]['sfp_distance']][0]}km to {fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]}km for SFP type {all_ports_csr[k]['sfp_distance']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

							else:
								# check info = False and check distance = False
								if not check_info and not check_distance:
									output_message = f"Missing/mismatch required info (AGG hostname, AGG port and CSR hostname) and the fiber distance ({sfp_distance}km) specified in description ({port_description}) for port {element['csr_port_up']} is not within range {fiber_reading_range[all_ports_csr[k]['sfp_distance']][0]}km to {fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]}km for SFP type {all_ports_csr[k]['sfp_distance']}"
								
								# check info = False and check distance = True
								elif not check_info and check_distance:
									output_message = f"Missing/mismatch required info (AGG hostname, AGG port and CSR hostname) in port description ({port_description}) for port {element['csr_port_up']}"

								# check info = True and check distance = False
								elif check_info and not check_distance:
									output_message = f"The fiber distance ({sfp_distance}km) specified in description ({port_description}) for port {element['csr_port_up']} is not within range {fiber_reading_range[all_ports_csr[k]['sfp_distance']][0]}km to {fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]}km for SFP type {all_ports_csr[k]['sfp_distance']}"

								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							k += 1

						else:
							output_message = f"Invalid fiber info format or no sfp distance specified in description ({port_description}) for port {element['csr_port_up']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

				csr_output['csr']['port_description'] = api_output
				api_output = dict()

				#                                                     #
				#              Verify CRC error packet                #
				#                                                     #
				for element in all_ports_csr:

					payload = {
						'csr_ip': serialized_data['csr_wan_ip'],
						'agg_ip': serialized_data['agg_loopback_ip'],
						'agg_hostname': serialized_data['agg_hostname'],
						'interface': f"GigabitEthernet {element['csr_port_up']}",
					}

					response = requests.get(url+'v1/via_agg/display_crc_packet_interface/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving crc reading for CSR {element['type']} port (GigabitEthernet {element['csr_port_up']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message

					else:
						if output['output'] == "The crc output does not exist":
							output_message = f"There is no matching CSR {element['type']} port {element['csr_port_up']} configuration returned while checking crc reading"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						else:
							# check if crc = 0 
							if output['output']['crc'] == 0:
								output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port_up']} is {output['output']['crc']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message
								# api_output[element['type']]['crc_reading'] = output['output']['crc']

							else:
								output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port_up']} is greater than zero ({output['output']['crc']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message
								# api_output[element['type']]['crc_reading'] = output['output']['crc']

				csr_output['csr']['crc'] = api_output
				api_output = dict()

				#                                               #
				#              Verify BFD session               #
				#                                               #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/display_bfd_session_all/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving bfd session configurations for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					if len(all_ports_csr) == 2:
						api_output['main'] = { 'status': 'undefined' }
						api_output['main']['details'] = error_message
						api_output['protect'] = { 'status': 'undefined' }
						api_output['protect']['details'] = error_message

					else:
						api_output['main'] = { 'status': 'undefined' }
						api_output['main']['details'] = error_message
				
				else:
					# verify main and protect port
					for element in all_ports_csr:

						up, index_bfd_csr = False, None
						# determine index of matching output ie port and state
						for i in range(len(output['output'])):
							if serialized_data['agg_wan_ip'] == output['output'][i]['peer_ip_address'] and re.search(f"{element['csr_port_up']}$", output['output'][i]['int_name']):
								index_bfd_csr = i
								if re.search('up', output['output'][i]['state'], re.IGNORECASE):
									up = True
								break

						if not up and index_bfd_csr is None:
							output_message = f"No matching CSR {element['type']} port ({element['csr_port_up']}) and AGG WAN IP Address ({serialized_data['agg_wan_ip']}) in the returned BFD session configurations"
							log_text(filename=log_file, content=output_message,
									ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						elif not up:
							output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port_up']}) and AGG WAN IP Address ({serialized_data['agg_wan_ip']}) is {output['output'][index_bfd_csr]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						elif up:
							output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port_up']}) and AGG WAN IP Address ({serialized_data['agg_wan_ip']}) is {output['output'][index_bfd_csr]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

					# verify eth-trunk
					virtual_up = False

					# determine index of matching output ie port and state
					for i in range(len(output['output'])):
						if serialized_data['agg_wan_ip'] == output['output'][i]['peer_ip_address'] and re.search(f"eth-trunk1", output['output'][i]['int_name'], re.IGNORECASE):
							index_bfd_virtual_csr = i
							if re.search('up', output['output'][i]['state'], re.IGNORECASE):
								virtual_up = True
							break

					if not virtual_up and "index_bfd_virtual_csr" not in locals():
						output_message = f"No matching CSR port (Eth-Trunk1) and AGG WAN IP Address ({serialized_data['agg_wan_ip']}) in the returned BFD session configurations"
						log_text(filename=log_file, content=output_message,
								ne_output=output['ne_response'])
						api_output['virtual'] = { 'status': 'undefined' }
						api_output['virtual']['details'] = output_message

					elif not virtual_up:
						output_message = f"BFD session state for CSR port (Eth-Trunk1) and AGG WAN IP Address ({serialized_data['agg_wan_ip']}) is {output['output'][index_bfd_virtual_csr]['state']}"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['virtual'] = { 'status': False }
						api_output['virtual']['details'] = output_message

					elif virtual_up:
						output_message = f"BFD session state for CSR port (Eth-Trunk1) and AGG WAN IP Address ({serialized_data['agg_wan_ip']}) is {output['output'][index_bfd_virtual_csr]['state']}"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['virtual'] = { 'status': True }
						api_output['virtual']['details'] = output_message

				csr_output['csr']['bfd'] = api_output
				api_output = dict()

				#                                                  #
				#              Verify license status               #
				#                                                  #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/display_license_balance_status/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving license status for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output = { 'status': 'undefined' }
					api_output['details'] = error_message

				else:
					for element in all_ports_csr:

						activated, index_license_csr = False, None

						for i in range(len(output['output'])):
							if re.search(f"{element['csr_port_up']}$", output['output'][i]['Physical Position'], re.IGNORECASE):
								index_license_csr = i
								if re.search(r"activated", output['output'][i]['ActiveStatus'], re.IGNORECASE):
									activated = True
								break

						if not activated and index_license_csr is None:
							output_message = f"There is no license status for CSR {element['type']} port ({element['csr_port_up']})"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						elif not activated:
							output_message = f"The license status for CSR {element['type']} port ({element['csr_port_up']}) is not activated ({output['output'][index_license_csr]['ActiveStatus']})"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						elif activated:
							output_message = f"The license status for CSR {element['type']} port ({element['csr_port_up']}) is {output['output'][index_license_csr]['ActiveStatus']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

					# verify port downlink
					activated, index_license_csr = False, None

					for i in range(len(output['output'])):
						if re.search(f"0/2/6$", output['output'][i]['Physical Position'], re.IGNORECASE):
							index_license_csr = i
							if re.search(r"activated", output['output'][i]['ActiveStatus'], re.IGNORECASE):
								activated = True
							break

					if not activated and index_license_csr is None:
						output_message = f"There is no license status for CSR downlink port (0/2/6)"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['downlink'] = { 'status': 'undefined' }
						api_output['downlink']['details'] = output_message

					elif not activated:
						output_message = f"The license status for CSR downlink port (0/2/6) is not activated ({output['output'][index_license_csr]['ActiveStatus']})"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['downlink'] = { 'status': False }
						api_output['downlink']['details'] = output_message

					elif activated:
						output_message = f"The license status for CSR downlink port (0/2/6) is {output['output'][index_license_csr]['ActiveStatus']}"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['downlink'] = { 'status': True }
						api_output['downlink']['details'] = output_message

				csr_output['csr']['license'] = api_output
				api_output = bool()

				#                                                     #
				#              Verify software version                #
				#                                                     #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/display_version/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving software version for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output = { 'status': 'undefined' }
					api_output['details'] = error_message
				
				else:

					if not re.search(r"8.211|8.210", output['output']['SoftwareVersion'], re.IGNORECASE):
						output_message = f"The software version ({output['output']['SoftwareVersion']}) is not valid"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output = { 'status': False }
						api_output['details'] = output_message
					
					else:
						output_message = f"The software version ({output['output']['SoftwareVersion']}) is valid"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output = { 'status': True }
						api_output['details'] = output_message

				csr_output['csr']['software'] = api_output
				api_output = dict()


			#                                               #
			#              LOGIC FOR CSR ZTE                #
			#                                               #

			if serialized_data['csr_vendor'] == 'ZTE':

				all_ports_csr = list()

				if serialized_data['agg_port_main']:
					all_ports_csr.append(
						{
							"agg_port": serialized_data['agg_port_main'],
							"type": "main",
							"csr_port_down": "1/1/0/1",		# correct value = "1/1/0/1"
							"csr_port_up": "1/1/0/1"		# correct value = "1/1/0/1"
						}
					)
				if serialized_data['agg_port_protect']:
					all_ports_csr.append(
						{
							"agg_port": serialized_data['agg_port_protect'],
							"type": "protect",
							"csr_port_down": "1/1/0/2",		# correct value = "1/1/0/2"
							"csr_port_up": "1/1/0/2"		# correct value = "1/1/0/2"
						}
					)

				# declare main url variable
				url = 'http://pisa-zte-svc/zte/csr/'


				#                                                  #
				#              Verify Power Reading                #
				#                                                  #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/show_optical_info_brief/', params=payload, verify=False)
				output = response.json()

				# return Response(output)

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving fiber reading for {serialized_data['csr_hostname'].upper()}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					# api_output[element['type']] = 'undefined'


					#                                                                       #
					#              Stop verification connection error at CSR                #
					#                                                                       #
					final_output.append(agg_output)
					output_message = f"Successfully run verifications on AGG but failed on CSR"
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type': 'Verification',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': str(final_output),
						'status': 'failed',
						'log_file': log_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = final_output
					api_response['log_file'] = log_file
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_200_OK)

				else:
					j = 0	# for sfp distance
					for element in all_ports_csr:
						# determine index of matching output ie port number
						for i in range(len(output['output'])):
							if re.search(f"{element['csr_port_down']}$", output['output'][i]['interface'], re.IGNORECASE):
								index_fiber_csr = i
								break

						rx_power, tx_power, no_signal = False, False, False
						threshold_fiber_reading = {
							"10km": {
								'RxPower': [-14, 0.5],
								'TxPower': [-7.9, 1.5],
							},
							"40km": {
								'RxPower': [-15, -1],
								'TxPower': [-4.4, 5],
							},
							"80km": {
								'RxPower': [-23.1, -7],
								'TxPower': [-0.3, 5],
							}
						}

						# verify fiber reading based on distance
						for i in list(threshold_fiber_reading.keys()):
							if re.search(i, output['output'][index_fiber_csr]['type']):
								serialized_data['csr_distance'] = i
								all_ports_csr[j]['sfp_distance'] = i
								j += 1

								# check if no signal
								if output['output'][index_fiber_csr]['RxPower'] == 'N/A' or output['output'][index_fiber_csr]['TxPower'] == 'N/A':
									no_signal = True

								# compare value
								if not no_signal:
									if threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][0] < output['output'][index_fiber_csr]['RxPower'] < threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][1]:
										rx_power = True
									if threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][0] < output['output'][index_fiber_csr]['TxPower'] < threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][1]:
										tx_power = True
									break

						if no_signal:
							output_message = f"Incomplete signal for fiber reading of CSR {element['type']} port {element['csr_port_down']} ie RxPower ({output['output'][index_fiber_csr]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_csr]['TxPower']}dBm)"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						# if rx_power = False and tx_power = False
						elif not rx_power and not tx_power:
							output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][1]}dBm"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						# if rx_power = True and tx_power = False
						elif rx_power and not tx_power:
							output_message = f"Fiber reading (TxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['TxPower'][1]}dBm"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						# if rx_power = False and tx_power = True
						elif not rx_power and tx_power:
							output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port_down']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[serialized_data['csr_distance']]['RxPower'][1]}dBm"
							log_text(filename=log_file, content=output_message,ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message
						
						# if both is True
						elif rx_power and tx_power:
							output_message = f"Fiber readings (RxPower ({output['output'][index_fiber_csr]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_csr]['TxPower']}dBm)) for CSR {element['type']} port {element['csr_port_down']} are within thresholds"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

				csr_output = {'csr': { 'power_reading': api_output }}
				api_output = dict()


				#                                                         #
				#              Compare SFP Type (AGG & CSR)               #
				#                                                         #

				for i in range(len(all_port)):
					if all_port[i]['sfp_distance'] == all_ports_csr[i]['sfp_distance']:
						output_message = f"SFP type for {all_port[i]['type']} port match for both AGG ({all_port[i]['sfp_distance']}) and CSR ({all_ports_csr[i]['sfp_distance']})"
						# log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						log_text(filename=log_file, content=output_message)
						api_output[all_port[i]['type']] = { 'status': True }
						api_output[all_port[i]['type']]['details'] = output_message

					else:
						output_message = f"SFP type for {all_port[i]['type']} port mismatch between AGG ({all_port[i]['sfp_distance']}) and CSR ({all_ports_csr[i]['sfp_distance']})"
						log_text(filename=log_file, content=output_message)
						api_output[all_port[i]['type']] = { 'status': False }
						api_output[all_port[i]['type']]['details'] = output_message

				csr_output['csr']['sfp'] = api_output
				agg_output['agg']['sfp'] = api_output
				api_output = dict()


				#                                                  #
				#              Verify LLDP neighbour               #
				#                                                  #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/show_lldp_neighbor_brief/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving lldp neighbor details for {serialized_data['csr_hostname'].upper()}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output[element['type']] = { 'status': 'undefined' }
					api_output[element['type']]['details'] = error_message
				
				else:
					for element in all_ports_csr:

						if re.search('huawei', serialized_data['agg_vendor'], re.IGNORECASE) and re.search('zte', serialized_data['csr_vendor'], re.IGNORECASE):
							# extract port number by removing port name
							if re.search(r"25ge", element['agg_port'], re.IGNORECASE):	# check if port is 25GE
								pattern = re.compile(r'25GE', re.IGNORECASE)
								agg_port_no = re.sub(pattern, '', element['agg_port'])
							else:
								agg_port_no = re.sub(r"[a-zA-Z]", "", element['agg_port'])

							# check if info ie agg hostname, agg port and csr port are exist
							if re.search(serialized_data['agg_hostname'], output['ne_response'], re.IGNORECASE) and re.search(agg_port_no, output['ne_response'], re.IGNORECASE) and re.search(element['csr_port_up'], output['ne_response'], re.IGNORECASE):
								output_message = f"AGG {element['type']} port ({element['agg_port']}) and AGG hostname ({serialized_data['agg_hostname']}) returned from LLDP neighbor configurations match with CSR {element['type']} port ({element['csr_port_up']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message
							else:
								output_message = f"AGG {element['type']} port ({element['agg_port']}) and AGG hostname ({serialized_data['agg_hostname']}) returned from LLDP neighbor configurations not match with CSR {element['type']} port ({element['csr_port_up']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

						else:

							match = False

							for i in range(len(output['output'])):
								if re.search(f"{element['csr_port_up']}$", output['output'][i]['local_interface'], re.IGNORECASE):
									index_lldp_csr = i
									if re.search(f"{serialized_data['agg_hostname']}$", output['output'][i]['system_name'], re.IGNORECASE) and re.search(f"{element['agg_port']}$", output['output'][i]['port_id'], re.IGNORECASE):
										match = True
										break

							if not match and 'index_lldp_csr' not in locals():
								output_message = f"CSR {element['type']} port returned from LLDP neighbor configurations does not match with default value {element['csr_port_up']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': 'undefined' }
								api_output[element['type']]['details'] = output_message

							elif not match:
								output_message = f"AGG {element['type']} port ({output['output'][index_lldp_csr]['port_id']}) and AGG hostname ({output['output'][index_lldp_csr]['system_name']}) returned from LLDP neighbor configurations mismatch with CSR {element['type']} port ({output['output'][index_lldp_csr]['local_interface']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							elif match:
								output_message = f"AGG {element['type']} port ({output['output'][index_lldp_csr]['port_id']}) and AGG hostname ({output['output'][index_lldp_csr]['system_name']}) returned from LLDP neighbor configurations match with CSR {element['type']} port ({output['output'][index_lldp_csr]['local_interface']})"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

				csr_output['csr']['lldp_neighbor'] = api_output
				api_output = dict()


				#                                                          #
				#              Verify Interface Description                #
				#                                                          #
				k = 0	# for multiple sfp distance
				for element in all_ports_csr:

					payload = {
						'csr_ip': serialized_data['csr_wan_ip'],
						'agg_ip': serialized_data['agg_loopback_ip'],
						'agg_hostname': serialized_data['agg_hostname'],
						'interface': f"xgei-{element['csr_port_up']}",
					}

					response = requests.get(url+'v1/via_agg/show_running_config_interface/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving port description for CSR {element['type']} port (xgei-{element['csr_port_up']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message
					
					else:
						port_description = output['output']['description']
						check_info = False
						check_distance = False
						sfp_distance = None
						csr_port = f"xgei-{element['csr_port_up']}"

						# check csr and agg hostname and port
						if re.search(csr_port, port_description, re.IGNORECASE) and re.search(serialized_data['csr_hostname'], port_description, re.IGNORECASE) and re.search(element['agg_port'], port_description, re.IGNORECASE) and re.search(serialized_data['agg_hostname'], port_description, re.IGNORECASE):
							check_info = True

						# check fiber info
						match = re.search(r'\[(.*?)\]', port_description)
						# check fiber info contains []
						if match:
							fiber_info, sfp_distance = match.group(1), None
							# standard fiber
							if not '|' in fiber_info:
								# if re.match(r'^.*[,_].*[,_].*$', fiber_info):	# must contain , and _
								if re.match(r"(?=.*[_])(?=.*[,])", fiber_info):	# must contain , and _
									if len(fiber_info.split(',')[1].strip().split('_')) > 1:	# check if there is _
										if re.match(r'^[0-9\.]+km$', fiber_info.split(',')[1].strip().split('_')[1].lower()):	# must contain number and km
											sfp_distance = round(float(re.sub('km', '', fiber_info.split(',')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)
								elif re.match(r"(?=.*[_])(?=.*[;])", fiber_info):	# must contain ; and _
									if len(fiber_info.split(';')[1].strip().split('_')) > 1:	# check if there is _
										if re.match(r'^[0-9\.]+km$', fiber_info.split(';')[1].strip().split('_')[1].lower()):	# must contain number and km
											sfp_distance = round(float(re.sub('km', '', fiber_info.split(';')[1].strip().split('_')[1], flags=re.IGNORECASE)), 3)

							else:
								# fiber junction
								# if not re.search(r'dwdm', fiber_info, re.IGNORECASE):
								# sfp_distance = 0
								# distance = list()
								split_fiber_info = fiber_info.split('|')
								for i in range(len(split_fiber_info)):
									# if re.match(r'^.*[,_].*[,_].*$', split_fiber_info[i].strip()):	# must contain , and _
									if re.match(r"(?=.*[_])(?=.*[,])", split_fiber_info[i].strip()):	# must contain , and _
										if len(split_fiber_info[i].strip().split(',')[1].split('_')) > 1:	# check if there is _
											if re.match(r'^[0-9\.]+km$', split_fiber_info[i].strip().split(',')[1].split('_')[1].lower()):	# must contain number and km
												if sfp_distance is None:
													sfp_distance = round(float(re.sub('km', '', split_fiber_info[i].strip().split(',')[1].split('_')[1], flags=re.IGNORECASE)), 3)
												else:
													sfp_distance += round(float(re.sub('km', '', split_fiber_info[i].strip().split(',')[1].split('_')[1], flags=re.IGNORECASE)), 3)
													sfp_distance = round(sfp_distance, 2)
									elif re.match(r"(?=.*[_])(?=.*[;])", split_fiber_info[i].strip()):	# must contain ; and _
										if len(split_fiber_info[i].strip().split(';')[1].split('_')) > 1:	# check if there is _
											if re.match(r'^[0-9\.]+km$', split_fiber_info[i].strip().split(';')[1].split('_')[1].lower()):	# must contain number and km
												if sfp_distance == 0:
													sfp_distance = round(float(re.sub('km', '', split_fiber_info[i].strip().split(';')[1].split('_')[1], flags=re.IGNORECASE)), 3)
												else:
													sfp_distance += round(float(re.sub('km', '', split_fiber_info[i].strip().split(';')[1].split('_')[1], flags=re.IGNORECASE)), 3)
													sfp_distance = round(sfp_distance, 2)

								# dwdm
								# else:
								# 	split_fiber_info = fiber_info.split('|')
								# 	for i in range(len(split_fiber_info)):
								# 		if re.match(r'^.*[,_].*[,_].*$', split_fiber_info[0].strip()):	# must contain , and _
								# 			if len(split_fiber_info[0].strip().split(',')[1].split('_')) > 1:	# check if there is _
								# 				if re.match(r'^[0-9\.]+km$', split_fiber_info[0].strip().split(',')[1].split('_')[1].lower()):	# must contain number and km
								# 					sfp_distance = round(float(re.sub('km', '', split_fiber_info[0].strip().split(',')[1].split('_')[1], flags=re.IGNORECASE)), 3)
						# else:
						# 	output_message = f"The CSR port description ({port_description}) for {element['csr_port_up']} does not contain fiber details"
						# 	log_text(filename=log_file, content=output_message,ne_output=output['ne_response'])
						# 	api_output[element['type']] = { 'status': 'undefined' }
						# 	api_output[element['type']]['details'] = output_message
						
						fiber_reading_range = {
							"10km": [0, 10],
							"40km": [10.1, 40],
							"80km": [40.1, 80],
						}

						#  verify sfp distance
						if 'sfp_distance' is not None and isinstance(sfp_distance, float):
							if fiber_reading_range[all_ports_csr[k]['sfp_distance']][0] <= sfp_distance <= fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]:
								check_distance = True

							if check_info and check_distance:
								output_message = f"The CSR port description ({port_description}) for port {element['csr_port_up']} contains complete information and the specified distance ({sfp_distance}km) is within the range {fiber_reading_range[all_ports_csr[k]['sfp_distance']][0]}km to {fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]}km for SFP type {all_ports_csr[k]['sfp_distance']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message

							else:
								if not check_info and not check_distance:
									output_message = f"Missing/mismatch required info (AGG hostname, AGG port and CSR hostname) and the fiber distance ({sfp_distance}km) specified in description ({port_description}) for port {element['csr_port_up']} is not within range {fiber_reading_range[all_ports_csr[k]['sfp_distance']][0]}km to {fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]}km for SFP type {all_ports_csr[k]['sfp_distance']}"
								
								elif not check_info and check_distance:
									output_message = f"Missing/mismatch required info (AGG hostname, AGG port and CSR hostname) in port description ({port_description}) for port {element['csr_port_up']}"

								elif check_info and not check_distance:
									output_message = f"The fiber distance ({sfp_distance}km) specified in description ({port_description}) for port {element['csr_port_up']} is not within range {fiber_reading_range[all_ports_csr[k]['sfp_distance']][0]}km to {fiber_reading_range[all_ports_csr[k]['sfp_distance']][1]}km for SFP type {all_ports_csr[k]['sfp_distance']}"

								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message

							k += 1
						
						else:
							output_message = f"Invalid fiber info format or no sfp distance specified in description ({port_description}) for port {element['csr_port_up']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

				csr_output['csr']['port_description'] = api_output
				api_output = dict()


				#                                                     #
				#              Verify CRC error packet                #
				#                                                     #
				for element in all_ports_csr:

					payload = {
						'csr_ip': serialized_data['csr_wan_ip'],
						'agg_ip': serialized_data['agg_loopback_ip'],
						'agg_hostname': serialized_data['agg_hostname'],
						'port': f"xgei-{element['csr_port_up']}",
					}

					response = requests.get(url+'v1/via_agg/show_port_crc/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving crc reading for CSR {element['type']} port (xgei-{element['csr_port_up']})"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output[element['type']] = { 'status': 'undefined' }
						api_output[element['type']]['details'] = error_message
					
					else:
						if output['output'] == "There is error returned from NE":
							output_message = f"There is no matching CSR {element['type']} port {element['csr_port_up']} configuration returned while checking crc reading"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						else:
							# check if crc = 0 
							if output['output']['In_CRC_ERROR'] == 0:
								output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port_up']} is {output['output']['In_CRC_ERROR']}"
								log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': True }
								api_output[element['type']]['details'] = output_message
								# api_output[element['type']]['crc_reading'] = output['output']['In_CRC_ERROR']

							else:
								output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port_up']} is greater than zero ({output['output']['In_CRC_ERROR']})"
								log_text(filename=log_file, content=output_message,ne_output=output['ne_response'])
								api_output[element['type']] = { 'status': False }
								api_output[element['type']]['details'] = output_message
								# api_output[element['type']]['crc_reading'] = output['output']['In_CRC_ERROR']

				csr_output['csr']['crc'] = api_output
				api_output = dict()

				#                                               #
				#              Verify BFD session               #
				#                                               #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/show_bfd_neighbor_all_brief/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving bfd session configurations for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					if len(all_ports_csr) == 2:
						api_output['main'] = { 'status': 'undefined' }
						api_output['main']['details'] = error_message
						api_output['protect'] = { 'status': 'undefined' }
						api_output['protect']['details'] = error_message

					else:
						api_output['main'] = { 'status': 'undefined' }
						api_output['main']['details'] = error_message
				
				else:
					# verify main and protect port
					for element in all_ports_csr:

						up, index_bfd_csr = False, None
						# determine index of matching output ie port and state
						for i in range(len(output['output'])):
							if serialized_data['csr_wan_ip'] == output['output'][i]['local'] and re.search(f"{element['csr_port_up']}$", output['output'][i]['interface']):
								index_bfd_csr = i
								if re.search('up', output['output'][i]['state'], re.IGNORECASE):
									up = True
								break

						if not up and index_bfd_csr is None:
							output_message = f"No matching CSR {element['type']} port ({element['csr_port_up']}) and CSR WAN IP Address ({serialized_data['csr_wan_ip']}) in the returned BFD session configurations"
							log_text(filename=log_file, content=output_message,
									ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						elif not up:
							output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port_up']}) and CSR WAN IP Address ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd_csr]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						elif up:
							output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port_up']}) and CSR WAN IP Address ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd_csr]['state']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

					# verify port virtual 
					virtual_up = False
					# determine index of matching output ie port and state
					for i in range(len(output['output'])):
						if serialized_data['csr_wan_ip'] == output['output'][i]['local'] and re.search(f"smartgroup1", output['output'][i]['interface'], re.IGNORECASE):
							index_bfd_virtual_csr = i
							if re.search('up', output['output'][i]['state'], re.IGNORECASE):
								virtual_up = True
							break

					if not virtual_up and "index_bfd_virtual_csr" not in locals():
						output_message = f"No matching CSR port (smartgroup1) and CSR WAN IP Address ({serialized_data['csr_wan_ip']}) in the returned BFD session configurations"
						log_text(filename=log_file, content=output_message,
								ne_output=output['ne_response'])
						api_output['virtual'] = { 'status': 'undefined' }
						api_output['virtual']['details'] = output_message

					elif not virtual_up:
						output_message = f"BFD session state for CSR port (smartgroup1) and CSR WAN IP Address ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd_virtual_csr]['state']}"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['virtual'] = { 'status': False }
						api_output['virtual']['details'] = output_message

					elif virtual_up:
						output_message = f"BFD session state for CSR port (smartgroup1) and CSR WAN IP Address ({serialized_data['csr_wan_ip']}) is {output['output'][index_bfd_virtual_csr]['state']}"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['virtual'] = { 'status': True }
						api_output['virtual']['details'] = output_message

				csr_output['csr']['bfd'] = api_output
				api_output = dict()

				#                                                  #
				#              Verify license status               #
				#                                                  #
				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
					'filter': 'xgei',
				}

				response = requests.get(url+'v1/via_agg/show_license_port_brief/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving license status for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output = { 'status': 'undefined' }
					api_output['details'] = error_message
				
				else:
					for element in all_ports_csr:

						activated, index_license_csr = False, None

						for i in range(len(output['output'])):
							if re.search(f"{element['csr_port_up']}$", output['output'][i]['interface'], re.IGNORECASE):
								index_license_csr = i
								if re.search(r"activated", output['output'][i]['status'], re.IGNORECASE) and re.search(r"matched", output['output'][i]['permit_status'], re.IGNORECASE):
									activated = True
								break

						if not activated and index_license_csr is None:
							output_message = f"There is no license/permit status for CSR {element['type']} port ({element['csr_port_up']})"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': 'undefined' }
							api_output[element['type']]['details'] = output_message

						elif not activated:
							output_message = f"The license/permit status for CSR {element['type']} port ({element['csr_port_up']}) is either not activated ({output['output'][index_license_csr]['status']}) or not matched ({output['output'][index_license_csr]['permit_status']})"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': False }
							api_output[element['type']]['details'] = output_message

						elif activated:
							output_message = f"The license and permit status for CSR {element['type']} port ({element['csr_port_up']}) is {output['output'][index_license_csr]['status']} and {output['output'][index_license_csr]['permit_status']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output[element['type']] = { 'status': True }
							api_output[element['type']]['details'] = output_message

					# for downlink
					activated, index_license_csr = False, None

					for i in range(len(output['output'])):
						if re.search(f"1/1/0/3$", output['output'][i]['interface'], re.IGNORECASE):
							index_license_csr = i
							# if re.search(r"activated", output['output'][i]['status'], re.IGNORECASE) and re.search(r"matched", output['output'][i]['permit_status'], re.IGNORECASE):
							if re.search(r"activated", output['output'][i]['status'], re.IGNORECASE) and re.match(r"matched", output['output'][i]['permit_status'], re.IGNORECASE):      # modified to re.match on 26 Oct 2023 to cater logic to exclude overallocation-matched
								activated = True
							break

					if not activated and index_license_csr is None:
						output_message = f"There is no license/permit status for CSR downlink port (1/1/0/3)"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['downlink'] = { 'status': 'undefined' }
						api_output['downlink']['details'] = output_message

					elif not activated:
						output_message = f"The license/permit status for CSR downlink port (1/1/0/3) is either not activated ({output['output'][index_license_csr]['status']}) or not matched ({output['output'][index_license_csr]['permit_status']})"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['downlink'] = { 'status': False }
						api_output['downlink']['details'] = output_message

					elif activated:
						output_message = f"The license and permit status for CSR downlink port (1/1/0/3) is {output['output'][index_license_csr]['status']} and {output['output'][index_license_csr]['permit_status']}"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output['downlink'] = { 'status': True }
						api_output['downlink']['details'] = output_message

				csr_output['csr']['license'] = api_output
				api_output = bool()

				#                                                                                                  #
				#              Verify CSR version (in Production Trial, uncommented on 2 Oct 2023)                 #
				#                                                                                                  #
				check_version = False

				payload = {
					'csr_ip': serialized_data['csr_wan_ip'],
					'agg_ip': serialized_data['agg_loopback_ip'],
					'agg_hostname': serialized_data['agg_hostname'],
				}

				response = requests.get(url+'v1/via_agg/show_version/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = f"An error has occured while retrieving CSR version for {serialized_data['csr_hostname']}"
					output_message = error_message + \
						'\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					api_output = { 'status': 'undefined' }
					api_output['details'] = error_message
				
				else:
					if '6120H-S V5.10.10.30' in output['output']['software_version']:
						output_message = f"The software version {output['output']['software_version']} does not require hot patch"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						api_output = { 'status': True }
						api_output['details'] = output_message
						check_version = True
					
					else:
						output_message = f"The software version {output['output']['software_version']} require hot patch"
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

				if not check_version:	# if not match allowed versions, proceed to check patch version

					#                                                 #
					#              Verify patch version               #
					#                                                 #
					payload = {
						'csr_ip': serialized_data['csr_wan_ip'],
						'agg_ip': serialized_data['agg_loopback_ip'],
						'agg_hostname': serialized_data['agg_hostname'],
					}

					response = requests.get(url+'v1/via_agg/show_patch_effective_brief/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = f"An error has occured while retrieving patch version for {serialized_data['csr_hostname']}"
						output_message = error_message + \
							'\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						api_output = { 'status': 'undefined' }
						api_output['details'] = error_message
					
					else:
						if len(output['output']) != 4:
							output_message = f"The software version is invalid i.e. only {len(output['output'])} patch id available for {serialized_data['csr_hostname']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': False }
							api_output['details'] = output_message
						
						else:
							output_message = f"The sofware version is valid with {len(output['output'])} patch id for {serialized_data['csr_hostname']}"
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							api_output = { 'status': True }
							api_output['details'] = output_message

				csr_output['csr']['software'] = api_output

			final_output.append(agg_output)
			final_output.append(csr_output)

			#                                                #
			#              OUTPUT FINAL MESSAGE              #
			#                                                #

			if 'undefined' in str(final_output) or 'False' in str(final_output):
				output_message = f"Successfully run verifications on AGG and CSR but some of the verification items contains errors"
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': output_message,
					'api_output': str(final_output),
					'status': 'failed',
					'log_file': log_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = final_output
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_200_OK)

			else:
				output_message = f"Successfully run verifications on AGG and CSR and all verification items passed"
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': output_message,
					'api_output': str(final_output),
					'status': 'success',
					'log_file': log_file,
					'user': user
				}
				api_response['status'] = 'success'
				api_response['message'] = output_message
				api_response['data'] = final_output
				api_response['log_file'] = log_file
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_200_OK)


		except Exception as error:
			output_error = list()
			exc_type, exc_obj, exc_tb = sys.exc_info()
			output_error.append(str(exc_type))
			output_error.append(str(exc_obj))
			output_error.append(str(exc_tb.tb_lineno))

			# out_error = dict()
			# out_error = {
			# 	'error_type': str(exc_type),
			# 	'error_details': str(exc_obj),
			# 	'error_line': str(exc_tb.tb_lineno),
			# }

			# if 'output' in locals() and 'output' in output:
			# 	out_error.update(output['output'])

			error_message = 'Unexpected error has occured while executing API'
			output_message = error_message + \
				'\n[error message: {}]'.format(error)
			log_text(filename=log_file, content=output_message, ne_output=dumps(output_error, indent=4, separators=(',', ': ')))
			DB_data = {
				'order_type': 'N/A',
				'service_type': '5G',
				'input_details': request.data,
				'message': error_message,
				'api_output': str(error),
				'status': 'failed',
				'log_file': log_file,
				'config_file': 'Undefined',
				'user': user
			}
			api_response['status'] = 'failed'
			api_response['message'] = error_message
			# api_response['data'] = str(error)
			api_response['data'] = output_error
			if 'log_file' in locals():
				api_response['log_file'] = log_file
			else:
				api_response['log_file'] = ''
			log_database(data_DB=DB_data)
			return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
