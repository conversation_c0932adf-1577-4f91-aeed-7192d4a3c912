from rest_framework.views import APIView  # activate REST //post,get,put,delete
# return Response JSON associative array
from rest_framework.response import Response
from rest_framework import status  # error status code 404
from api.serializers import L2TbuSerializer
import requests
import time
import re
import sys
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from api.libs import log_text, log_database
from json import dumps


class API(APIView):

    # authentication_classes = ()  # exclude from global authentication
    # permission_classes = ()

    @swagger_auto_schema(
        request_body=L2TbuSerializer,
        responses={201: L2TbuSerializer()},
        operation_summary="Workflow for Temporary Bandwidth Upgrade layer 2 NE for 1GOV (support Huawei and ALU)"
    )
    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(
                    key=token.replace('Token ', '')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = L2TbuSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': request.data,
                    'message': output_message,
                    'api_output': str(input_data.errors),
                    'status': 'failed',
                    'log_file': 'N/A',
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = '1GOV_TBU_{}_{}_{}@{}_by_{}.txt'.format(
                serialized_data['service_id'], serialized_data['node_name'], date_now, time_now, user)		# Create Log File

            if re.search('npe', serialized_data['node_name'], re.IGNORECASE):
                serialized_data['network_element'] = 'NPE'
            elif re.search('epe', serialized_data['node_name'], re.IGNORECASE):
                serialized_data['network_element'] = 'EPE'
            elif re.search('agg', serialized_data['node_name'], re.IGNORECASE):
                serialized_data['network_element'] = 'AGG'
            else:
                serialized_data['network_element'] = 'Undefined'

             # LOG CREATED TIME #
            output_message = "File created at {} {}".format(date_now, time_now)
            log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(
                request.data, indent=4, separators=(',', ': ')))

            #                                             #
            #              GET LOOPBACK IP                #
            #                                             #
            payload = {
                'search': serialized_data['node_name'],
            }
            response = requests.get(
                'http://pisa-workflow-svc/workflow/database/v1/network_element/', params=payload, verify=False)
            output = response.json()

            # check if api returns network element details
            if len(output['network_element']) == 0:
                output_message = "No Loopback IP for NE ({}) returned from PISA Database".format(
                    serialized_data['node_name'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': output_message,
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output_message
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            serialized_data['device_id'] = output['network_element'][0]['loopbackIp']
            serialized_data['ne_type'] = output['network_element'][0]['neType']

            output_message = 'Successfully retrieved NE Loopback IP ({}) from PISA API'.format(
                serialized_data['device_id'])
            log_text(filename=log_file, ne_output=dumps(
                output['network_element'][0], indent=4, separators=(',', ': ')), content=output_message)

            #                                            #
            #              CHECK VPN NAME                #
            #                                            #
            if not re.search('gov_', serialized_data['vpn_name'], re.IGNORECASE):
                output_message = "Invalid VPN name ({}). Missing GOV_".format(
                    serialized_data['vpn_name'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': output_message,
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output_message
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            #                                                                    #
            #              CHECK IF VENDOR IS NOT HUAWEI  & NOKIA                #
            #                                                                    #

            if not re.search('huawei', output['network_element'][0]['vendor'], re.IGNORECASE) and not re.search('nokia', output['network_element'][0]['vendor'], re.IGNORECASE):
                output_message = f"{serialized_data['ne_type'].upper()}'s vendor ({output['network_element'][0]['vendor']}) is not allowed."
                log_text(filename=log_file, ne_output=dumps(
                    output['network_element'][0], indent=4, separators=(',', ': ')), content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': output_message,
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output_message
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)



            #                                             #
            #              LOGIC FOR NOKIA                #
            #                                             #

            if re.search('nokia', output['network_element'][0]['vendor'], re.IGNORECASE):

                # declare main url variable
                url = 'http://pisa-nokia-svc/nokia/npe_epe/'

                #                                            #
                #              VERIFY SYSNAME                #
                #                                            #

                serialized_data['port'] = (serialized_data['interface'].split('.')[
                        0]).lower().replace('port', '').strip()
                vlan = (serialized_data['interface'].split('.')[
                        1]).lower().replace('port', '').strip()
                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'port': serialized_data['port']
                }

                response = requests.get(url+'v1/show_port_lldp_remote_info', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving lldp remote info for port ({serialized_data['port']})"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                
                else:
                    if output['output'] == "There is no output returned from NE":
                        output_message = f"There is no lldp remote info returned for port ({serialized_data['port']})"
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

                    elif output['output'] == "There is error returned from NE":
                        output_message = f"{output['output']} while retrieving lldp remote info for port ({serialized_data['port']})"
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': output_message,
                            'api_output': output_message,
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = output_message
                        api_response['data'] = output_message
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    
                    else:
                        if re.search(serialized_data['sys_name'], output['output']['SysName'], re.IGNORECASE):
                            output_message = f"The remote lldp for port ({serialized_data['port']}) SysName ({output['output']['SysName']}) match the provided SysName ({serialized_data['sys_name']})"
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                        else:
                            output_message = f"Mismatch remote lldp for port ({serialized_data['port']}) SysName ({output['output']['SysName']}) with the provided SysName ({serialized_data['sys_name']})"
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                            DB_data = {
                                'order_type': 'TBU',
                                'service_type': '1GOV',
                                'input_details': serialized_data,
                                'message': output_message,
                                'api_output': output_message,
                                'status': 'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = output_message
                            log_database(data_DB=DB_data)
                            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                #                                             #
                #        CHECK PORT ASSOCIATE TO A LAG        #
                #                                             #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'port': serialized_data['port']
                }
                response = requests.get(url+'v1/show_port', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving port ({serialized_data['port']})"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    if re.search('lag', output['output']['OperState'], re.IGNORECASE):
                        output_message = f"The port ({serialized_data['port']}) is associated to lag (lag-{(output['output']['OperState']).lower().split('lag')[1].strip()})"
                        serialized_data['port'] = f"lag-{(output['output']['OperState']).lower().split('lag')[1].strip()}"
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'][0:500])

                #                              #
                #        GET SERVICE ID        #
                #                              #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'port': serialized_data['port']
                }
                response = requests.get(url+'v1/show_service_sap', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving port ({serialized_data['port']}) service id"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    if output['output'] == "There is no output returned from NE":
                        error_message = f"No service sap info returned for port {serialized_data['port']}"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    
                    else:
                        serialized_data['metroe_service_id'] = str()
                        serialized_data['interface'] = f"{serialized_data['port']}:{vlan}"
                        for element in output['output']:
                            if element['PortId'] == serialized_data['interface']:
                                serialized_data['metroe_service_id'] = element['SvcId']

                        if serialized_data['metroe_service_id'] != '':
                            output_message = f"Successfully retrieved service id ({serialized_data['metroe_service_id']}) for interface ({serialized_data['interface']})"
                            log_text(filename=log_file, content=output_message,
                                    ne_output=output['ne_response'])
                            
                        else:
                            output_message = f"No match service id for interface ({serialized_data['interface']})"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            DB_data = {
                                'order_type': 'TBU',
                                'service_type': '1GOV',
                                'input_details': serialized_data,
                                'message': output_message,
                                'api_output': output_message,
                                'status': 'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = output_message
                            log_database(data_DB=DB_data)
                            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                #                                #
                #        GET SERVICE TYPE        #
                #                                #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'metroe_service_id': serialized_data['metroe_service_id']
                }
                response = requests.get(url+'v1/show_service_id', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving service id ({serialized_data['metroe_service_id']}) service type"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    if output['output'] == "There is no output returned from NE":
                        error_message = f"No service id info returned for service id ({serialized_data['metroe_service_id']})"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    
                    else:
                        serialized_data['service_type'] = output['output']['ServiceType']
                        output_message = f"Successfully retrieved service type ({serialized_data['service_type']}) for service id ({serialized_data['metroe_service_id']})"
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'][0:500])

                #                               #
                #        GET EXISTING BW        #
                #                               #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'metroe_service_id': serialized_data['metroe_service_id'],
                    'service_type': serialized_data['service_type'],
                    'interface': serialized_data['interface'].replace(':', '.')
                }
                response = requests.get(
                    url+'v1/show_service_cos_info', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving COS info for service id ({serialized_data['metroe_service_id']}) and interface ({serialized_data['interface']})"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    if output['output'] == "There is no output returned from NE":
                        error_message = f"There is no COS info returned for service id ({serialized_data['metroe_service_id']}) and interface ({serialized_data['interface']})"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                    else:
                        # convert old bw to kbps
                        if re.findall('k', serialized_data['old_bandwidth'], re.IGNORECASE):
                            serialized_data['rate_limit'] = int(
                                re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1
                        elif re.findall('m', serialized_data['old_bandwidth'], re.IGNORECASE):
                            serialized_data['rate_limit'] = int(
                                re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000
                        elif re.findall('g', serialized_data['old_bandwidth'], re.IGNORECASE):
                            serialized_data['rate_limit'] = int(
                                re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000000

                        # convert new bw to kbps
                        if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
                            serialized_data['new_rate_limit'] = int(
                                re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1
                        elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
                            serialized_data['new_rate_limit'] = int(
                                re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000
                        elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
                            serialized_data['new_rate_limit'] = int(
                                re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000000

                        #                                           #
                        #        COMPARE EXISTING RATE LIMIT        #
                        #                                           #

                        # compare existing rate limit with new bandwidth
                        if output['output']['scheduler']['rate'] == str(serialized_data['new_rate_limit']):
                            output_message = f"The existing rate limit ({output['output']['scheduler']['rate']}) match the new bandwith rate limit ({serialized_data['new_rate_limit']}). No change is required"
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                            DB_data = {
                                'order_type': 'TBU',
                                'service_type': '1GOV',
                                'input_details': serialized_data,
                                'message': output_message,
                                'api_output': 'No error',
                                'status': 'success',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'success'
                            api_response['message'] = output_message
                            api_response['data'] = 'No error'
                            log_database(data_DB=DB_data)
                            return Response(api_response, status=status.HTTP_200_OK)

                        # compare existing rate limit with old bandwidth
                        elif output['output']['scheduler']['rate'] != str(serialized_data['rate_limit']):
                            output_message = f"The existing rate limit ({output['output']['scheduler']['rate']}) mismatch with the old bandwidth rate limit ({serialized_data['rate_limit']})"
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                            DB_data = {
                                'order_type': 'TBU',
                                'service_type': '1GOV',
                                'input_details': serialized_data,
                                'message': output_message,
                                'api_output': output_message,
                                'status': 'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = output_message
                            log_database(data_DB=DB_data)
                            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                        else:
                            output_message = f"The existing rate limit ({output['output']['scheduler']['rate']}) match with the old bandwidth rate limit ({serialized_data['rate_limit']})"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

                #                                                      #
                #          GENERATE L2 1GOV TBU CONFIGURATION          #
                #                                                      #

                config_file = '{}_1GOV_TBU_{}_{}_{}@{}_by_{}'.format(serialized_data['ne_type'].upper(
                ), serialized_data['service_id'], serialized_data['node_name'],  date_now, time_now, user)  # create config file

                payload = {
                    'filename': config_file,
                    'metroe_service_id': serialized_data['metroe_service_id'],
                    'service_type': serialized_data['service_type'].lower(),
                    'interface': serialized_data['interface'],
                    'bandwidth': serialized_data['new_bandwidth'],
                }

                response = requests.post(
                    url+'v1/1gov/generate_config_modify_bw/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while generating configurations'
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['data']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = f"{output['output']} ({config_file}.txt)"
                    log_text(filename=log_file, content=output_message)

                #                                                    #
                #          LOAD THE GENERATED CONFIGURATION          #
                #                                                    #
                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'filename': config_file
                }

                response = requests.post(
                    url+'v1/load_config/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while loading the generated configurations to {serialized_data['network_element']}"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] != 'Successfully loaded configurations to NPE/EPE':
                        error_message = 'Configurations loading process is unsuccessful'
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        output_message = f"Successfully loaded the generated configurations at {serialized_data['network_element']}"
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': output_message,
                            'api_output': 'No error',
                            'status': 'success',
                            'log_file': log_file,
                            'config_file': config_file,
                            'user': user
                        }
                        api_response['status'] = 'success'
                        api_response['message'] = output_message
                        api_response['data'] = 'No error'
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_200_OK)



            #                                              #
            #              LOGIC FOR HUAWEI                #
            #                                              #

            if re.search('huawei', output['network_element'][0]['vendor'], re.IGNORECASE):

                # declare main url variable
                url = 'http://pisa-huawei-svc/huawei/npe_epe/'

                #                                            #
                #              GET NE VERSION                #
                #                                            #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                }
                response = requests.get(
                    url+'v1/show_version/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while retrieving NE ({}) version'.format(
                        serialized_data['node_name'])
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] == 'Software version is not found':
                        error_message = 'Software version is not found in NE ({})'.format(
                            serialized_data['node_name'])
                        log_text(filename=log_file, content=error_message)
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        version_details = output['output']

                        # assign version to a variable
                        if re.search('v6', version_details, re.IGNORECASE):
                            serialized_data['ne_version'] = 'V6'
                        elif re.search('v8', version_details, re.IGNORECASE):
                            serialized_data['ne_version'] = 'V8'

                        output_message = 'Successfully retrieved software version ({}) for the NE ({}) and identified the NE as version ({})'.format(
                            version_details, serialized_data['node_name'], serialized_data['ne_version'])
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'][0:300])

                #                                            #
                #              VERIFY SYSNAME                #
                #                                            #

                # test data - GigabitEthernet9/0/1 (has SysName: UPE10GE-01), GigabitEthernet1/0/0 (0 neighbor) and Eth-Trunk1 ()

                port = serialized_data['interface'].split('.')[0]
                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'port': port
                }
                response = requests.get(
                    url+'v1/show_lldp_neighbor_interface/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed' or output['output'] == "No output returned from NE":
                    error_message = 'An error has occured while retrieving lldp neighbor details for port ({})'.format(
                        port)
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output']['neighbor'] == 0:
                        output_message = "There is 0 neighbor returned for lldp for port ({})".format(
                            port)
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])
                    else:
                        if re.search(serialized_data['sys_name'], output['output']['SysName'], re.IGNORECASE):
                            output_message = "The neighbor lldp for port ({}) SysName ({}) match the provided SysName ({})".format(
                                port, output['output']['SysName'], serialized_data['sys_name'])
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                        else:
                            output_message = "Mismatch neighbor lldp for port ({}) SysName ({}) with the provided SysName ({})".format(
                                port, output['output']['SysName'], serialized_data['sys_name'])
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                            DB_data = {
                                'order_type': 'TBU',
                                'service_type': '1GOV',
                                'input_details': serialized_data,
                                'message': output_message,
                                'api_output': output_message,
                                'status': 'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = output_message
                            log_database(data_DB=DB_data)
                            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                #                               #
                #        GET QOS PROFILE        #
                #                               #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'interface': serialized_data['interface']
                }
                response = requests.get(
                    url+'v1/show_config_interface/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while retrieving interface ({})'.format(
                        serialized_data['interface'])
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    # check if qos profile exist in the output
                    if not 'qos_profile_inbound' in output['output']:
                        output_message = "QOS profile does not exist under interface ({})".format(
                            serialized_data['interface'])
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': output_message,
                            'api_output': output_message,
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = output_message
                        api_response['data'] = output_message
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                    else:
                        if not "Classi" in output['output']['qos_profile_inbound']:
                            output_message = f"Invalid interface ({serialized_data['interface']}) QOS profile ({output['output']['qos_profile_inbound']}) name"
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])
                            DB_data = {
                                'order_type': 'TBU',
                                'service_type': '1GOV',
                                'input_details': serialized_data,
                                'message': output_message,
                                'api_output': output_message,
                                'status': 'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = output_message
                            log_database(data_DB=DB_data)
                            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                        else:
                            # assigned variable to qos profile
                            serialized_data['qos_profile'] = output['output']['qos_profile_inbound']
                            output_message = f"Successfully retrieved interface ({serialized_data['interface']}) QOS profile ({output['output']['qos_profile_inbound']})"
                            log_text(filename=log_file, content=output_message,
                                     ne_output=output['ne_response'])

                #                                      #
                #        GET QOS PROFILE DETAILS       #
                #                                      #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'qos_profile': serialized_data['qos_profile']
                }
                response = requests.get(
                    url+'v1/show_config_qos_profile/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving qos profile ({serialized_data['interface']}) details"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                else:

                    # convert old bw to kbps
                    if re.findall('k', serialized_data['old_bandwidth'], re.IGNORECASE):
                        serialized_data['rate_limit'] = int(
                            re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1
                    elif re.findall('m', serialized_data['old_bandwidth'], re.IGNORECASE):
                        serialized_data['rate_limit'] = int(
                            re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000
                    elif re.findall('g', serialized_data['old_bandwidth'], re.IGNORECASE):
                        serialized_data['rate_limit'] = int(
                            re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000000

                    # convert new bw to kbps
                    if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
                        serialized_data['new_rate_limit'] = int(
                            re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1
                    elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
                        serialized_data['new_rate_limit'] = int(
                            re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000
                    elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
                        serialized_data['new_rate_limit'] = int(
                            re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000000

                    #                                           #
                    #        COMPARE EXISTING RATE LIMIT        #
                    #                                           #

                    if output['output']['user-queue']['cir'] == str(serialized_data['new_rate_limit']) or output['output']['user-queue']['pir'] == str(serialized_data['new_rate_limit']):
                        output_message = f"QOS profile ({serialized_data['qos_profile']}) cir ({output['output']['user-queue']['cir']}) or pir ({output['output']['user-queue']['pir']}) match new bandwith rate limit ({serialized_data['new_rate_limit']}). No change is required"
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': output_message,
                            'api_output': 'No error',
                            'status': 'success',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'success'
                        api_response['message'] = output_message
                        api_response['data'] = 'No error'
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_200_OK)

                    elif output['output']['user-queue']['cir'] != str(serialized_data['rate_limit']) or output['output']['user-queue']['pir'] != str(serialized_data['rate_limit']):
                        output_message = f"Mismatch rate limit ({serialized_data['rate_limit']}) with QOS profile ({serialized_data['qos_profile']}) cir ({output['output']['user-queue']['cir']}) or pir ({output['output']['user-queue']['pir']})"
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': output_message,
                            'api_output': output_message,
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = output_message
                        api_response['data'] = output_message
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                    else:

                        output_message = f"Rate limit ({serialized_data['rate_limit']}) match with QOS profile ({serialized_data['qos_profile']}) cir ({output['output']['user-queue']['cir']}) or pir ({output['output']['user-queue']['pir']})"
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])

                #                                                      #
                #          GENERATE L2 1GOV TBU CONFIGURATION          #
                #                                                      #
                config_file = '{}_1GOV_TBU_{}_{}_{}@{}_by_{}'.format(serialized_data['ne_type'].upper(
                ), serialized_data['service_id'], serialized_data['node_name'],  date_now, time_now, user)  # create config file

                # Generate new qos profile name
                serialized_data['new_qos_profile'] = f"{serialized_data['qos_profile'].split('_')[0]}_{serialized_data['qos_profile'].split('_')[1]}_{serialized_data['qos_profile'].split('_')[2]}_{ serialized_data['new_bandwidth'].upper()}"

                payload = {
                    'filename': config_file,
                    'qos_profile_name': serialized_data['new_qos_profile'],
                    'interface': serialized_data['interface'],
                    'bandwidth': serialized_data['new_bandwidth'],
                }

                response = requests.post(
                    url+'v1/1gov/generate_config_modify_bw/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while generating configurations'
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['data']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = f"{output['output']} ({config_file}.txt)"
                    log_text(filename=log_file, content=output_message)

                #                                                    #
                #          LOAD THE GENERATED CONFIGURATION          #
                #                                                    #
                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'filename': config_file
                }

                if int(re.sub(r'\D', '', serialized_data['ne_version'])) > 6:
                    payload['commit'] = True
                else:
                    payload['commit'] = False

                response = requests.post(
                    url+'v1/load_config/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while loading the generated configurations to {serialized_data['network_element']}"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] != 'Successfully loaded configurations to NPE/EPE':
                        error_message = 'Configurations loading process is unsuccessful'
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message,
                                 ne_output=output['ne_response'])
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        output_message = f"Successfully loaded the generated configurations at {serialized_data['network_element']}"
                        log_text(
                            filename=log_file, content=output['output'], ne_output=output['ne_response'])

                #                                 #
                #        POST VERIFICATION        #
                #                                 #

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'interface': serialized_data['interface']
                }
                response = requests.get(
                    url+'v1/show_config_interface/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while retrieving interface ({})'.format(
                        serialized_data['interface'])
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    #                                                #
                    #              OUTPUT FINAL MESSAGE              #
                    #                                                #
                    output_message = f"Successfully provisioned new bandwidth at {serialized_data['network_element']} and run post-verification command"
                    log_text(
                        filename=log_file, content=output_message, ne_output=output['ne_response'])
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': output_message,
                        'api_output': 'No error',
                        'status': 'success',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'success'
                    api_response['message'] = output_message
                    api_response['data'] = 'No error'
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_200_OK)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            log_text(filename=log_file, content=output_message)
            DB_data = {
                'order_type': 'TBU',
                'service_type': '1GOV',
                'input_details': request.data,
                'message': error_message,
                'api_output': str(error),
                'status': 'failed',
                'log_file': log_file,
                'config_file': 'Undefined',
                'user': user
            }
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            # api_response['data'] = str(error)
            api_response['data'] = output_error
            log_database(data_DB=DB_data)
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
