from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import L3ServiceTestingv2SelfServeSerializer
import requests, json, time, re, os, sys
import threading
import ipaddress
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps
from concurrent.futures import ThreadPoolExecutor

class API(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        request_body=L3ServiceTestingv2SelfServeSerializer,
        responses={201: L3ServiceTestingv2SelfServeSerializer()},
        operation_summary="Workflow for Self Serve Application for both L3 Service Testing"
    )

    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = L3ServiceTestingv2SelfServeSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            
            # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')

            combined_response = []

            def is_ipv4(ip):
                try:
                    return type(ipaddress.ip_address(ip)) is ipaddress.IPv4Address
                except ValueError:
                    return False

            def is_ipv6(ip):
                try:
                    return type(ipaddress.ip_address(ip)) is ipaddress.IPv6Address
                except ValueError:
                    return False

            #Check Interface Status (Juniper) 
            def fetch_interface_status_juniper(serialized_data, combined_response):
                try:
                    interface_status = {
                        'interface_status': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }
                    interface_status['interface_status'] = True

                    combined_ne_response = ""
                    pe_interface = serialized_data['pe_interface'].split(".")[0]
                    command = f'show interfaces {pe_interface} | match ae'
                    
                    url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                    response_ae = requests.get(url_pe, verify=False)
                    response_ae.raise_for_status()
                    
                    output_ae = response_ae.json()
                    ne_response_ae = output_ae.get('ne_response', '')
                    combined_ne_response += ne_response_ae + "\n\n"
                    
                    ae_bundle_value = ""
                    if "Protocol aenet, AE bundle" in ne_response_ae:
                        match = re.search(r'Protocol aenet, AE bundle: (\S+)', ne_response_ae)
                        if match:
                            ae_bundle_value = match.group(1)
                    
                    int_match_physical_int = ae_bundle_value.split('.')[0] if ae_bundle_value else pe_interface
                    command = f'show interfaces {int_match_physical_int}'
                    
                    url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                    response = requests.get(url_pe, verify=False)
                    response.raise_for_status()
                    
                    output = response.json()
                    ne_response = output.get('ne_response', '')

                    lines = ne_response.split('\r\n')
                    interface_status_line = next((line for line in lines if line.startswith(f'Physical interface: {int_match_physical_int},')), '')
                    
                    combined_ne_response += ne_response + "\n\n"
                    interface_status.update({'status': 'success', 'data': interface_status_line, 'ne_response': combined_ne_response})

                except requests.exceptions.RequestException as e:
                    interface_status.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})
                
                with threading.Lock():
                    combined_response.append(interface_status)

            # Check Interface Power Level (Juniper)
            def fetch_interface_power_level_juniper(serialized_data, combined_response):
                try:
                    interface_power_level = {
                        'interface_power_level': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }
                    interface_power_level['interface_power_level'] = True

                    command = f'show interfaces diagnostics optics {serialized_data["pe_interface"].split(".")[0]}'
                    url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                    response = requests.get(url_pe, verify=False)
                    response.raise_for_status()
                    
                    output = response.json()
                    ne_response = output.get('ne_response', '')

                    power_data = [line.strip() for line in ne_response.split('\r\n') if 'Laser output power' in line or 'Receiver signal average optical power' in line]

                    interface_power_level.update({'status': 'success', 'data': '\n'.join(power_data), 'ne_response': ne_response})

                except requests.exceptions.RequestException as e:
                    interface_power_level.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                with threading.Lock():
                    combined_response.append(interface_power_level)

            # Check Bandwidth Subscription (Juniper)
            def fetch_bandwidth_subscription_juniper(serialized_data, combined_response):
                try:
                    bandwidth_subscription = {
                        'bandwidth_subscription': False,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }
                    bandwidth_subscription['bandwidth_subscription'] = True

                    combined_ne_response = ""
                    combined_data_response = {
                        "Description": "",
                        "Policer": "",
                        "Shaping-rate": ""
                    }

                    command = f'show configuration interfaces {serialized_data["pe_interface"]} description'
                    url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                    response = requests.get(url_pe, verify=False)
                    response.raise_for_status()
                    output = response.json()
                    ne_response = output.get('ne_response', '')
                    combined_ne_response += ne_response + "\n\n"

                    ne_response_lines = ne_response.split('\n')
                    for line in ne_response_lines:
                        if "description" in line:
                            description_match = re.search(r'description\s*"([^"]+)"', line)
                            if description_match:
                                combined_data_response["Description"] = description_match.group(1)
                                break

                    if re.search(r'\.', serialized_data['pe_interface'], re.IGNORECASE):
                        pe_interface_base = serialized_data["pe_interface"].split(".")[0]
                        command = f'show interfaces {pe_interface_base} | match ae'
                    else:
                        pe_interface_base = serialized_data["pe_interface"]
                        command = f'show interfaces {pe_interface_base} | match ae'

                    url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                    response_ae = requests.get(url_pe, verify=False)
                    response_ae.raise_for_status()
                    output_ae = response_ae.json()
                    ne_response_ae = output_ae.get('ne_response', '')
                    combined_ne_response += ne_response_ae + "\n\n"

                    ae_bundle_value = ""
                    if "Protocol aenet, AE bundle" in ne_response_ae:
                        match = re.search(r'Protocol aenet, AE bundle: (\S+)', ne_response_ae)
                        if match:
                            ae_bundle_value = match.group(1)

                    # Ae Bundle
                    if ae_bundle_value:
                        ae_base, ae_unit = ae_bundle_value.split(".") if "." in ae_bundle_value else (ae_bundle_value, "")
                        command = f'show configuration interfaces {ae_base} unit {ae_unit} | match policer'
                    else:
                        # command = f"show configuration interfaces {serialized_data["pe_interface"]} | match policer"
                        command = f"show configuration interfaces {serialized_data['pe_interface']} | match policer"

                    url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                    response_policer = requests.get(url_pe, verify=False)
                    response_policer.raise_for_status()
                    output_policer = response_policer.json()
                    ne_response_policer = output_policer.get('ne_response', '')
                    combined_ne_response += ne_response_policer + "\n\n"

                    policer_match = re.findall(r'input (\S+);', ne_response_policer)
                    if policer_match:
                        combined_data_response["Policer"] = ", ".join(policer_match)

                    if not ae_bundle_value and re.search(r'\.', serialized_data["pe_interface"], re.IGNORECASE):
                        pe_interface_split = serialized_data["pe_interface"].split(".")
                        if len(pe_interface_split) > 1:
                            pe_interface_base, pe_interface_unit = pe_interface_split
                            command = f'show configuration class-of-service interfaces {pe_interface_base} unit {pe_interface_unit}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response_shaping_rate = requests.get(url_pe, verify=False)
                            response_shaping_rate.raise_for_status()
                            output_shaping_rate = response_shaping_rate.json()
                            ne_response_shaping_rate = output_shaping_rate.get('ne_response', '')
                            combined_ne_response += ne_response_shaping_rate + "\n\n"

                            shaping_rate_match = re.search(r'shaping-rate (.+)', ne_response_shaping_rate)
                            if shaping_rate_match:
                                combined_data_response["Shaping-rate"] = shaping_rate_match.group(1)

                    bandwidth_subscription.update({'status': 'success', 'data': combined_data_response, 'ne_response': combined_ne_response})

                except requests.exceptions.RequestException as e:
                    bandwidth_subscription.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                with threading.Lock():
                    combined_response.append(bandwidth_subscription)

            # Ping Test (Juniper) (IPv4 and IPv6)
            def fetch_ping_test_juniper(serialized_data, combined_response):
                try:
                    ping_test_pe_ce = {
                        'ping_test_pe_ce': True,
                        'status': 'not_checked',
                        'data': {
                            'ipv4': 'n/a',
                            'ipv6': 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    }

                    # Check IPv4 if provided
                    if serialized_data.get('ce_ipv4'):
                        command = f'ping {serialized_data["ce_ipv4"].split("/")[0]} source-address {serialized_data["pe_ipv4"].split("/")[0]} count 10'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response_ipv4 = output.get('ne_response', '')

                        ping_test_pe_ce['ne_response']['ne_response_ipv4'] = ne_response_ipv4

                        if "Can't assign requested address" in ne_response_ipv4:
                            ping_test_pe_ce['data']['ipv4'] = "Can't assign requested address"
                            ping_test_pe_ce['status'] = 'failed'
                        elif "syntax error" in ne_response_ipv4:
                            command = f'ping {serialized_data["ce_ipv4"].split("/")[0]} source {serialized_data["pe_ipv4"].split("/")[0]} count 10'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()
                            output = response.json()
                            ne_response_ipv4 = output.get('ne_response', '')
                            ping_test_pe_ce['ne_response']['ne_response_ipv4'] = ne_response_ipv4
                            match = re.search(r'(\d+\.?\d*)% packet loss', ne_response_ipv4)
                            packet_loss_ipv4 = match.group(1) if match else 'unknown'
                            ping_test_pe_ce['data']['ipv4'] = f'{packet_loss_ipv4}% packet loss'
                            ping_test_pe_ce['status'] = 'success'
                        else:
                            match_ipv4 = re.search(r'(\d+\.?\d*)% packet loss', ne_response_ipv4)
                            packet_loss_ipv4 = match_ipv4.group(1) if match_ipv4 else 'unknown'
                            ping_test_pe_ce['data']['ipv4'] = f'{packet_loss_ipv4}% packet loss'
                            ping_test_pe_ce['status'] = 'success'
                    else:
                        ping_test_pe_ce['data']['ipv4'] = 'IPv4 address not provided'
                        ping_test_pe_ce['status'] = 'skipped'

                    # Check IPv6 if provided
                    if serialized_data.get('ce_ipv6'):
                        command = f'ping {serialized_data["ce_ipv6"].split("/")[0]} source-address {serialized_data["pe_ipv6"].split("/")[0]} count 10'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response_ipv6 = output.get('ne_response', '')

                        ping_test_pe_ce['ne_response']['ne_response_ipv6'] = ne_response_ipv6

                        if "Can't assign requested address" in ne_response_ipv6:
                            ping_test_pe_ce['data']['ipv6'] = "Can't assign requested address"
                            ping_test_pe_ce['status'] = 'failed'
                        elif "syntax error" in ne_response_ipv6:
                            command = f'ping {serialized_data["ce_ipv6"].split("/")[0]} source {serialized_data["pe_ipv6"].split("/")[0]} count 10'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()
                            output = response.json()
                            ne_response_ipv6 = output.get('ne_response', '')
                            ping_test_pe_ce['ne_response']['ne_response_ipv6'] = ne_response_ipv6
                            match = re.search(r'(\d+\.?\d*)% packet loss', ne_response_ipv6)
                            packet_loss_ipv6 = match.group(1) if match else 'unknown'
                            ping_test_pe_ce['data']['ipv6'] = f'{packet_loss_ipv6}% packet loss'
                            ping_test_pe_ce['status'] = 'success'
                        else:
                            match_ipv6 = re.search(r'(\d+\.?\d*)% packet loss', ne_response_ipv6)
                            packet_loss_ipv6 = match_ipv6.group(1) if match_ipv6 else 'unknown'
                            ping_test_pe_ce['data']['ipv6'] = f'{packet_loss_ipv6}% packet loss'
                            ping_test_pe_ce['status'] = 'success'
                    else:
                        ping_test_pe_ce['data']['ipv6'] = 'IPv6 address not provided'
                        ping_test_pe_ce['status'] = 'skipped'

                except requests.exceptions.RequestException as e:
                    ping_test_pe_ce['status'] = 'failed'
                    ping_test_pe_ce['data']['ipv4'] = str(e)
                    ping_test_pe_ce['data']['ipv6'] = str(e)
                    ping_test_pe_ce['ne_response']['ne_response_ipv4'] = 'n/a'
                    ping_test_pe_ce['ne_response']['ne_response_ipv6'] = 'n/a'

                with threading.Lock():
                    combined_response.append(ping_test_pe_ce)

            # Traceroute (Juniper) (IPv4 and IPv6)
            def fetch_traceroute_juniper(serialized_data, combined_response):
                traceroute_results = []
                combined_ne_response = ""

                for ip in serialized_data.get('traceroute_lan_ip', []):
                    traceroute = {
                        'traceroute': True,
                        'status': 'not_checked',
                        'data': None,
                        'ne_response': 'n/a'
                    }

                    # Remove subnet mask if present
                    if '/' in ip:
                        ip = ip.split('/')[0]

                    try:
                        command = f'traceroute {ip}'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        traceroute.update({'status': 'success', 'ne_response': output.get('ne_response', 'n/a')})

                        combined_ne_response += traceroute['ne_response'] + "\n\n"

                    except requests.exceptions.RequestException as e:
                        traceroute.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    traceroute_results.append(traceroute)

                final_response = {
                    'traceroute': True,
                    'status': 'success',
                    'data': 'n/a',
                    'ne_response': combined_ne_response
                }

                with threading.Lock():
                    combined_response.append(final_response)

            # Ping LAN IP (Juniper) (IPv4 and IPv6)
            # def fetch_ping_lan_ip_juniper(serialized_data, combined_response):
            #     ping_lan_ip_results = []
            #     combined_ne_response = ""

            #     for ip in serialized_data.get('ping_lan_ip', []):
            #         ping_lan_ip = {
            #             'ping_lan_ip': True,
            #             'status': 'not_checked',
            #             'data': 'n/a',
            #             'ne_response': 'n/a'
            #         }

            #         # Remove subnet mask if present
            #         if '/' in ip:
            #             ip = ip.split('/')[0]

            #         try:
            #             command = f'ping {ip} count 10'
            #             url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

            #             response = requests.get(url_pe, verify=False)
            #             response.raise_for_status()
            #             output = response.json()

            #             ne_response = output.get('ne_response', '')

            #             match = re.search(r'(\d+\.?\d*)% packet loss', ne_response)
            #             packet_loss = match.group(1) if match else 'unknown'

            #             ping_lan_ip.update({'status': 'success', 'data': f'{packet_loss}% packet loss', 'ne_response': ne_response})

            #             combined_ne_response += ping_lan_ip['ne_response'] + "\n\n"

            #         except requests.exceptions.RequestException as e:
            #             ping_lan_ip.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

            #         ping_lan_ip_results.append(ping_lan_ip)

            #     final_response = {
            #         'ping_lan_ip': True,
            #         'status': 'success',
            #         'data': 'n/a',
            #         'ne_response': combined_ne_response
            #     }

            #     with threading.Lock():
            #         combined_response.append(final_response)
            
            def fetch_ping_lan_ip_juniper(serialized_data, combined_response):
                # Initialize the ping_lan_ip dictionary
                ping_lan_ip = {'ping_lan_ip': True}
                combined_ne_response = ""

                # Iterate over the IPs from the serialized data
                for index, ip in enumerate(serialized_data.get('ping_lan_ip', []), start=1):
                    # Create a key dynamically for each IP, e.g., 'lan_ip_1', 'lan_ip_2', etc.
                    lan_ip_key = f"lan_ip_{index}"
                    ping_lan_ip[lan_ip_key] = {
                        'status': 'not_checked',
                        'data': 'n/a',
                        'ne_response': 'n/a'
                    }

                    # Remove subnet mask if present
                    if '/' in ip:
                        ip = ip.split('/')[0]

                    try:
                        command = f'ping {ip} count 10'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()

                        ne_response = output.get('ne_response', '')

                        match = re.search(r'(\d+\.?\d*)% packet loss', ne_response)
                        packet_loss = match.group(1) if match else 'unknown'

                        # Update the specific lan_ip entry with the results
                        ping_lan_ip[lan_ip_key].update({
                            'status': 'success',
                            'data': f'{packet_loss}% packet loss',
                            'ne_response': ne_response
                        })

                        # Collect combined responses
                        combined_ne_response += ne_response + "\n\n"

                    except requests.exceptions.RequestException as e:
                        # Update the specific lan_ip entry in case of a request failure
                        ping_lan_ip[lan_ip_key].update({
                            'status': 'failed',
                            'data': str(e),
                            'ne_response': 'n/a'
                        })

                # Safely append the ping_lan_ip dictionary to the combined response
                with threading.Lock():
                    combined_response.append(ping_lan_ip)


            # Incoming Route (Juniper) (IPv4 and IPv6)
            def fetch_incoming_route_juniper(serialized_data, combined_response):
                try:
                    incoming_route = {
                        'incoming_route': True,
                        'status': 'not_checked',
                        'data': {
                            'ipv4': 'n/a',
                            'ipv6': 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    }

                    # Check IPv4 if provided
                    if serialized_data.get('ce_ipv4'):
                        command = f'show route receive-protocol bgp {serialized_data["ce_ipv4"].split("/")[0]}'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response_ipv4 = output.get('ne_response', '')

                        incoming_route['ne_response']['ne_response_ipv4'] = ne_response_ipv4
                        incoming_route['data']['ipv4'] = 'n/a'  # Update 'data' field if needed

                        incoming_route['status'] = 'success'
                    else:
                        incoming_route['data']['ipv4'] = 'IPv4 address not provided'
                        incoming_route['status'] = 'skipped'

                    # Check IPv6 if provided
                    if serialized_data.get('ce_ipv6'):
                        command = f'show route receive-protocol bgp {serialized_data["ce_ipv6"].split("/")[0]}'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response_ipv6 = output.get('ne_response', '')

                        incoming_route['ne_response']['ne_response_ipv6'] = ne_response_ipv6
                        incoming_route['data']['ipv6'] = 'n/a'  # Update 'data' field if needed

                        incoming_route['status'] = 'success'
                    else:
                        incoming_route['data']['ipv6'] = 'IPv6 address not provided'
                        incoming_route['status'] = 'skipped'

                except requests.exceptions.RequestException as e:
                    incoming_route['status'] = 'failed'
                    incoming_route['data']['ipv4'] = str(e)
                    incoming_route['data']['ipv6'] = str(e)
                    incoming_route['ne_response']['ne_response_ipv4'] = 'n/a'
                    incoming_route['ne_response']['ne_response_ipv6'] = 'n/a'

                with threading.Lock():
                    combined_response.append(incoming_route)

            # Advertise Route (Juniper) (IPv4 and IPv6)
            def fetch_advertise_route_juniper(serialized_data, combined_response):
                try:
                    advertise_route = {
                        'advertise_route': True,
                        'status': 'not_checked',
                        'data': {
                            'ipv4': 'n/a',
                            'ipv6': 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    }

                    combined_ne_response_ipv4 = ""
                    combined_ne_response_ipv6 = ""

                    # Check each IP in advertise_route_lan_ip
                    for ip in serialized_data.get('advertise_route_lan_ip', []):
                        if is_ipv4(ip) and serialized_data.get('ce_ipv4'):
                            # Handle IPv4
                            command = f'show route advertising-protocol bgp {serialized_data["ce_ipv4"].split("/")[0]} match-prefix {ip}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()
                            output = response.json()
                            ne_response_ipv4 = output.get('ne_response', '')

                            combined_ne_response_ipv4 += ne_response_ipv4 + "\n\n"

                            advertise_route['ne_response']['ne_response_ipv4'] = combined_ne_response_ipv4.strip()
                            advertise_route['data']['ipv4'] = 'IPv4 advertisement checked'
                            advertise_route['status'] = 'success'

                        elif is_ipv6(ip) and serialized_data.get('ce_ipv6'):
                            # Handle IPv6
                            command = f'show route advertising-protocol bgp {serialized_data["ce_ipv6"].split("/")[0]} match-prefix {ip}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()
                            output = response.json()
                            ne_response_ipv6 = output.get('ne_response', '')

                            combined_ne_response_ipv6 += ne_response_ipv6 + "\n\n"

                            advertise_route['ne_response']['ne_response_ipv6'] = combined_ne_response_ipv6.strip()
                            advertise_route['data']['ipv6'] = 'IPv6 advertisement checked'
                            advertise_route['status'] = 'success'
                        else:
                            if is_ipv4(ip):
                                advertise_route['data']['ipv4'] = 'IPv4 address not provided'
                            elif is_ipv6(ip):
                                advertise_route['data']['ipv6'] = 'IPv6 address not provided'

                except requests.exceptions.RequestException as e:
                    advertise_route['status'] = 'failed'
                    advertise_route['data']['ipv4'] = str(e)
                    advertise_route['data']['ipv6'] = str(e)
                    advertise_route['ne_response']['ne_response_ipv4'] = 'n/a'
                    advertise_route['ne_response']['ne_response_ipv6'] = 'n/a'

                with threading.Lock():
                    combined_response.append(advertise_route)

            # Route Summary (Juniper) (IPv4 and IPv6)
            def fetch_route_summary_juniper(serialized_data, combined_response):
                try:

                    route_summary = {
                        'route_summary': True,
                        'status': 'not_checked',
                        'data': {
                            'ipv4': 'n/a',
                            'ipv6': 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    }

                    combined_ne_response_ipv4 = ""
                    combined_ne_response_ipv6 = ""

                    # Check IPv4 if provided
                    if serialized_data.get('ce_ipv4'):
                        command = f'show bgp neighbor {serialized_data["ce_ipv4"].split("/")[0]} | match prefix'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response_ipv4 = output.get('ne_response', '')

                        route_summary['ne_response']['ne_response_ipv4'] = ne_response_ipv4
                        route_summary['data']['ipv4'] = 'n/a'  # Update 'data' field if needed

                        route_summary['status'] = 'success'
                    else:
                        route_summary['data']['ipv4'] = 'IPv4 address not provided'
                        route_summary['status'] = 'skipped'

                    # Check IPv6 if provided
                    if serialized_data.get('ce_ipv6'):
                        command = f'show bgp neighbor {serialized_data["ce_ipv6"].split("/")[0]} | match prefix'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"
                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response_ipv6 = output.get('ne_response', '')

                        route_summary['ne_response']['ne_response_ipv6'] = ne_response_ipv6
                        route_summary['data']['ipv6'] = 'n/a'  # Update 'data' field if needed

                        route_summary['status'] = 'success'
                    else:
                        route_summary['data']['ipv6'] = 'IPv6 address not provided'
                        route_summary['status'] = 'skipped'

                except requests.exceptions.RequestException as e:
                    route_summary['status'] = 'failed'
                    route_summary['data']['ipv4'] = str(e)
                    route_summary['data']['ipv6'] = str(e)
                    route_summary['ne_response']['ne_response_ipv4'] = 'n/a'
                    route_summary['ne_response']['ne_response_ipv6'] = 'n/a'

                with threading.Lock():
                    combined_response.append(route_summary)


            # IP Prefix
            def fetch_ip_prefix_register_juniper(serialized_data, combined_response):
                try:
                    ip_prefix_register = {
                        'ip_prefix_register': True,
                        'status': 'not_checked',
                        'data': {
                            'ipv4': 'n/a',
                            'ipv6': 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    }

                    combined_ne_response_ipv4 = ""
                    combined_ne_response_ipv6 = ""

                    # Check IPv4 if provided
                    if serialized_data.get('ce_ipv4'):
                        command = f'show configuration | match {serialized_data["ce_ipv4"].split("/")[0]} | match IP_TRANSIT | display set'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response = output.get('ne_response', '')
                        combined_ne_response_ipv4 += ne_response + "\n\n"

                        match = re.search(r'import (\S+)', ne_response)
                        policy_name = match.group(1) if match else None

                        if policy_name:
                            command = f'show configuration policy-options policy-statement {policy_name}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()
                            output = response.json()
                            ne_response = output.get('ne_response', '')

                            combined_ne_response_ipv4 += ne_response + "\n\n"
                            ip_prefix_register['data']['ipv4'] = 'n/a'
                            ip_prefix_register['ne_response']['ne_response_ipv4'] = combined_ne_response_ipv4
                            ip_prefix_register['status'] = 'success'
                        else:
                            ip_prefix_register.update({
                                'status': 'failed',
                                'data': {
                                    'ipv4': 'Policy name not found in the first command output',
                                    'ipv6': 'n/a'
                                },
                                'ne_response': {
                                    'ne_response_ipv4': combined_ne_response_ipv4,
                                    'ne_response_ipv6': 'n/a'
                                }
                            })

                    # Check IPv6 if provided
                    if serialized_data.get('ce_ipv6'):
                        command = f'show configuration | match {serialized_data["ce_ipv6"].split("/")[0]} | match IP_TRANSIT | display set'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        response = requests.get(url_pe, verify=False)
                        response.raise_for_status()
                        output = response.json()
                        ne_response = output.get('ne_response', '')
                        combined_ne_response_ipv6 += ne_response + "\n\n"

                        match = re.search(r'import (\S+)', ne_response)
                        policy_name = match.group(1) if match else None

                        if policy_name:
                            command = f'show configuration policy-options policy-statement {policy_name}'
                            url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()
                            output = response.json()
                            ne_response = output.get('ne_response', '')

                            combined_ne_response_ipv6 += ne_response + "\n\n"
                            ip_prefix_register['data']['ipv6'] = 'n/a'
                            ip_prefix_register['ne_response']['ne_response_ipv6'] = combined_ne_response_ipv6
                            ip_prefix_register['status'] = 'success'
                        else:
                            ip_prefix_register.update({
                                'status': 'failed',
                                'data': {
                                    'ipv4': ip_prefix_register['data']['ipv4'],
                                    'ipv6': 'Policy name not found in the first command output'
                                },
                                'ne_response': {
                                    'ne_response_ipv4': combined_ne_response_ipv4,
                                    'ne_response_ipv6': combined_ne_response_ipv6
                                }
                            })

                except requests.exceptions.RequestException as e:
                    ip_prefix_register.update({
                        'status': 'failed',
                        'data': {
                            'ipv4': str(e),
                            'ipv6': str(e)
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    })

                with threading.Lock():
                    combined_response.append(ip_prefix_register)


            # BGP Status
            def fetch_bgp_status_juniper(serialized_data, combined_response):
                def simplify_ipv6(ip):
                    try:
                        return str(ipaddress.IPv6Address(ip.split('/')[0]))
                    except ipaddress.AddressValueError:
                        return ip

                try:
                    bgp_status = {
                        'bgp_status': True,
                        'status': 'not_checked',
                        'data': {
                            'ipv4': 'n/a',
                            'ipv6': 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    }

                    # Check BGP status for IPv4
                    if serialized_data.get('ce_ipv4'):
                        command_ipv4 = f'show bgp summary | match {serialized_data["ce_ipv4"].split("/")[0]}'
                        url_pe_ipv4 = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command_ipv4}"

                        response_ipv4 = requests.get(url_pe_ipv4, verify=False)
                        response_ipv4.raise_for_status()
                        output_ipv4 = response_ipv4.json()
                        ne_response_ipv4 = output_ipv4.get('ne_response', '')

                        match_ipv4 = re.search(r'\b(Establ|Established|Active|Open)\b', ne_response_ipv4)
                        bgp_state_ipv4 = match_ipv4.group(0) if match_ipv4 else 'unknown'

                        bgp_status['data']['ipv4'] = bgp_state_ipv4
                        bgp_status['ne_response']['ne_response_ipv4'] = ne_response_ipv4
                        bgp_status['status'] = 'success'

                    # Check BGP status for IPv6
                    if serialized_data.get('ce_ipv6'):
                        simplified_ipv6 = simplify_ipv6(serialized_data['ce_ipv6'])
                        command_ipv6 = f'show bgp summary | match {simplified_ipv6.split("/")[0]}'
                        url_pe_ipv6 = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command_ipv6}"

                        response_ipv6 = requests.get(url_pe_ipv6, verify=False)
                        response_ipv6.raise_for_status()
                        output_ipv6 = response_ipv6.json()
                        ne_response_ipv6 = output_ipv6.get('ne_response', '')

                        match_ipv6 = re.search(r'\b(Establ|Established|Active|Open)\b', ne_response_ipv6)
                        bgp_state_ipv6 = match_ipv6.group(0) if match_ipv6 else 'unknown'

                        bgp_status['data']['ipv6'] = bgp_state_ipv6
                        bgp_status['ne_response']['ne_response_ipv6'] = ne_response_ipv6
                        bgp_status['status'] = 'success'

                except requests.exceptions.RequestException as e:
                    bgp_status.update({
                        'status': 'failed',
                        'data': {
                            'ipv4': str(e) if serialized_data.get('ce_ipv4') else 'n/a',
                            'ipv6': str(e) if serialized_data.get('ce_ipv6') else 'n/a'
                        },
                        'ne_response': {
                            'ne_response_ipv4': 'n/a',
                            'ne_response_ipv6': 'n/a'
                        }
                    })

                with threading.Lock():
                    combined_response.append(bgp_status)

            # Show Routing Table
            def fetch_show_routing_table_juniper(serialized_data, combined_response):
                try:
                    show_routing_table_status = {
                        'show_routing_table': True,
                        'status': 'not_checked',
                        'data': []
                    }

                    # Function to simplify IPv6 addresses while keeping the subnet mask
                    def simplify_ipv6(ip):
                        try:
                            address, mask = ip.split('/')
                            simplified_address = str(ipaddress.IPv6Address(address))
                            return f"{simplified_address}/{mask}"
                        except (ipaddress.AddressValueError, ValueError):
                            return ip

                    # Check routing table for each LAN IP
                    for ip in serialized_data.get('show_routing_table_lan_ip', []):
                        # Simplify IPv6 addresses
                        if ':' in ip:
                            ip = simplify_ipv6(ip)

                        command = f'show route {ip}'
                        url_pe = f"http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={serialized_data['ne_ip']}&command={command}"

                        try:
                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()
                            ne_response = output.get('ne_response', 'n/a')

                            show_routing_table_status['data'].append({
                                'ip': ip,
                                'ne_response': ne_response
                            })
                            show_routing_table_status['status'] = 'success'

                        except requests.exceptions.RequestException as e:
                            show_routing_table_status['data'].append({
                                'ip': ip,
                                'ne_response': f"Error: {str(e)}"
                            })

                except Exception as e:
                    show_routing_table_status.update({
                        'status': 'failed',
                        'data': [{'error': str(e)}]
                    })

                with threading.Lock():
                    combined_response.append(show_routing_table_status)


            # NID Loopback Checking
            def fetch_nid_loopback_checking(serialized_data, combined_response):
                nid_loopback_checking = {
                    'nid_loopback_checking': False,
                    'status': 'not_checked',
                    'data': None,
                    'ne_response': 'n/a'
                }

                nid_loopback_checking['nid_loopback_checking'] = True

                if serialized_data['nid_model'] != '' and serialized_data['nid_device_id'] != '':

                    if serialized_data['nid_model'] == 'RAISECOM':

                        try:
                            command = f'show interface nni 1 loopback'
                            url_pe = f"http://pisa-raisecom-svc/raisecom/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()
                            ne_response = output.get('ne_response', '')

                            output = {
                                "loopback_status": None,
                                "loopback_rule": None
                            }

                            # Regular expressions to match the desired values in the response
                            loopback_status_pattern = re.compile(r"Loopback status:\s+(\d+)")
                            loopback_rule_pattern = re.compile(r"Loopback rule:\s+(\d+)")

                            # Extract the Loopback Status
                            loopback_status_match = loopback_status_pattern.search(ne_response)
                            if loopback_status_match:
                                output["loopback_status"] = loopback_status_match.group(1)

                            # Extract the loopback rule
                            loopback_rule_match = loopback_rule_pattern.search(ne_response)
                            if loopback_rule_match:
                                output["loopback_rule"] = loopback_rule_match.group(1)

                            # Update the nid_loopback_checking dictionary
                            nid_loopback_checking.update({'status': 'success', 'data': output, 'ne_response': ne_response})

                        except requests.exceptions.RequestException as e:
                            nid_loopback_checking.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    elif serialized_data['nid_model'] == 'HFR':

                        try:
                            command = f'show ethernet sat loopback'
                            url_pe = f"http://pisa-hfr-svc/hfr/v1/show_config?ne_ip={serialized_data['ne_ip']}&command={command}"

                            response = requests.get(url_pe, verify=False)
                            response.raise_for_status()  # Check for HTTP errors
                            output = response.json()
                            ne_response = output.get('ne_response', '')

                            output = {
                                "loopback_status": None
                            }

                            # Regular expressions to match the desired values in the response
                            loopback_status_pattern = re.compile(r"LOOPBACK Status:\s+(\d+)")

                            # Extract the Loopback Status
                            loopback_status_match = loopback_status_pattern.search(ne_response)
                            if loopback_status_match:
                                output["loopback_status"] = loopback_status_match.group(1)

                            # Update the nid_loopback_checking dictionary
                            nid_loopback_checking.update({'status': 'success', 'data': output, 'ne_response': ne_response})

                        except requests.exceptions.RequestException as e:
                            nid_loopback_checking.update({'status': 'failed', 'data': str(e), 'ne_response': 'n/a'})

                    else:
                        nid_loopback_checking.update({'status': 'failed', 'data': 'Invalid NID model (Only Raisecom and HFR)', 'ne_response': 'n/a'})

                else:
                    nid_loopback_checking.update({'status': 'failed', 'data': 'Incomplete/invalid information fetched by API for NID Testing', 'ne_response': 'n/a'})

                with threading.Lock():
                    combined_response.append(nid_loopback_checking)


            # Network Type = PE and Vendor = Juniper
            if serialized_data['network_type'] == 'pe' and serialized_data['vendor'] == 'juniper':

                threads = []

                if serialized_data['service_testing']['interface_status']:
                    thread_interface_status = threading.Thread(target=fetch_interface_status_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_interface_status)
                    thread_interface_status.start()

                if serialized_data['service_testing']['interface_power_level']:
                    thread_interface_power_level = threading.Thread(target=fetch_interface_power_level_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_interface_power_level)
                    thread_interface_power_level.start()

                if serialized_data['service_testing']['bandwidth_subscription']:
                    thread_bandwidth_subscription = threading.Thread(target=fetch_bandwidth_subscription_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_bandwidth_subscription)
                    thread_bandwidth_subscription.start()

                if serialized_data['service_testing']['ping_test_pe_ce']:
                    thread_ping_test_pe_ce = threading.Thread(target=fetch_ping_test_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_ping_test_pe_ce)
                    thread_ping_test_pe_ce.start()

                if serialized_data['service_testing']['traceroute']:
                    thread_traceroute = threading.Thread(target=fetch_traceroute_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_traceroute)
                    thread_traceroute.start()

                if serialized_data['service_testing']['ping_lan_ip']:
                    thread_ping_lan_ip = threading.Thread(target=fetch_ping_lan_ip_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_ping_lan_ip)
                    thread_ping_lan_ip.start()

                if serialized_data['service_testing']['incoming_route']:
                    thread_incoming_route = threading.Thread(target=fetch_incoming_route_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_incoming_route)
                    thread_incoming_route.start()

                if serialized_data['service_testing']['advertise_route']:
                    thread_advertise_route = threading.Thread(target=fetch_advertise_route_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_advertise_route)
                    thread_advertise_route.start()

                if serialized_data['service_testing']['route_summary']:
                    thread_route_summary = threading.Thread(target=fetch_route_summary_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_route_summary)
                    thread_route_summary.start()

                if serialized_data['service_testing']['ip_prefix_register']:
                    thread_ip_prefix_register = threading.Thread(target=fetch_ip_prefix_register_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_ip_prefix_register)
                    thread_ip_prefix_register.start()

                if serialized_data['service_testing']['bgp_status']:
                    thread_bgp_status = threading.Thread(target=fetch_bgp_status_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_bgp_status)
                    thread_bgp_status.start()

                if serialized_data['service_testing']['show_routing_table']:
                    thread_show_routing_table = threading.Thread(target=fetch_show_routing_table_juniper, args=(serialized_data, combined_response))
                    threads.append(thread_show_routing_table)
                    thread_show_routing_table.start()

                if serialized_data['service_testing']['nid_loopback_checking']:
                    thread_nid_loopback_checking = threading.Thread(target=fetch_nid_loopback_checking, args=(serialized_data, combined_response))
                    threads.append(thread_nid_loopback_checking)
                    thread_nid_loopback_checking.start()

                for thread in threads:
                    thread.join()

                return Response({'status': 'success', 'data': combined_response}, status=status.HTTP_200_OK)

            else:
                api_response['status'] = 'failed'
                api_response['message'] = 'Network type or vendor is not supported'
                api_response['data'] = 'n/a'
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            # log_text(filename=log_file, content=output_message)
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            api_response['data'] = output_error
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)


    # define a function to invoke API
    def invoke_api(self, info):
        response = requests.get(
            info['url'], 
            # data=info['payload'], 
            verify=False, 
            # headers={ 'Authorization': info['token'] }
        )
        return response.json()


