from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.serializers import SelfVerificationBot5Gv2Serializer
import time, re, sys, os, requests
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from api.libs import log_text, log_database
from json import dumps

# define custome exception
class lldpException(Exception):
    pass


class API(APIView):
    # exclude from global authentication
    authentication_classes = ()
    permission_classes = ()

    # document the API in swagger
    @swagger_auto_schema(
        request_body=SelfVerificationBot5Gv2Serializer,
        responses={201: SelfVerificationBot5Gv2Serializer()},
        operation_summary="Workflow for 5G Self Verification Bot (version 2) for Telegram Bot (NOCC)"
    )
    
    def post(self, request):
        try:
            # Get user Id from Token
            token = request.META.get('HTTP_AUTHORIZATION', '').replace('Token ', '')
            user_id = Token.objects.get(key=token).user_id if token else None
            user = User.objects.get(id=user_id).username if user_id else request.data['staff_id']

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = SelfVerificationBot5Gv2Serializer(data=request.data)
            
            if not input_data.is_valid():  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                DB_data = {
                    'order_type': 'Verification',
                    'service_type': '5G',
                    'input_details': request.data,
                    'message': output_message,
                    'api_output': str(input_data.errors),
                    'status': 'failed',
                    'log_file': 'N/A',
                    'user': user
                }
                api_response = {
                    'status': 'failed',
                    'message': output_message,
                    'data': input_data.errors,
                    'log_file': ''
                }
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            serialized_data = input_data.validated_data # assign a variable with serialized data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = f"5G_ExistingVerificationBot_{serialized_data['csr_hostname'].replace('.', '').replace('tmonemy', '').upper()}_{date_now}@{time_now}_by_{user}.txt" # Create Log File

            # Log file created time
            output_message = "File created at {} {} for 5G Existing Site Verification Bot Log".format(date_now, time_now)
            log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

            # for purpose of displaying the info in ipgenv2
            serialized_data['service_id'] = serialized_data['csr_hostname'].lower().replace('.tmone.my', '').upper()

            # Determine CSR vendor
            if re.search('zt', serialized_data['csr_hostname'], re.IGNORECASE):
                serialized_data['csr_vendor'] = 'ZTE'
                
            elif re.search('hw', serialized_data['csr_hostname'], re.IGNORECASE):
                serialized_data['csr_vendor'] = 'Huawei'

            else:
                output_message = f"Invalid CSR hostname {serialized_data['csr_hostname']} which does not contain 'hw' or 'zt'"
                DB_data = {
                    'order_type': 'Verification',
                    'service_type': '5G',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': serialized_data,
                    'status': 'failed',
                    'log_file': log_file,
                    'user': user
                }
                api_response = {
                    'status': 'failed',
                    'message': output_message,
                    'data': serialized_data,
                    'log_file': log_file
                }
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            #                                       #
            #              GET AGG INFO             #
            #                                       #

            if not '.tmone.my' in serialized_data['csr_hostname'].lower():
                payload_csr = {
                    'ne_ip': f"{serialized_data['csr_hostname']}.tmone.my"
                }

            else:
                payload_csr = {
                    'ne_ip': serialized_data['csr_hostname']
                }

            ################ Logic for CSR ################

            if serialized_data['csr_vendor'] == 'Huawei':
                csr_url = 'http://pisa-huawei-svc/huawei/csr/'

                # define ports
                ports = [
                    {
                        'type': 'main',
                        'csr_port': 'GigabitEthernet0/2/0'
                    },
                    {
                        'type': 'protect',
                        'csr_port': 'GigabitEthernet0/2/1'
                    }
                ]

                response = requests.get(csr_url+'v1/display_lldp_neighbor_brief/', params=payload_csr, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving lldp neighbor details for {serialized_data['csr_hostname'].upper()}. Possible due to CSR IP not being registered in DNS server"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    # api_output = {
                    #     'main': {
                    #         'status': 'undefined',
                    #         'details': error_message
                    #     },
                    #     'protect': {
                    #         'status': 'undefined',
                    #         'details': error_message
                    #     },
                    # }
                    raise lldpException(error_message)

                else:
                    match_main, match_protect = False, False
                    for element in ports:
                        for i in range(len(output['output'])):
                            if re.search(f"{element['csr_port']}$", output['output'][i]['LocalIntf'], re.IGNORECASE):
                                serialized_data['agg_hostname'] = output['output'][i]['NeighborDev']
                                serialized_data['agg_vendor'] = 'ZTE' if re.search(r'zt', output['output'][i]['NeighborDev'], re.IGNORECASE) else 'Huawei'

                                if element['type'] == 'main':
                                    ports[0]['agg_port'] = output['output'][i]['NeighborIntf']
                                    match_main = True

                                else:
                                    ports[1]['agg_port'] = output['output'][i]['NeighborIntf']
                                    match_protect = True

                    
                    if not match_main and not match_protect:
                        error_message = f"Failed to retrieve AGG hostname, AGG main and protect ports from CSR lldp neighbor configurations"
                        # api_output = {
                        #     'main': {
                        #         'status': False,
                        #         'details': output_message_main
                        #     },
                        #     'protect': {
                        #         'status': False,
                        #         'details': output_message_protect
                        #     },
                        # }
                        log_text(filename=log_file, content=error_message, ne_output=output['ne_response'])
                        raise lldpException(error_message)

                    elif match_main and match_protect:
                        serialized_data['ports'] = ports
                        output_message_main = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG main port ({ports[0]['agg_port']}) from CSR lldp neighbor configurations"
                        output_message_protect = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG protect port ({ports[1]['agg_port']}) from CSR lldp neighbor configurations"
                        api_output = {
                            'main': {
                                'status': True,
                                'details': output_message_main
                            },
                            'protect': {
                                'status': True,
                                'details': output_message_protect
                            },
                        }
                        log_text(filename=log_file, content=f"{output_message_main} \n\n>> {output_message_protect}", ne_output=output['ne_response'])

                    elif not match_main and match_protect:
                        ports.pop(0)
                        serialized_data['ports'] = ports
                        output_message_main = f"Failed to retrieve AGG hostname and main port from CSR lldp neighbor configurations"
                        output_message_protect = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG protect port ({ports[0]['agg_port']}) from CSR lldp neighbor configurations"
                        api_output = {
                            'main': {
                                'status': False,
                                'details': output_message_main
                            },
                            'protect': {
                                'status': True,
                                'details': output_message_protect
                            },
                        }
                        log_text(filename=log_file, content=f"{output_message_main} \n\n>> {output_message_protect}", ne_output=output['ne_response'])

                    elif match_main and not match_protect:
                        ports.pop(1)
                        serialized_data['ports'] = ports
                        output_message_main = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG main port ({ports[0]['agg_port']}) from CSR lldp neighbor configurations"
                        output_message_protect = f"Failed to retrieve AGG hostname and protect port from CSR lldp neighbor configurations"
                        api_output = {
                            'main': {
                                'status': True,
                                'details': output_message_main
                            },
                            'protect': {
                                'status': False,
                                'details': output_message_protect
                            },
                        }
                        log_text(filename=log_file, content=f"{output_message_main} \n\n>> {output_message_protect}", ne_output=output['ne_response'])
        
                final_output = dict()
                final_output['lldp_neighbor'] = dict()  # Define 'lldp_neighbor' as an empty dictionary
                final_output['lldp_neighbor']['csr'] = api_output
                api_output = dict()   

                #                                                  #
                #              VERIFY INTERFACE STATUS             #
                #                                                  #
                
                for element in ports:

                    payload = {
                        'interface': element['csr_port']
                    }
                    payload.update(payload_csr)

                    response = requests.get(csr_url+'v1/display_interface/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving CSR {element['type']} port {element['csr_port']} configurations"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output[element['type']]['status'] = 'undefined'
                        api_output[element['type']]['details'] = error_message

                    else:
                        if output['output'] == "There is no output returned from NE":
                            output_message = f"There is no matching CSR {element['type']} port {element['csr_port']} configuration returned while checking interface status"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = output_message

                        else:

                            if output['output']['interface_current_state'] == 'UP' and output['output']['line_protocol'] == 'UP':
                                output_message = f"The CSR {element['type']} port {element['csr_port']} status is UP"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"The CSR {element['type']} port {element['csr_port']} current state is {output['output']['interface_current_state']} and line protocol state is {output['output']['line_protocol']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                final_output['interface_status'] = dict()
                final_output['interface_status']['csr'] = api_output
                api_output = dict()

                #                                               #
                #              VERIFY FIBER READING             #
                #                                               #

                response = requests.get(csr_url+'v1/display_optical_module_brief/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving fiber reading for CSR"
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    api_output = {
                        'main': {
                            'status': 'undefined',
                            'details': error_message
                        },
                        'protect': {
                            'status': 'undefined',
                            'details': error_message
                        },
                    }

                else:
                    j = 0
                    for element in ports:
                        index_fiber_csr, csr_port_fiber = None, element['csr_port'].replace('GigabitEthernet', 'ETH')
                        for i in range(len(output['output'])):
                            if re.search(f"{csr_port_fiber}$", output['output'][i]['port'], re.IGNORECASE):
                                index_fiber_csr = i
                                break

                        rx_power, tx_power, threshold_fiber_reading = False, False, {
                            "10km": {
                                'RxPower': [-14, 0.499],
                                'TxPower': [-7.898, -0.499],
                            },
                            "40km": {
                                'RxPower': [-15, -1],
                                'TxPower': [-4.4, 4],
                            },
                            "80km": {
                                'RxPower': [-23.979, -7],
                                'TxPower': [0, 4],
                            }
                        }

                        if index_fiber_csr is None:
                            output_message = f"No matching CSR {element['type']} port {csr_port_fiber} in the fiber reading configurations"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            log_text(filename=log_file, content=output_message)
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = output_message

                        else:

                            # verify fiber reading based on distance
                            for i in list(threshold_fiber_reading.keys()):
                                if re.search(i, output['output'][index_fiber_csr]['type']):
                                    ports[j]['csr_sfp_distance'] = i
                                    if threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][0] < output['output'][index_fiber_csr]['RxPower'] < threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][1]:
                                        rx_power = True
                                    if threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][0] < output['output'][index_fiber_csr]['TxPower'] < threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][1]:
                                        tx_power = True
                                    break

                            # if rx_power = False and tx_power = False
                            if not rx_power and not tx_power:
                                output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = True and tx_power = False
                            elif rx_power and not tx_power:
                                output_message = f"Fiber reading (TxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = False and tx_power = True
                            elif not rx_power and tx_power:
                                output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message
                            
                            # if both is True
                            elif rx_power and tx_power:
                                output_message = f"Fiber readings (RxPower ({output['output'][index_fiber_csr]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_csr]['TxPower']}dBm)) for CSR {element['type']} port {element['csr_port']} are within thresholds"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                        j += 1

                final_output['fiber_reading'] = dict()
                final_output['fiber_reading']['csr'] = api_output
                api_output = dict()

                #                                            #
                #              VERIFY BFD STATUS             #
                #                                            #

                response = requests.get(csr_url+'v1/display_bfd_session_all/', params=payload_csr, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving bfd session for CSR"
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    api_output = {
                        'main': {
                            'status': 'undefined',
                            'details': error_message
                        },
                        'protect': {
                            'status': 'undefined',
                            'details': error_message
                        },
                    }

                else:
                    for element in ports:
                        index_bfd_csr, bfd_is_up = None, False
                        for i in range(len(output['output'])):
                            if re.search(f"{element['csr_port']}$", output['output'][i]['int_name'], re.IGNORECASE):
                                index_bfd_csr = i
                                if re.search('up', output['output'][i]['state'], re.IGNORECASE):
                                    bfd_is_up = True
                                break

                        if index_bfd_csr is None:
                            output_message = f"No matching CSR {element['type']} port {element['csr_port']} in the bfd session configurations"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = output_message

                        else:
                            if not bfd_is_up:
                                output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port']}) is {output['output'][index_bfd_csr]['state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port']}) is UP"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                final_output['bfd_status'] = dict()
                final_output['bfd_status']['csr'] = api_output
                api_output = dict()

                #                                           #
                #              VERIFY CRC ERROR             #
                #                                           #
                for element in ports:

                    csr_check_crc_counter = 0
                    max_iterations = 3  # Define a maximum number of loop iterations
                    iterations = 0

                    while True:
                        iterations += 1
                        if iterations >= max_iterations:
                            error_message = "Maximum iterations reached while checking crc error. Exiting loop."
                            log_text(filename=log_file, content=error_message)
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = error_message
                            break

                        else:
                            payload = {
                                'interface': element['csr_port']
                            }
                            payload.update(payload_csr)

                            response = requests.get(csr_url+'v1/display_crc_packet_interface/', params=payload, verify=False)
                            output = response.json()

                            if output['status'] == 'failed':
                                error_message = f"An error has occured while retrieving crc reading for CSR {element['type']} port ({element['csr_port']})"
                                output_message = error_message + \
                                    '\n[error message: {}]'.format(output['output'])
                                log_text(filename=log_file, content=output_message)
                                api_output[element['type']] = { 'status': 'undefined' }
                                api_output[element['type']]['details'] = error_message
                                break

                            else:
                                if output['output'] == "The crc output does not exist":
                                    output_message = f"There is no matching CSR {element['type']} port {element['csr_port']} configuration returned while checking crc reading"
                                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                    api_output[element['type']] = { 'status': 'undefined' }
                                    api_output[element['type']]['details'] = output_message
                                    break

                                else:
                                    # check if crc = 0 
                                    if output['output']['crc'] == 0:
                                        output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port']} is {output['output']['crc']}"
                                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                        api_output[element['type']] = { 'status': True }
                                        api_output[element['type']]['details'] = output_message
                                        break

                                    else:
                                        if csr_check_crc_counter == 1:
                                            output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port']} is greater than zero ({output['output']['crc']})"
                                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                            api_output[element['type']] = { 'status': False }
                                            api_output[element['type']]['details'] = output_message
                                            break

                                        else:
                                            #                                     #
                                            #              RESET PORT             #
                                            #                                     #

                                            response = requests.get(csr_url+'v1/reset_counters_interface/', params=payload, verify=False)
                                            output = response.json()

                                            if output['status'] == 'failed':
                                                error_message = f"An error has occured while trying to reset counter for CSR {element['type']} port ({element['csr_port']})"
                                                output_message = error_message + \
                                                    '\n[error message: {}]'.format(output['output'])
                                                log_text(filename=log_file, content=output_message)
                                                api_output[element['type']] = { 'status': 'undefined' }
                                                api_output[element['type']]['details'] = error_message
                                                break

                                            else:
                                                output_message = f"Successfully reset counter for CSR {element['type']} port ({element['csr_port']})"
                                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                                csr_check_crc_counter += 1  # increase counter = port has been reset once
                                                time.sleep(5)   # put 5s delay before re-check the CRC

                final_output['crc_error'] = dict()
                final_output['crc_error']['csr'] = api_output
                api_output = dict()

            else:
                csr_url = 'http://pisa-zte-svc/zte/csr/'

                # define ports
                ports = [
                    {
                        'type': 'main',
                        'csr_port': 'xgei-1/1/0/1'
                    },
                    {
                        'type': 'protect',
                        'csr_port': 'xgei-1/1/0/2'
                    }
                ]

                response = requests.get(csr_url+'v1/show_lldp_neighbor_brief/', params=payload_csr, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving lldp neighbor details for {serialized_data['csr_hostname'].upper()}. Possible due to CSR IP not being registered in DNS server"
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    raise lldpException(error_message)

                else:
                    match_main, match_protect = False, False
                    for element in ports:
                        for i in range(len(output['output'])):
                            if re.search(f"{element['csr_port']}$", output['output'][i]['local_interface'], re.IGNORECASE):
                                serialized_data['agg_hostname'] = output['output'][i]['system_name']
                                serialized_data['agg_vendor'] = 'ZTE' if re.search(r'zt', output['output'][i]['system_name'], re.IGNORECASE) else 'Huawei'

                                if element['type'] == 'main':
                                    ports[0]['agg_port'] = output['output'][i]['port_id']
                                    match_main = True

                                else:
                                    ports[1]['agg_port'] = output['output'][i]['port_id']
                                    match_protect = True

                    if not match_main and not match_protect:
                        error_message = f"Failed to retrieve AGG hostname, AGG main and protect ports from CSR lldp neighbor configurations"
                        log_text(filename=log_file, content=error_message, ne_output=output['ne_response'])
                        raise lldpException(error_message)

                    elif match_main and match_protect:
                        serialized_data['ports'] = ports
                        output_message_main = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG main port ({ports[0]['agg_port']}) from CSR lldp neighbor configurations"
                        output_message_protect = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG protect port ({ports[1]['agg_port']}) from CSR lldp neighbor configurations"
                        api_output = {
                            'main': {
                                'status': True,
                                'details': output_message_main
                            },
                            'protect': {
                                'status': True,
                                'details': output_message_protect
                            },
                        }
                        log_text(filename=log_file, content=f"{output_message_main} \n\n>> {output_message_protect}", ne_output=output['ne_response'])

                    elif not match_main and match_protect:
                        ports.pop(0)
                        serialized_data['ports'] = ports
                        output_message_main = f"Failed to retrieve AGG hostname and main port from CSR lldp neighbor configurations"
                        output_message_protect = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG protect port ({ports[0]['agg_port']}) from CSR lldp neighbor configurations"
                        api_output = {
                            'main': {
                                'status': False,
                                'details': output_message_main
                            },
                            'protect': {
                                'status': True,
                                'details': output_message_protect
                            },
                        }
                        log_text(filename=log_file, content=f"{output_message_main} \n\n>> {output_message_protect}", ne_output=output['ne_response'])

                    elif match_main and not match_protect:
                        ports.pop(1)
                        serialized_data['ports'] = ports
                        output_message_main = f"Successfully retrieved AGG hostname ({serialized_data['agg_hostname']}) and AGG main port ({ports[0]['agg_port']}) from CSR lldp neighbor configurations"
                        output_message_protect = f"Failed to retrieve AGG hostname and protect port from CSR lldp neighbor configurations"
                        api_output = {
                            'main': {
                                'status': True,
                                'details': output_message_main
                            },
                            'protect': {
                                'status': False,
                                'details': output_message_protect
                            },
                        }
                        log_text(filename=log_file, content=f"{output_message_main} \n\n>> {output_message_protect}", ne_output=output['ne_response'])
        
                final_output = dict()
                final_output['lldp_neighbor'] = dict()  # Define 'lldp_neighbor' as an empty dictionary
                final_output['lldp_neighbor']['csr'] = api_output
                api_output = dict()

                #                                                  #
                #              VERIFY INTERFACE STATUS             #
                #                                                  #
                
                for element in ports:

                    payload = {
                        'interface': element['csr_port'],
                        'filter': element['csr_port'],
                    }
                    payload.update(payload_csr)

                    response = requests.get(csr_url+'v1/show_interface/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving CSR {element['type']} port {element['csr_port']} configurations"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output[element['type']]['status'] = 'undefined'
                        api_output[element['type']]['details'] = error_message

                    else:
                        if output['output'] == "There is no output returned from NE":
                            output_message = f"There is no matching CSR {element['type']} port {element['csr_port']} configuration returned while checking interface status"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = output_message

                        else:
                            if output['output']['interface_state'] == 'up':
                                output_message = f"The CSR {element['type']} port {element['csr_port']} status is UP"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"The CSR {element['type']} port {element['csr_port']} current state is {output['output']['interface_state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                final_output['interface_status'] = dict()
                final_output['interface_status']['csr'] = api_output
                api_output = dict()

                #                                               #
                #              VERIFY FIBER READING             #
                #                                               #

                response = requests.get(csr_url+'v1/show_optical_info_brief/', params=payload_csr, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving fiber reading for CSR"
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    api_output = {
                        'main': {
                            'status': 'undefined',
                            'details': error_message
                        },
                        'protect': {
                            'status': 'undefined',
                            'details': error_message
                        },
                    }

                else:
                    j = 0
                    for element in ports:
                        index_fiber_csr = None
                        for i in range(len(output['output'])):
                            if re.search(f"{element['csr_port']}$", output['output'][i]['interface'], re.IGNORECASE):
                                index_fiber_csr = i
                                break

                        rx_power, tx_power, threshold_fiber_reading = False, False, {
							"10km": {
								'RxPower': [-14, 0.5],
								'TxPower': [-7.9, 1.5],
							},
							"40km": {
								'RxPower': [-15, -1],
								'TxPower': [-4.4, 5],
							},
							"80km": {
								'RxPower': [-23.1, -7],
								'TxPower': [-0.3, 5],
							}
						}

                        if index_fiber_csr is None:
                            output_message = f"No matching CSR {element['type']} port {element['csr_port']} in the fiber reading configurations"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            log_text(filename=log_file, content=output_message)
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = output_message

                        else:
                            # verify fiber reading based on distance
                            for i in list(threshold_fiber_reading.keys()):
                                if re.search(i, output['output'][index_fiber_csr]['type']):
                                    ports[j]['csr_sfp_distance'] = i
                                    if threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][0] < output['output'][index_fiber_csr]['RxPower'] < threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][1]:
                                        rx_power = True
                                    if threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][0] < output['output'][index_fiber_csr]['TxPower'] < threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][1]:
                                        tx_power = True
                                    break

                            # if rx_power = False and tx_power = False
                            if not rx_power and not tx_power:
                                output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = True and tx_power = False
                            elif rx_power and not tx_power:
                                output_message = f"Fiber reading (TxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = False and tx_power = True
                            elif not rx_power and tx_power:
                                output_message = f"Fiber reading (RxPower) for CSR {element['type']} port {element['csr_port']} ie {output['output'][index_fiber_csr]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['csr_sfp_distance']]['RxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message
                            
                            # if both is True
                            elif rx_power and tx_power:
                                output_message = f"Fiber readings (RxPower ({output['output'][index_fiber_csr]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_csr]['TxPower']}dBm)) for CSR {element['type']} port {element['csr_port']} are within thresholds"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                        j += 1

                final_output['fiber_reading'] = dict()
                final_output['fiber_reading']['csr'] = api_output
                api_output = dict()

                #                                            #
                #              VERIFY BFD STATUS             #
                #                                            #

                response = requests.get(csr_url+'v1/show_bfd_neighbor_all_brief/', params=payload_csr, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = f"An error has occured while retrieving bfd session for CSR"
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    api_output = {
                        'main': {
                            'status': 'undefined',
                            'details': error_message
                        },
                        'protect': {
                            'status': 'undefined',
                            'details': error_message
                        },
                    }

                else:
                    for element in ports:
                        index_bfd_csr, bfd_is_up = None, False
                        for i in range(len(output['output'])):
                            if re.search(f"{element['csr_port']}$", output['output'][i]['interface'], re.IGNORECASE):
                                index_bfd_csr = i
                                if re.search('up', output['output'][i]['state'], re.IGNORECASE):
                                    bfd_is_up = True
                                break

                        if index_bfd_csr is None:
                            output_message = f"No matching CSR {element['type']} port {element['csr_port']} in the bfd session configurations"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = output_message

                        else:
                            if not bfd_is_up:
                                output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port']}) is {output['output'][index_bfd_csr]['state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"BFD session state for CSR {element['type']} port ({element['csr_port']}) is UP"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                final_output['bfd_status'] = dict()
                final_output['bfd_status']['csr'] = api_output
                api_output = dict()

                #                                           #
                #              VERIFY CRC ERROR             #
                #                                           #
                for element in ports:

                    csr_check_crc_counter = 0
                    max_iterations = 3  # Define a maximum number of loop iterations
                    iterations = 0

                    while True:
                        iterations += 1
                        if iterations >= max_iterations:
                            error_message = "Maximum iterations reached while checking crc error. Exiting loop."
                            log_text(filename=log_file, content=error_message)
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = error_message
                            break

                        else:
                            payload = {
                                'port': element['csr_port']
                            }
                            payload.update(payload_csr)

                            response = requests.get(csr_url+'v1/show_port_crc/', params=payload, verify=False)
                            output = response.json()

                            if output['status'] == 'failed':
                                error_message = f"An error has occured while retrieving crc reading for CSR {element['type']} port ({element['csr_port']})"
                                output_message = error_message + \
                                    '\n[error message: {}]'.format(output['output'])
                                log_text(filename=log_file, content=output_message)
                                api_output[element['type']] = { 'status': 'undefined' }
                                api_output[element['type']]['details'] = error_message
                                break

                            else:
                                if output['output'] == "There is error returned from NE":
                                    output_message = f"An error has occured while retrieving crc reading for CSR {element['type']} port ({element['csr_port']})"
                                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                    api_output[element['type']] = { 'status': 'undefined' }
                                    api_output[element['type']]['details'] = output_message
                                    break

                                else:
                                    # check if crc = 0 
                                    if output['output']['In_CRC_ERROR'] == 0:
                                        output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port']} is 0"
                                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                        api_output[element['type']] = { 'status': True }
                                        api_output[element['type']]['details'] = output_message
                                        break

                                    else:
                                        if csr_check_crc_counter == 1:
                                            output_message = f"CRC error packet for CSR {element['type']} port {element['csr_port']} is greater than zero ({output['output']['In_CRC_ERROR']})"
                                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                            api_output[element['type']] = { 'status': False }
                                            api_output[element['type']]['details'] = output_message
                                            break

                                        else:
                                            #                                     #
                                            #              RESET PORT             #
                                            #                                     #
                                            payload = {
                                                'interface': element['csr_port']
                                            }
                                            payload.update(payload_csr)

                                            response = requests.get(csr_url+'v1/clear_statistics_interface/', params=payload, verify=False)
                                            output = response.json()

                                            if output['status'] == 'failed':
                                                error_message = f"An error has occured while trying to reset counter for CSR {element['type']} port ({element['csr_port']})"
                                                output_message = error_message + \
                                                    '\n[error message: {}]'.format(output['output'])
                                                log_text(filename=log_file, content=output_message)
                                                api_output[element['type']] = { 'status': 'undefined' }
                                                api_output[element['type']]['details'] = error_message
                                                break

                                            else:
                                                output_message = f"Successfully reset counter for CSR {element['type']} port ({element['csr_port']})"
                                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                                csr_check_crc_counter += 1  # increase counter = port has been reset once
                                                time.sleep(5)   # put 5s delay before re-check the CRC

                final_output['crc_error'] = dict()
                final_output['crc_error']['csr'] = api_output
                api_output = dict()

            ################ Logic for AGG ################

            if serialized_data['agg_vendor'] == 'Huawei':

                csr_url = 'http://pisa-huawei-svc/huawei/csr/'
                agg_url = 'http://pisa-huawei-svc/huawei/agg/'

                #                                                  #
                #              VERIFY INTERFACE STATUS             #
                #                                                  #
                
                for element in ports:
                    # AGG
                    payload = {
                        'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                        'interface': element['agg_port']
                    }

                    response = requests.get(csr_url+'v1/display_interface/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving AGG {element['type']} port {element['agg_port']} configurations"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output[element['type']]['status'] = 'undefined'
                        api_output[element['type']]['details'] = error_message

                    else:
                        if output['output'] == "There is no output returned from NE":
                            output_message = f"There is no matching AGG {element['type']} port {element['port']} configuration returned while checking fiber reading"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = output_message

                        else:

                            if output['output']['interface_current_state'] == 'UP' and output['output']['line_protocol'] == 'UP':
                                output_message = f"The AGG {element['type']} port {element['agg_port']} status is UP"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"The AGG {element['type']} port {element['agg_port']} current state is {output['output']['interface_current_state']} and line protocol state is {output['output']['line_protocol']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                final_output['interface_status']['agg'] = api_output
                api_output = dict()

                #                                               #
                #              VERIFY FIBER READING             #
                #                                               #

                j = 0
                for element in ports:

                    # get only the port number without port type e.g. 3/0/11 from 25GE3/0/11
                    agg_port_number = re.split(r"[a-zA-Z]+", element['agg_port'])[1].strip()

                    payload = {
                        'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                        'filter': agg_port_number
                    }

                    response = requests.get(agg_url+'v1/display_optical_module_brief/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving fiber reading for AGG"
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output = {
                            'main': {
                                'status': 'undefined',
                                'details': error_message
                            },
                            'protect': {
                                'status': 'undefined',
                                'details': error_message
                            },
                        }
                    
                    else:
                        index_fiber_agg = None
                        for i in range(len(output['output'])):
                            if re.search(f"{agg_port_number}$", output['output'][i]['port'], re.IGNORECASE):
                                index_fiber_agg = i
                                break

                        rx_power, tx_power, threshold_fiber_reading = False, False, {
                            "10km": {
                                'RxPower': [-14, 0.5],
                                'TxPower': [-7.898, -0.499],
                            },
                            "40km": {
                                'RxPower': [-15, -1],
                                'TxPower': [-4.4, 3.999],
                            },
                            "80km": {
                                'RxPower': [-23.1, -7],
                                'TxPower': [-0.3, 5],
                            },
                        }

                        if index_fiber_agg is None:
                            error_message = f"No matching AGG {element['type']} port {element['agg_port']} in the fiber reading configurations"
                            output_message = error_message + '\n[error message: {}]'.format(output['output'])
                            log_text(filename=log_file, content=output_message)
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = error_message

                        else:

                            # verify fiber reading based on distance
                            for i in list(threshold_fiber_reading.keys()):
                                if re.search(i, output['output'][index_fiber_agg]['type']):
                                    ports[j]['agg_sfp_distance'] = i
                                    if threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][0] < output['output'][index_fiber_agg]['RxPower'] < threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][1]:
                                        rx_power = True
                                    if threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][0] < output['output'][index_fiber_agg]['TxPower'] < threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][1]:
                                        tx_power = True
                                    break

                            # if rx_power = False and tx_power = False
                            if not rx_power and not tx_power:
                                output_message = f"Fiber reading (RxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = True and tx_power = False
                            elif rx_power and not tx_power:
                                output_message = f"Fiber reading (TxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = False and tx_power = True
                            elif not rx_power and tx_power:
                                output_message = f"Fiber reading (RxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message
                            
                            # if both is True
                            elif rx_power and tx_power:
                                output_message = f"Fiber readings (RxPower ({output['output'][index_fiber_agg]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_agg]['TxPower']}dBm)) for AGG {element['type']} port {element['agg_port']} are within thresholds"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                        j += 1

                final_output['fiber_reading']['agg'] = api_output
                api_output = dict()

                #                                              #
                #              VERIFY SFP DISTANCE             #
                #                                              #

                for element in ports:
                    # check if sfp match
                    if element['csr_sfp_distance'] == element['agg_sfp_distance']:
                        output_message = f"SFP distance for {element['type']} ports match between AGG ({element['agg_sfp_distance']}) and CSR ({element['csr_sfp_distance']})"
                        api_output[element['type']] = dict()
                        api_output[element['type']]['status'] = True
                        api_output[element['type']]['details'] = output_message

                    # check if sfp not match
                    else:
                        output_message = f"SFP distance for {element['type']} ports not match between AGG ({element['agg_sfp_distance']}) and CSR ({element['csr_sfp_distance']})"
                        api_output[element['type']] = dict()
                        api_output[element['type']]['status'] = False
                        api_output[element['type']]['details'] = output_message

                # sfp_main_match, sfp_protect_match = False, False
                # # verify main SFP distance
                # if ports[0]['csr_sfp_distance'] == ports[0]['agg_sfp_distance']:
                #     sfp_main_match = True

                # # verify protect SFP distance
                # if ports[1]['csr_sfp_distance'] == ports[1]['agg_sfp_distance']:
                #     sfp_protect_match = True

                # if not sfp_main_match and not sfp_protect_match:
                #     output_message_main = f"SFP distance for main ports not match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects not match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': False,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': False,
                #             'details': output_message_protect
                #         },
                #     }

                # elif not sfp_main_match and sfp_protect_match:
                #     output_message_main = f"SFP distance for main ports not match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': False,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': True,
                #             'details': output_message_protect
                #         },
                #     }

                # elif sfp_main_match and not sfp_protect_match:
                #     output_message_main = f"SFP distance for main ports match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects not match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': True,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': False,
                #             'details': output_message_protect
                #         },
                #     }

                # else:
                #     output_message_main = f"SFP distance for main ports match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': True,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': True,
                #             'details': output_message_protect
                #         },
                #     }
                
                final_output['sfp_distance'] = api_output
                api_output = dict()

                #                                            #
                #              VERIFY BFD STATUS             #
                #                                            #
                
                for element in ports:

                    payload = {
                        'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                        'filter': element['agg_port']
                    }

                    response = requests.get(agg_url+'v1/display_bfd_session_all/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving AGG {element['type']} port {element['agg_port']} bfd session configurations"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output[element['type']]['status'] = 'undefined'
                        api_output[element['type']]['details'] = error_message

                    else:
                        index_bfd_agg, bfd_agg_is_up = None, False
                        for i in range(len(output['output'])):
                            if re.search(f"{element['agg_port']}$", output['output'][i]['int_name'], re.IGNORECASE):
                                index_bfd_agg = i
                                if re.search('up', output['output'][i]['state'], re.IGNORECASE):
                                    bfd_agg_is_up = True
                                break

                        if index_bfd_agg is None:
                            output_message = f"No matching AGG {element['type']} port {element['agg_port']} in the bfd session configurations"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = output_message

                        else:
                            if not bfd_is_up:
                                output_message = f"BFD session state for AGG {element['type']} port ({element['agg_port']}) is {output['output'][index_bfd_agg]['state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"BFD session state for AGG {element['type']} port ({element['agg_port']}) is {output['output'][index_bfd_agg]['state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                final_output['bfd_status']['agg'] = api_output
                api_output = dict()

                #                                           #
                #              VERIFY CRC ERROR             #
                #                                           #
                for element in ports:

                    agg_check_crc_counter = 0
                    max_iterations = 3  # Define a maximum number of loop iterations
                    iterations = 0

                    while True:

                        iterations += 1
                        if iterations >= max_iterations:
                            error_message = "Maximum iterations reached while checking crc error. Exiting loop."
                            log_text(filename=log_file, content=error_message)
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = error_message
                            break

                        else:
                            payload = {
                                'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                                'interface': element['agg_port']
                            }

                            response = requests.get(agg_url+'v1/verify_crc_packet_interface/', params=payload, verify=False)
                            output = response.json()

                            if output['status'] == 'failed':
                                error_message = f"An error has occured while retrieving crc reading for AGG {element['type']} port ({element['agg_port']})"
                                output_message = error_message + \
                                    '\n[error message: {}]'.format(output['output'])
                                log_text(filename=log_file, content=output_message)
                                api_output[element['type']] = { 'status': 'undefined' }
                                api_output[element['type']]['details'] = error_message
                                break

                            else:
                                if output['output'] == "The crc output does not exist":
                                    output_message = f"There is no matching AGG {element['type']} port {element['agg_port']} configuration returned while checking crc reading"
                                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                    api_output[element['type']] = { 'status': 'undefined' }
                                    api_output[element['type']]['details'] = output_message
                                    break

                                else:
                                    # check if crc = 0 
                                    if output['output']['crc'] == 0:
                                        output_message = f"CRC error packet for AGG {element['type']} port {element['agg_port']} is {output['output']['crc']}"
                                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                        api_output[element['type']] = { 'status': True }
                                        api_output[element['type']]['details'] = output_message
                                        break

                                    else:
                                        if agg_check_crc_counter == 1:
                                            output_message = f"CRC error packet for AGG {element['type']} port {element['agg_port']} is greater than zero ({output['output']['crc']})"
                                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                            api_output[element['type']] = { 'status': False }
                                            api_output[element['type']]['details'] = output_message
                                            break

                                        else:
                                            #                                     #
                                            #              RESET PORT             #
                                            #                                     #
                                            payload = {
                                                'hostname': serialized_data['agg_hostname'],
                                                'interface': element['agg_port']
                                            }

                                            response = requests.get(agg_url+'v1/reset_counters_interface/', params=payload, verify=False)
                                            output = response.json()

                                            if output['status'] == 'failed':
                                                error_message = f"An error has occured while trying to reset counter for AGG {element['type']} port ({element['agg_port']})"
                                                output_message = error_message + \
                                                    '\n[error message: {}]'.format(output['output'])
                                                log_text(filename=log_file, content=output_message)
                                                api_output[element['type']] = { 'status': 'undefined' }
                                                api_output[element['type']]['details'] = error_message
                                                break

                                            else:
                                                output_message = f"Successfully reset counter for AGG {element['type']} port ({element['agg_port']})"
                                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                                agg_check_crc_counter += 1  # increase counter = port has been reset once
                                                time.sleep(5)   # put 5s delay before re-check the CRC

                final_output['crc_error']['agg'] = api_output
                api_output = dict()

            else:

                csr_url = 'http://pisa-zte-svc/zte/csr/'
                agg_url = 'http://pisa-zte-svc/zte/agg/'

                #                                                  #
                #              VERIFY INTERFACE STATUS             #
                #                                                  #
                
                for element in ports:
                    # AGG
                    payload = {
                        'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                        'interface': element['agg_port'],
                        'filter': element['agg_port']
                    }

                    response = requests.get(csr_url+'v1/show_interface/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving AGG {element['type']} port {element['agg_port']} configurations"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output[element['type']]['status'] = 'undefined'
                        api_output[element['type']]['details'] = error_message

                    else:
                        if output['output'] == "There is no output returned from NE":
                            output_message = f"There is no matching AGG {element['type']} port {element['port']} configuration returned while checking fiber reading"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = output_message

                        else:

                            if output['output']['interface_state'] == 'up':
                                output_message = f"The AGG {element['type']} port {element['agg_port']} status is UP"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"The AGG {element['type']} port {element['agg_port']} current state is {output['output']['interface_state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                final_output['interface_status']['agg'] = api_output
                api_output = dict()

                #                                               #
                #              VERIFY FIBER READING             #
                #                                               #

                j = 0
                for element in ports:

                    payload = {
                        'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                        'filter': element['agg_port']
                    }

                    response = requests.get(agg_url+'v1/show_optical_info_brief/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving fiber reading for AGG"
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output = {
                            'main': {
                                'status': 'undefined',
                                'details': error_message
                            },
                            'protect': {
                                'status': 'undefined',
                                'details': error_message
                            },
                        }
                    
                    else:
                        index_fiber_agg = None
                        for i in range(len(output['output'])):
                            if re.search(f"{element['agg_port']}$", output['output'][i]['port'], re.IGNORECASE):
                                index_fiber_agg = i
                                break

                        rx_power, tx_power, threshold_fiber_reading = False, False, {
                            "10km": {
                                'RxPower': [-14, 0.5],
                                'TxPower': [-7.9, 1.5],
                            },
                            "40km": {
                                'RxPower': [-15, -1],
                                'TxPower': [-4.4, 5],
                            },
                            "80km": {
                                'RxPower': [-23.1, -7],
                                'TxPower': [-0.3, 5],
                            },
                        }

                        if index_fiber_agg is None:
                            error_message = f"No matching AGG {element['type']} port {element['agg_port']} in the fiber reading configurations"
                            output_message = error_message + '\n[error message: {}]'.format(output['output'])
                            log_text(filename=log_file, content=output_message)
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = error_message

                        else:

                            # verify fiber reading based on distance
                            for i in list(threshold_fiber_reading.keys()):
                                if re.search(i, output['output'][index_fiber_agg]['type']):
                                    ports[j]['agg_sfp_distance'] = i
                                    if threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][0] < output['output'][index_fiber_agg]['RxPower'] < threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][1]:
                                        rx_power = True
                                    if threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][0] < output['output'][index_fiber_agg]['TxPower'] < threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][1]:
                                        tx_power = True
                                    break

                            # if rx_power = False and tx_power = False
                            if not rx_power and not tx_power:
                                output_message = f"Fiber reading (RxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][1]}dBm and fiber reading (TxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = True and tx_power = False
                            elif rx_power and not tx_power:
                                output_message = f"Fiber reading (TxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['TxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['TxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            # if rx_power = False and tx_power = True
                            elif not rx_power and tx_power:
                                output_message = f"Fiber reading (RxPower) for AGG {element['type']} port {element['agg_port']} ie {output['output'][index_fiber_agg]['RxPower']}dBm is not within threshold {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][0]}dBm and {threshold_fiber_reading[ports[j]['agg_sfp_distance']]['RxPower'][1]}dBm"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message
                            
                            # if both is True
                            elif rx_power and tx_power:
                                output_message = f"Fiber readings (RxPower ({output['output'][index_fiber_agg]['RxPower']}dBm) and TxPower ({output['output'][index_fiber_agg]['TxPower']}dBm)) for AGG {element['type']} port {element['agg_port']} are within thresholds"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                        j += 1

                final_output['fiber_reading']['agg'] = api_output
                api_output = dict()

                #                                              #
                #              VERIFY SFP DISTANCE             #
                #                                              #

                for element in ports:
                    # check if sfp match
                    if element['csr_sfp_distance'] == element['agg_sfp_distance']:
                        output_message = f"SFP distance for {element['type']} ports match between AGG ({element['agg_sfp_distance']}) and CSR ({element['csr_sfp_distance']})"
                        api_output[element['type']] = dict()
                        api_output[element['type']]['status'] = True
                        api_output[element['type']]['details'] = output_message

                    # check if sfp not match
                    else:
                        output_message = f"SFP distance for {element['type']} ports not match between AGG ({element['agg_sfp_distance']}) and CSR ({element['csr_sfp_distance']})"
                        api_output[element['type']] = dict()
                        api_output[element['type']]['status'] = False
                        api_output[element['type']]['details'] = output_message

                # sfp_main_match, sfp_protect_match = False, False
                # # verify main SFP distance
                # if ports[0]['csr_sfp_distance'] == ports[0]['agg_sfp_distance']:
                #     sfp_main_match = True

                # # verify protect SFP distance
                # if ports[1]['csr_sfp_distance'] == ports[1]['agg_sfp_distance']:
                #     sfp_protect_match = True

                # if not sfp_main_match and not sfp_protect_match:
                #     output_message_main = f"SFP distance for main ports not match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects not match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': False,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': False,
                #             'details': output_message_protect
                #         },
                #     }

                # elif not sfp_main_match and sfp_protect_match:
                #     output_message_main = f"SFP distance for main ports not match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': False,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': True,
                #             'details': output_message_protect
                #         },
                #     }

                # elif sfp_main_match and not sfp_protect_match:
                #     output_message_main = f"SFP distance for main ports match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects not match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': True,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': False,
                #             'details': output_message_protect
                #         },
                #     }

                # else:
                #     output_message_main = f"SFP distance for main ports match between AGG ({ports[0]['agg_sfp_distance']}) and CSR ({ports[0]['csr_sfp_distance']})"
                #     output_message_protect = f"SFP distance for protect protects match between AGG ({ports[1]['agg_sfp_distance']}) and CSR ({ports[1]['csr_sfp_distance']})"
                #     api_output = {
                #         'main': {
                #             'status': True,
                #             'details': output_message_main
                #         },
                #         'protect': {
                #             'status': True,
                #             'details': output_message_protect
                #         },
                #     }
                
                final_output['sfp_distance'] = api_output
                api_output = dict()

                #                                            #
                #              VERIFY BFD STATUS             #
                #                                            #
                
                for element in ports:

                    payload = {
                        'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                        'filter': element['agg_port']
                    }

                    response = requests.get(agg_url+'v1/show_bfd_neighbor_all_brief/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = f"An error has occured while retrieving AGG {element['type']} port {element['agg_port']} bfd session configurations"
                        output_message = error_message + \
                            '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        api_output[element['type']]['status'] = 'undefined'
                        api_output[element['type']]['details'] = error_message

                    else:
                        index_bfd_agg, bfd_agg_is_up = None, False
                        for i in range(len(output['output'])):
                            if re.search(f"{element['agg_port']}$", output['output'][i]['interface'], re.IGNORECASE):
                                index_bfd_agg = i
                                if re.search('up', output['output'][i]['state'], re.IGNORECASE):
                                    bfd_agg_is_up = True
                                break

                        if index_bfd_agg is None:
                            output_message = f"No matching AGG {element['type']} port {element['agg_port']} in the bfd session configurations"
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            api_output[element['type']] = { 'status': 'undefined' } 
                            api_output[element['type']]['details'] = output_message

                        else:
                            if not bfd_is_up:
                                output_message = f"BFD session state for AGG {element['type']} port ({element['agg_port']}) is {output['output'][index_bfd_agg]['state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': False }
                                api_output[element['type']]['details'] = output_message

                            else:
                                output_message = f"BFD session state for AGG {element['type']} port ({element['agg_port']}) is {output['output'][index_bfd_agg]['state']}"
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                api_output[element['type']] = { 'status': True }
                                api_output[element['type']]['details'] = output_message

                final_output['bfd_status']['agg'] = api_output
                api_output = dict()

                #                                           #
                #              VERIFY CRC ERROR             #
                #                                           #
                for element in ports:

                    agg_check_crc_counter = 0
                    max_iterations = 3  # Define a maximum number of loop iterations
                    iterations = 0

                    while True:

                        iterations += 1
                        if iterations >= max_iterations:
                            error_message = "Maximum iterations reached while checking crc error. Exiting loop."
                            log_text(filename=log_file, content=error_message)
                            api_output[element['type']] = { 'status': 'undefined' }
                            api_output[element['type']]['details'] = error_message
                            break

                        else:
                            payload = {
                                'ne_ip': f"{serialized_data['agg_hostname']}.tmone.my",
                                'port': element['agg_port']
                            }

                            response = requests.get(agg_url+'v1/show_port_crc/', params=payload, verify=False)
                            output = response.json()

                            if output['status'] == 'failed':
                                error_message = f"An error has occured while retrieving crc reading for AGG {element['type']} port ({element['agg_port']})"
                                output_message = error_message + \
                                    '\n[error message: {}]'.format(output['output'])
                                log_text(filename=log_file, content=output_message)
                                api_output[element['type']] = { 'status': 'undefined' }
                                api_output[element['type']]['details'] = error_message
                                break

                            else:
                                if output['output'] == "There is error returned from NE": 
                                    output_message = f"An error has occured while retrieving crc reading for AGG {element['type']} port ({element['agg_port']})"
                                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                    api_output[element['type']] = { 'status': 'undefined' }
                                    api_output[element['type']]['details'] = output_message
                                    break

                                else:
                                    # check if crc = 0 
                                    if output['output']['In_CRC_ERROR'] == 0:
                                        output_message = f"CRC error packet for AGG {element['type']} port {element['agg_port']} is {output['output']['In_CRC_ERROR']}"
                                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                        api_output[element['type']] = { 'status': True }
                                        api_output[element['type']]['details'] = output_message
                                        break

                                    else:
                                        if agg_check_crc_counter == 1:
                                            output_message = f"CRC error packet for AGG {element['type']} port {element['agg_port']} is greater than 0 ({output['output']['In_CRC_ERROR']})"
                                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                            api_output[element['type']] = { 'status': False }
                                            api_output[element['type']]['details'] = output_message
                                            break

                                        else:
                                            #                                     #
                                            #              RESET PORT             #
                                            #                                     #
                                            payload = {
                                                'hostname': serialized_data['agg_hostname'],
                                                'interface': element['agg_port']
                                            }

                                            response = requests.get(agg_url+'v1/clear_statistics_interface/', params=payload, verify=False)
                                            output = response.json()

                                            if output['status'] == 'failed':
                                                error_message = f"An error has occured while trying to reset counter for AGG {element['type']} port ({element['agg_port']})"
                                                output_message = error_message + \
                                                    '\n[error message: {}]'.format(output['output'])
                                                log_text(filename=log_file, content=output_message)
                                                api_output[element['type']] = { 'status': 'undefined' }
                                                api_output[element['type']]['details'] = error_message
                                                break

                                            else:
                                                output_message = f"Successfully reset counter for AGG {element['type']} port ({element['agg_port']})"
                                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                                agg_check_crc_counter += 1  # increase counter = port has been reset once
                                                time.sleep(5)   # put 5s delay before re-check the CRC

                final_output['crc_error']['agg'] = api_output
                api_output = dict()

            # for testing
            # return Response({'output': final_output, 'data': serialized_data})

            #                                                #
            #              OUTPUT FINAL MESSAGE              #
            #                                                #

            if 'undefined' in str(final_output) or 'False' in str(final_output):
                output_message = f"Successfully run verifications on AGG and CSR but some of the verification items contains errors"
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'Verification',
                    'service_type': '5G',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': str(final_output),
                    'status': 'failed',
                    'log_file': log_file,
                    'user': user
                }
                api_response = {
                    'status': 'failed',
                    'message': output_message,
                    'data': final_output,
                    'log_file': log_file
                }
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_200_OK)

            else:
                output_message = f"Successfully run verifications on AGG and CSR and all verification items passed"
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'Verification',
                    'service_type': '5G',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': str(final_output),
                    'status': 'success',
                    'log_file': log_file,
                    'user': user
                }
                api_response = {
                    'status': 'success',
                    'message': output_message,
                    'data': final_output,
                    'log_file': log_file
                }
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_200_OK)

        
        except lldpException as error:
            output_message = str(error)
            # log_text(filename=log_file, content=output_message)
            DB_data = {
                'order_type': 'Verification',
                'service_type': '5G',
                'input_details': serialized_data,
                'message': output_message,
                'api_output': output_message,
                'status': 'failed',
                'log_file': log_file,
                'user': user
            }
            api_response = {
                'status': 'failed',
                'message': output_message,
                'data': output_message,
                'log_file': log_file
            }
            log_database(data_DB=DB_data)
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)


        except Exception as error:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            out_error = {
                'error_type': str(exc_type),
                'error_details': str(exc_obj),
                'error_line': str(exc_tb.tb_lineno),
            }

            error_message = 'Unexpected error has occurred while executing API'
            output_message = f"{error_message}\n[error message: {error}]"
            if 'log_file' not in locals():
                log_file = ''
            log_text(filename=log_file, content=output_message, ne_output=dumps(out_error, indent=4, separators=(',', ': ')))

            DB_data = {
                'order_type': 'N/A',
                'service_type': '5G',
                'input_details': request.data,
                'message': error_message,
                'api_output': str(error),
                'status': 'failed',
                'log_file': log_file,
                'config_file': 'Undefined',
                'user': user if 'user' in locals() else 'not_available'
            }

            api_response = {
                'status': 'failed',
                'message': error_message,
                'data': out_error,
                'log_file': log_file
            }

            log_database(data_DB=DB_data)
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
