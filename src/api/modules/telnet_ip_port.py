from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.serializers import telnetIpSerializer
import subprocess, sys, telnetlib, socket
from drf_yasg.utils import swagger_auto_schema


class api(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        operation_description="telnet <network_element_ip> <port_number>",
        query_serializer=telnetIpSerializer,
        # responses={
        #     status.HTTP_200_OK: "Successfully ping destination ip",
        #     status.HTTP_400_BAD_REQUEST: "An error has occurred",
        # },
    )

    def get(self, request):

        # verify passed data
        input_data = telnetIpSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        
        else:
            try:
                # Initialize variables using the passed data
                serialized_data = input_data.data
                self.host = serialized_data['ip']
                self.port = serialized_data['port']

                if self.telnet():
                    return Response({
                        "status": "success",
                        "output": f"Host {self.host} with port {self.port} is reachable",
                        # "ne_response": output
                    }, status=status.HTTP_200_OK)
                
                else:
                    return Response({
                        "status": "failed",
                        "output": f"Host {self.host} with port {self.port} is unreachable",
                        # "ne_response": output
                    }, status=status.HTTP_200_OK)

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))

                return Response({'status': 'failed', 'output': str(error), 'details': output_error}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    # telnet function
    def telnet(self):
        try:
            # try telnet to host with port
            tn = telnetlib.Telnet(self.host, self.port, timeout=1)
            return True
        
        except socket.timeout:
            return False


