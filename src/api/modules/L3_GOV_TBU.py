from rest_framework.views import APIView  # activate REST //post,get,put,delete
# return Response JSON associative array
from rest_framework.response import Response
from rest_framework import status  # error status code 404
from api.serializers import TbuSerializer
import requests
import json
import time
import re
import os
import sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps


class API(APIView):

    # authentication_classes = ()  # exclude from global authentication
    # permission_classes = ()

    @swagger_auto_schema(
        request_body=TbuSerializer,
        responses={201: TbuSerializer()},
        operation_summary="Workflow for Temporary Bandwidth Upgrade layer 3 NE for 1GOV"
    )
    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(
                    key=token.replace('Token ', '')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = TbuSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid circuit details fetched by API from UNIS'
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': request.data,
                    'message': output_message,
                    'api_output': str(input_data.errors),
                    'status': 'failed',
                    'log_file': 'N/A',
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = '1GOV_TBU_{}_{}_{}@{}_by_{}.txt'.format(
                serialized_data['service_id'], serialized_data['node_name'], date_now, time_now, user)		# Create Log File

            # convert the format of date to srting to solve issue 'object of type date is not json serializable'

            if 'start_date' in serialized_data:
                serialized_data['start_date'] = str(serialized_data['start_date'])

            if 'end_date' in serialized_data:
                serialized_data['end_date'] = str(serialized_data['end_date'])

            # LOG CREATED TIME #
            output_message = "File created at {} {}".format(date_now, time_now)
            log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(
                request.data, indent=4, separators=(',', ': ')))

            #                                                #
            #              CHECK SERVICE TYPE                #
            #                                                #
            # if not '1GOV Service (Metro Ethernet)' in serialized_data['product_name']:
            #     output_message = 'Invalid product name ({}). Only product 1GOV Service (Metro Ethernet) is allowed'.format(serialized_data['product_name'])
            #     log_text(filename=log_file, content=output_message)
            #     DB_data = {
            #         'order_type':'TBU',
            #         'service_type':'1GOV',
            #         'input_details':serialized_data,
            #         'message':output_message,
            #         'api_output':output_message,
            #         'status':'failed',
            #         'log_file':log_file,
            #         'config_file': 'N/A',
            #         'user': user
            #     }
            #     api_response['status'] = 'failed'
            #     api_response['message'] = output_message
            #     api_response['data'] = output_message
            #     log_database(data_DB=DB_data)
            #     return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                            #
            #              CHECK VPN NAME                #
            #                                            #
            if not re.search('gov_', serialized_data['vpn_name'], re.IGNORECASE):
                output_message = "Invalid VPN name ({}). Missing GOV_".format(
                    serialized_data['vpn_name'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': output_message,
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output_message
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            #                                      #
            #        GET INTERFACE DETAILS         #
            #                                      #

            # sample API output
            # "output": {
            # 	"name": "xe-7/3/0",
            # 	"unit": [
            # 		{
            # 			"name": 1020,
            # 			"description": "IPVPN - DORADO - SVC NO:PS3000162419",
            # 			"bandwidth": "8m",
            # 			"vlan-id": 1020,
            # 			"family": {
            # 				"inet": {
            # 					"policer": {
            # 						"input": "POLICER_8M_10GPIC"
            # 					},
            # 					"address": [
            # 					{
            # 						"name": "**************/30"
            # 					}
            # 					]
            # 				}
            # 			}
            # 		}
            # 	]
            # }

            url = 'http://pisa-juniper-svc/juniper/pe/'  # declare main url variable

            payload = {
                'ne_ip': serialized_data['device_id'],
                'interface': serialized_data['interface']
            }
            response = requests.get(
                url+'v2/show_interface/', params=payload, verify=False)
            output = response.json()

            if output['status'] == 'failed':
                error_message = 'An error has occured while retrieving interface ({}) details'.format(
                    serialized_data['interface'])
                output_message = error_message + \
                    '\n[error message: {}]'.format(output['output'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': error_message,
                    'api_output': str(output['output']),
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if output['output'] == 'interface is not exist':
                    error_message = 'Interface ({}) is not exist in PE ({})'.format(
                        serialized_data['interface'], serialized_data['node_name'])
                    log_text(filename=log_file, content=error_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    interface_details = output['output']
                    output_message = 'Sucessfully retrieved interface ({}) details'.format(
                        serialized_data['interface'])
                    log_text(filename=log_file, content=output_message, ne_output=dumps(
                        interface_details, indent=4, separators=(',', ': ')))


            #                                                                   #
            #        CHECK PE INTERFACE DESCRIPTION CONTAINS SERVICE ID         #
            #                                                                   #

            if not re.search(serialized_data['service_id'], interface_details['unit']['description'], re.IGNORECASE):
                error_message = f"PE interface description does not contains service id ({serialized_data['service_id']})"
                output_message = error_message + "\n[error message: Interface description retrieved from NE is '{}']".format(interface_details['unit']['description'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': error_message,
                    'api_output': f"PE interface description retrieved from NE is {interface_details['unit']['description']}",
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = f"PE interface description retrieved from NE is {interface_details['unit']['description']}"
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
            else:
                output_message = f"PE interface description ({interface_details['unit']['description']}) contains service id ({serialized_data['service_id']})"
                log_text(filename=log_file, content=output_message)

            #                                                #
            #        CHECK PE IP AND INTERFACE MATCH         #
            #                                                #

            # pe_wan_ip_subnet = interface_details['unit']['family']['inet']['address']['name']
            # pe_wan_ip = pe_wan_ip_subnet.split('/')[0]

            # if pe_wan_ip == serialized_data['pe_wan_ip']:
            #     output_message = 'PE IP Address ({}) match with Interface ({})'.format(
            #         serialized_data['pe_wan_ip'], serialized_data['interface'])
            #     log_text(filename=log_file, content=output_message)
            # else:
            #     error_message = 'Mismatched PE IP Address ({}) and Interface ({})'.format(
            #         serialized_data['pe_wan_ip'], serialized_data['interface'])
            #     output_message = error_message + \
            #         '\n[error message: PE IP Address retrieved from NE is {}]'.format(
            #             pe_wan_ip)
            #     log_text(filename=log_file, content=output_message)
            #     DB_data = {
            #         'order_type': 'TBU',
            #         'service_type': '1GOV',
            #         'input_details': serialized_data,
            #         'message': error_message,
            #         'api_output': 'PE IP Address retrieved from NE is {}'.format(pe_wan_ip),
            #         'status': 'failed',
            #         'log_file': log_file,
            #         'config_file': 'N/A',
            #         'user': user
            #     }
            #     api_response['status'] = 'failed'
            #     api_response['message'] = error_message
            #     api_response['data'] = 'PE IP Address retrieved from NE is {}'.format(
            #         pe_wan_ip)
            #     log_database(data_DB=DB_data)
            #     return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            #                                                    #
            #        CHECK PE INT DESCRIPTION (EXTENSION)        #
            #                                                    #

            # if re.search('extension', interface_details['unit']['description'], re.IGNORECASE):
            #     error_message = 'Change of bandwidth is not required due to BOD extension'
            #     output_message = error_message + "\n[error message: Interface description retrieved from NE is '{}']".format(interface_details['unit']['description'])
            #     log_text(filename=log_file, content=output_message)
            #     DB_data = {
            #         'order_type':'TBU',
            #         'service_type':'1GOV',
            #         'input_details':serialized_data,
            #         'message':error_message,
            #         'api_output':"Interface description retrieved from NE is '{}'".format(interface_details['unit']['description']),
            #         'status':'failed',
            #         'log_file':log_file,
            #         'config_file': 'N/A',
            #         'user': user
            #     }
            #     api_response['status'] = 'failed'
            #     api_response['message'] = error_message
            #     api_response['data'] = "Interface description retrieved from NE is '{}'".format(interface_details['unit']['description'])
            #     log_database(data_DB=DB_data)
            #     return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                            #
            #        GET SITE ABB        #
            #                            #

            interface_desc = interface_details['unit']['description']
            site_abbr = interface_desc.split('-')[1].replace(" ", "")

            if len(site_abbr) == 3:
                output_message = 'Successfully retrieved site abbreviation ({})'.format(
                    site_abbr)
                log_text(filename=log_file, content=output_message)
            else:
                error_message = 'Invalid site abbreviation ({}) i.e. more/less than 3 characters'.format(
                    site_abbr)
                output_message = error_message + \
                    "\n[error message: Interface description retrieved from NE is '{}']".format(
                        interface_details['unit']['description'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': error_message,
                    'api_output': "Interface description retrieved from NE is '{}'".format(interface_details['unit']['description']),
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = "Interface description retrieved from NE is '{}'".format(
                    interface_details['unit']['description'])
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

            #                                        #
            #        CHECK INTERFACE BW EXIST        #
            #                                        #

            bw = str()
            if 'bandwidth' in interface_details['unit'].keys():
                bw = interface_details['unit']['bandwidth']
            else:
                #                                          #
                #        GET INTERFACE COS DETAILS         #
                #                                          #

                # sample API output
                # "output": {
                # 	"name": "xe-7/3/0",
                # 	"unit": [{
                # 		"name": 1020,
                # 		"scheduler-map": "SCHED_CONVERGENCE",
                # 		"shaping-rate": {
                # 			"rate": "8m"
                # 		},
                # 		"classifiers": {
                # 			"inet-precedence": {
                # 			"classifier-name": "COS_CONVERGENCE"
                # 			}
                # 		}
                # 	}]
                # }

                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'interface': serialized_data['interface']
                }
                response = requests.get(
                    url+'v2/show_interface_cos/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while retrieving interface ({}) COS details'.format(
                        serialized_data['interface'])
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] == 'interface CoS is not exist':
                        error_message = 'Interface ({}) CoS is not exist in PE ({})'.format(
                            serialized_data['interface'], serialized_data['node_name'])
                        log_text(filename=log_file, content=error_message)
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': output['output'],
                            'status': 'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        interface_cos_details = output['output']
                        output_message = 'Successfully retrieved interface ({}) COS details'.format(
                            serialized_data['interface'])
                        log_text(filename=log_file, content=output_message, ne_output=dumps(
                            interface_cos_details, indent=4, separators=(',', ': ')))

                        bw = interface_cos_details['unit']['shaping-rate']['rate']

            #                                   #
            #        COMPARE EXISTING BW        #
            #                                   #

            # check bw condition if start_date and end_date exist
            if 'start_date' in serialized_data and 'end_date' in serialized_data:

                if not re.match(bw, serialized_data['old_bandwidth'], re.IGNORECASE) and not re.match(bw, serialized_data['new_bandwidth'], re.IGNORECASE):
                    output_message = 'Existing bandwidth ({}) is not match old bandwidth ({}) and new bandwidth ({})'.format(
                        bw, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': output_message,
                        'api_output': 'Existing bandwidth ({}) retrieved from PE'.format(bw),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = output_message
                    api_response['data'] = 'Existing bandwidth ({}) retrieved from PE'.format(
                        bw)
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                if re.match(bw, serialized_data['new_bandwidth'], re.IGNORECASE):
                    output_message = 'Existing bandwidth ({}) match new bandwidth ({}). No change is required at PE'.format(
                        bw, serialized_data['new_bandwidth'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': output_message,
                        'api_output': 'Existing bandwidth ({}) retrieved from PE'.format(bw),
                        'status': 'success',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'success'
                    api_response['message'] = output_message
                    api_response['data'] = 'Existing bandwidth ({}) retrieved from PE'.format(
                        bw)
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_200_OK)

        # if re.match(bw, serialized_data['old_bandwidth'], re.IGNORECASE):
            #                                                #
            #               CHECK BGP Status                 #
            #                                                #

            # payload = {
            #     'ne_ip': serialized_data['device_id'],
            #     'vpn': serialized_data['vpn_name'],
            #     'ce_ip': serialized_data['ce_wan_ip']
            # }
            # response = requests.get(url+'v2/get_bgp_summary/', params=payload, verify=False)
            # output = response.json()

            # if output['status'] == 'failed':
            #     error_message = 'An error has occured while retrieving BGP status from PE'
            #     output_message = error_message + '\n[error message: {}]'.format(output['output'])
            #     log_text(filename=log_file, content=output_message)
            #     DB_data = {
            #         'order_type':'TBU',
            #         'service_type':'1GOV',
            #         'input_details':serialized_data,
            #         'message':error_message,
            #         'api_output':str(output['output']),
            #         'status':'failed','log_file': log_file,
            #         'config_file': 'N/A',
            #         'user': user
            #     }
            #     api_response['status'] = 'failed'
            #     api_response['message'] = error_message
            #     api_response['data'] = str(output['output'])
            #     log_database(data_DB=DB_data)
            #     return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            # else:
            #     if output['output'] == 'Invalid BGP status':
            #         output_message = 'Invalid BGP status for CE IP Address ({}) returned from PE'.format(serialized_data['ce_wan_ip'])
            #         log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
            #         DB_data = {
            #             'order_type':'TBU',
            #             'service_type':'1GOV',
            #             'input_details':serialized_data,
            #             'message':output_message,
            #             'api_output':'BGP status retrieved from PE is ({})'.format(output['output']),
            #             'status':'failed',
            #             'log_file': log_file,
            #             'config_file': 'N/A',
            #             'user': user
            #         }
            #         api_response['status'] = 'failed'
            #         api_response['message'] = output_message
            #         api_response['data'] = 'BGP status retrieved from PE is ({})'.format(output['output'])
            #         log_database(data_DB=DB_data)
            #         return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            #     else:
            #         if output['output']['state'] != 'Established':
            #             output_message = 'BGP status for CE IP Address ({}) returned from PE ({}) is not established'.format(serialized_data['ce_wan_ip'], output['output']['state'])
            #             log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
            #             DB_data = {
            #                 'order_type':'TBU',
            #                 'service_type':'1GOV',
            #                 'input_details':serialized_data,
            #                 'message':output_message,
            #                 'api_output':'BGP status retrieved from PE is ({})'.format(output['output']['state']),
            #                 'status':'failed',
            #                 'log_file': log_file,
            #                 'config_file': 'N/A',
            #                 'user': user
            #             }
            #             api_response['status'] = 'failed'
            #             api_response['message'] = output_message
            #             api_response['data'] = 'BGP status retrieved from PE is ({})'.format(output['output']['state'])
            #             log_database(data_DB=DB_data)
            #             return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            #         else:
            #             output_message = 'BGP status for CE IP Address ({}) returned from PE is ({})'.format(serialized_data['ce_wan_ip'], output['output']['state'])
            #             log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

            #                                                 #
            #               CHECK INPUT POLICER               #
            #                                                 #

            interface_policer, global_policer = False, False
            # check if interface details dictionary key contains policer and input
            if 'policer' in interface_details['unit']['family']['inet'].keys():
                if 'input' in interface_details['unit']['family']['inet']['policer'].keys():
                    interface_policer = True

                    #                                                        #
                    #               CHECK GLOBAL POLICER EXIST               #
                    #                                                        #
                    # sample output
                    # "output": {
                    # 	"name": "POLICER_8M_10GPIC",
                    # 	"logical-interface-policer": [
                    # 		null
                    # 	],
                    # 	"if-exceeding": {
                    # 		"bandwidth-limit": "8m",
                    # 		"burst-size-limit": 6250000
                    # 	},
                    # 	"then": {
                    # 		"discard": [
                    # 			null
                    # 		]
                    # 	}
                    # }

                    # Generate new policer name
                    policer_name = str()
                    if 'ge' in interface_details['name']:
                        policer_name = 'POLICER_{}_1GPIC'.format(
                            serialized_data['new_bandwidth'].upper())
                    elif 'xe' in interface_details['name']:
                        policer_name = 'POLICER_{}_10GPIC'.format(
                            serialized_data['new_bandwidth'].upper())

                    payload = {
                        'ne_ip': serialized_data['device_id'],
                        'policer': policer_name
                    }

                    response = requests.get(
                        url+'v2/check_global_policer/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = 'An error has occured while retrieving Global Policer from PE'
                        output_message = error_message + \
                            '\n[error message: {}]'.format(
                                output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type': 'TBU',
                            'service_type': '1GOV',
                            'input_details': serialized_data,
                            'message': error_message,
                            'api_output': str(output['output']),
                            'status': 'failed', 'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = str(output['output'])
                        log_database(data_DB=DB_data)
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        if output['output'] == 'policer is not exist':
                            # global_policer = False
                            output_message = 'Global Policer ({}) is not exist in PE'.format(
                                policer_name)
                            log_text(
                                filename=log_file, content=output_message, ne_output=output['output'])
                        else:
                            global_policer = True
                            output_message = 'Global Policer ({}) is exist in PE'.format(
                                policer_name)
                            log_text(filename=log_file, content=output_message, ne_output=dumps(
                                output['output'], indent=4, separators=(',', ': ')))

            #                                       #
            #          CHECK FAMILY INETv6          #
            #                                       #

            # sample output
            # {
            # 	"name": "xe-7/3/0",
            # 	"unit": [
            # 	{
            # 		"name": 1020,
            # 		"description": "IPVPN - DORADO - SVC NO:PS3000162419",
            # 		"bandwidth": "8m",
            # 		"vlan-id": 1020,
            # 		"family": {
            # 			"inet": {
            # 				"policer": {
            # 					"input": "POLICER_8M_10GPIC"
            # 				},
            # 				"address": [
            # 				{
            # 					"name": "**************/30"
            # 				}
            # 				]
            # 			},
            # 			"inet6": {
            # 				"policer": {
            # 					"input": "POLICER_8M_10GPIC"
            # 				},
            # 				"address": [
            # 				{
            # 					"name": "2400:7400:0002:0000:0000:0000:0000:0b95/127"
            # 				}
            # 				]
            # 			}
            # 		}
            # 	}
            # 	]
            # }

            ipv6 = False
            if 'inet6' in interface_details['unit']['family'].keys():
                ipv6 = True

            #                                                      #
            #          GENERATE PE 1GOV TBU CONFIGURATION          #
            #                                                      #
            config_file = 'PE_1GOV_TBU_{}_{}_{}@{}_by_{}'.format(
                serialized_data['service_id'], serialized_data['node_name'],  date_now, time_now, user)  # create config file

            # sample json input for api
            # {
            #     "start_date": "2023-01-09",
            #     "end_date": "2023-01-09",
            #     "interface": "xe-3/3/0.2194",
            #     "vpn": "GOV_JPNIP",
            #     "site_abr": "JPN",
            #     "pe_node": "iMSE21.STM",
            #     "service_id": "PS1006632659",
            #     "new_bandwidth": "100m",
            #     "old_bandwidth": "10m",
            #     "interface_policer": false,
            #     "global_policer": false,
            #     "ipv6": false,
            #     "filename": "1GOV_TBU_scenario1"
            # }

            if 'start_date' in serialized_data and 'end_date' in serialized_data:

                payload = {
                    'start_date': serialized_data['start_date'],
                    'end_date': serialized_data['end_date'],
                    'interface': serialized_data['interface'],
                    'vpn': serialized_data['vpn_name'].upper(),
                    'site_abr': site_abbr,
                    'pe_node': serialized_data['node_name'],
                    'service_id': serialized_data['service_id'],
                    'new_bandwidth': serialized_data['new_bandwidth'].lower(),
                    'old_bandwidth': serialized_data['old_bandwidth'].lower(),
                    'filename': config_file,
                    'interface_policer': interface_policer,
                    'ipv6': ipv6,
                    'global_policer': not global_policer,
                }

            else:

                payload = {
                    'interface': serialized_data['interface'],
                    'vpn': serialized_data['vpn_name'].upper(),
                    'site_abr': site_abbr,
                    'pe_node': serialized_data['node_name'],
                    'service_id': serialized_data['service_id'],
                    'new_bandwidth': serialized_data['new_bandwidth'].lower(),
                    'old_bandwidth': serialized_data['old_bandwidth'].lower(),
                    'filename': config_file,
                    'interface_policer': interface_policer,
                    'ipv6': ipv6,
                    'global_policer': not global_policer,
                }

            response = requests.post(
                url+'v1/1gov/generate_config_temp_bw_upgrade/', data=payload, verify=False)
            output = response.json()

            if output['status'] == 'failed':
                error_message = 'An error has occured while generating PE configurations'
                output_message = error_message + \
                    '\n[error message: {}]'.format(output['output'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': error_message,
                    'api_output': str(output['output']),
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': config_file,
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['data']
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
            else:
                output_message = output['output']
                log_text(filename=log_file, content=output_message)

            #                                                #
            #                   CHECK CPU                    #
            #                                                #
            match, count, ne_output, master_util_success, master_util_failed = False, int(
            ), str(), dict(), dict()
            while not match:
                # if failed check 5 times
                if count > 4:
                    break

                # create 5s delay to re-chek CPU
                if count > 0:
                    time.sleep(10)

                payload = {
                    'ne_ip': serialized_data['device_id'],
                }

                response = requests.get(
                    url+'v1/check_cpu/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while retrieving CPU utilization info from PE'
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_keys = output['output'].keys()
                    for key in output_keys:
                        if re.findall('main', key, re.IGNORECASE):
                            if int(output['output'][key]['memory_utilization']) < 70 and int(output['output'][key]['idle']) > 10:
                                match = True
                                master_util_success[key] = {
                                    'memory_utilization': output['output'][key]['memory_utilization'],
                                    'idle': output['output'][key]['idle']
                                }
                            else:
                                match = False
                                master_util_failed[key] = {
                                    'memory_utilization': output['output'][key]['memory_utilization'],
                                    'idle': output['output'][key]['idle']
                                }

                    # increase counter if does not match threshold
                    if not match:
                        count += 1
                    # accumulate ne output in a var
                    ne_output += output['ne_response']

            if not match:
                output_message = str()
                for key in master_util_failed:
                    if output_message == '':
                        output_message = "CPU utilization is outside threshold i.e. {} memory utilization ({}) and idle ({})".format(
                            key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
                    else:
                        output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(
                            master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
                log_text(filename=log_file,
                            content=output_message, ne_output=ne_output)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': str(output['output']),
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': config_file,
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
            else:
                output_message = str()
                for key in master_util_success:
                    if output_message == '':
                        output_message = "CPU utilization is within threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(
                            master_util_success[key]['memory_utilization']) + ' percent', str(master_util_success[key]['idle']) + ' percent')
                    else:
                        output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(
                            master_util_success[key]['memory_utilization'])+' percent', str(master_util_success[key]['idle'])+' percent')
                log_text(filename=log_file,
                            content=output_message, ne_output=ne_output)

            #                                                #
            #                 PUSH PE CONFIG                 #
            #                                                #
            payload = {
                'ne_ip': serialized_data['device_id'],
                'filename': config_file
            }

            response = requests.post(
                url+'v2/load_config_modify/', data=payload, verify=False)
            output = response.json()

            if output['status'] == 'failed':
                error_message = 'An error has occured while loading the generated configurations to PE'
                output_message = error_message + \
                    '\n[error message: {}]'.format(output['output'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': error_message,
                    'api_output': str(output['output']),
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': config_file,
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if output['output'] != 'Successfully loaded configurations at PE':
                    error_message = 'Configurations loading process is unsuccessful'
                    output_message = error_message + \
                        '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                    DB_data = {
                        'order_type': 'TBU',
                        'service_type': '1GOV',
                        'input_details': serialized_data,
                        'message': error_message,
                        'api_output': str(output['output']),
                        'status': 'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    log_text(
                        filename=log_file, content=output['output'], ne_output=output['ne_response'])

            #                                                #
            #              CREATE RFC in CGATE               #
            #                                                #

            description = 'Service ID: {}, PE loopback IP : {}, PE interface : {}, New bandwidth : {}, Old bandwidth : {}, VPN Name: {}'.format(
                serialized_data['service_id'], serialized_data['device_id'], serialized_data['interface'], serialized_data['new_bandwidth'], serialized_data['old_bandwidth'], serialized_data['vpn_name'].upper())
            file_config = open(
                '/root/home/<USER>/{}.txt'.format(config_file), 'r')
            workplan = file_config.read()
            datetime_provi = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            payload = {
                'rfc_status': 'close',
                'title': '1GOV TBU - {} ({})'.format(serialized_data['service_id'], serialized_data['vpn_name'].upper()),
                'description': description,
                'impact': 'Low',
                'work_plan': workplan,
                'fall_back': 'Rollback',
                'reason': 'Provisioning',
                'start': datetime_provi,
                'end': datetime_provi,
                'hostname': serialized_data['node_name'],
                'region': serialized_data['pe_region'],
                'actual_start': datetime_provi,
                'actual_end': datetime_provi,
                'task_completed': 'Successful',
                'inventory_update': 'No'
            }

            try:
                response = requests.get('https://cgate.tm.com.my/cms/api/pisa/createRFC.php', params=payload, verify=False, timeout=40)
                cgate_output = response.json()

                cgate_error_message = str()

                if cgate_output[0]['HEADER']['CREATE_STATUS'] == 'FAIL':
                    cgate_error_message = 'Failed to create RFC in Cgate'
                    output_message = cgate_error_message + \
                        '\n[error message: {}]'.format(
                            cgate_output[0]['HEADER']['ERROR_MSG'])
                    log_text(filename=log_file, content=output_message)
                else:
                    cgate_success_message = 'Successfully created RFC in Cgate'
                    log_text(filename=log_file,
                                content=cgate_success_message)

            except Exception as error:
                cgate_error_message = 'Failed to create RFC in Cgate'
                output_message = cgate_error_message + \
                    '\n[error message: {}]'.format(str(error))
                log_text(filename=log_file, content=output_message)

            #                                                #
            #              OUTPUT FINAL MESSAGE              #
            #                                                #

            if cgate_error_message == 'Failed to create RFC in Cgate':
                output_message = 'Successfully provisioned new bandwidth at PE but failed to create RFC in CGate'
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': cgate_error_message,
                    'status': 'failed',
                    'log_file': log_file,
                    'config_file': config_file,
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = cgate_error_message
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
            else:
                output_message = 'Successfully provisioned new bandwidth at PE'
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type': 'TBU',
                    'service_type': '1GOV',
                    'input_details': serialized_data,
                    'message': output_message,
                    'api_output': 'No error',
                    'status': 'success',
                    'log_file': log_file,
                    'config_file': config_file,
                    'user': user
                }
                api_response['status'] = 'success'
                api_response['message'] = output_message
                api_response['data'] = 'No error'
                log_database(data_DB=DB_data)
                return Response(api_response, status=status.HTTP_200_OK)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            log_text(filename=log_file, content=output_message)
            DB_data = {
                'order_type': 'TBU',
                'service_type': '1GOV',
                'input_details': request.data,
                'message': error_message,
                'api_output': str(error),
                'status': 'failed',
                'log_file': log_file,
                'config_file': 'Undefined',
                'user': user
            }
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            # api_response['data'] = str(error)
            api_response['data'] = output_error
            log_database(data_DB=DB_data)
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
