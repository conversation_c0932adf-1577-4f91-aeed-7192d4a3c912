from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
# Serializers
from api.serializers import verifyXdslamSerializer
# Swagger
from drf_yasg.utils import swagger_auto_schema
# File and Database
from api.libs import log_text, log_database
# General
import sys, requests, ipaddress, re
from datetime import datetime
from json import dumps
from dotenv import load_dotenv
load_dotenv()

class ConfigurationError(Exception):
    """Exception raised for errors in the device configuration."""
    pass

class API(APIView):
    """
    API for verifying XDSLAM service for termination.
    """
    
    # exclude from global authentication
    authentication_classes = ()
    permission_classes = ()
    
    @swagger_auto_schema(
        request_body=verifyXdslamSerializer,
        request_body_example={
            "xdslam_list": [
                {
                    "access_id": "MRU_A8004",
                    "epe_name": "EPEALBRA01-SH1",
                    "epe_device_id": "**********",
                    "epe_vendor": "alcatel-lucent",
                    "epe_interface": "Port 1/2/8.500"
                }
            ]
        },
        tags=['XDSLAM'],
        operation_summary="Verify XDSLAM service for termination",
        operation_description="Verify XDSLAM service for termination",
        responses={
            200: "XDSLAM verification successful",
            400: "Bad request",
            500: "Internal server error"
        }
    )
    def post(self, request):
        stage = 'Start Process'
        try:
            # Get User Id from Token
            token = request.META.get('HTTP_AUTHORIZATION', '').replace('Token ', '')
            user_id = Token.objects.get(key=token).user_id if token else None
            user = User.objects.get(id=user_id).username if user_id else request.data.get('staff_id', 'not_available')

            '''
            Verify API input format
            '''
            stage = 'Verify Data Format'
            input_data = verifyXdslamSerializer(data=request.data)
            parameters = dict(request.data)
            parameters.pop('password', None)
            if not input_data.is_valid():
                output_msg = f"Stage: {stage}. Invalid input data: {str(input_data.errors)}"
                log_database(
                    data_DB={
                        'order_type': 'Verification',
                        'service_type': 'XDSLAM',
                        'input_details':parameters,
                        'message': output_msg,
                        'api_output': str(input_data.errors),
                        'status': 'failed',
                        'log_file': 'N/A',
                        'user': user
                    }
                )
                return Response({
                    'status': 'failed',
                    'output': output_msg,
                    'data': input_data.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            serialized_data = input_data.validated_data

            # Create log file name
            stage = 'Create Log File'
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = f"XDSLAM_Verification_{date_now}@{time_now}_by_{user}.txt"

            # Log the start of the process
            output_message = f"File created at {date_now} {time_now} for XDSLAM Service Verification"
            log_text(filename=log_file, content=output_message)
            
            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(parameters, indent=4, separators=(',', ': ')))

            '''
            Process each XDSLAM entry
            '''
            verification_results = []
            for entry in serialized_data['xdslam_list']:
                stage = f"Verify XDSLAM {entry['access_id']}"
                
                result = {
                    'access_id': entry['access_id'],
                    'epe_name': entry['epe_name'],
                    'epe_vendor': entry['epe_vendor'],
                    'epe_device_id': entry['epe_device_id'],
                    'epe_interface': entry['epe_interface'],
                    'verifications': {}
                }

                # Example verification steps - replace with actual verification logic
                try:
                    
                    '''
                    Verify all required parameters are provided and are valid
                    '''
                    stage = f"Verify Parameters {entry['access_id']}"
                    data_error = False
                    data_verification = {'status': False, 'details': {}}
                    # Verify Access ID is provided
                    if not entry['access_id']:
                        data_error = True
                        data_verification['details']['access_id'] = 'Access ID is required'
                    else:
                        serialized_data['service_id'] = entry['access_id']

                    # Verify EPE Name is provided
                    if not entry['epe_name']:
                        data_error = True
                        data_verification['details']['epe_name'] = 'EPE Name is required'
                    else:
                        # must contain 'epe' case insensitive
                        if 'epe' not in entry['epe_name'].lower():
                            data_error = True
                            data_verification['details']['epe_name'] = 'EPE Name must contain "epe"'

                    # Verify EPE Device ID is provided
                    if not entry['epe_device_id']:
                        data_error = True
                        data_verification['details']['epe_device_id'] = 'EPE Device ID is required'
                    else:
                        # must be a valid IP address
                        try:
                            ipaddress.ip_address(entry['epe_device_id'])
                        except ValueError:
                            data_error = True
                            data_verification['details']['epe_device_id'] = 'EPE Device ID must be a valid IP address'

                    # Verify EPE Interface is provided
                    if not entry['epe_interface']:
                        data_error = True
                        data_verification['details']['epe_interface'] = 'EPE Interface is required'

                    # Verify EPE Vendor is provided
                    if not entry['epe_vendor']:
                        data_error = True
                        data_verification['details']['epe_vendor'] = 'EPE Vendor is required'
                    else:
                        # must be valid vendor
                        if entry['epe_vendor'].lower() not in ['alcatel-lucent', 'huawei']:
                            data_error = True
                            data_verification['details']['epe_vendor'] = 'EPE Vendor must be "alcatel-lucent" or "huawei"'

                    if data_error:
                        # Compile the result
                        output_message = f"Stage: {stage}. Data verification failed for {entry['access_id']}"
                        data_verification['status'] = False
                        log_text(filename=log_file, content=output_message, ne_output=dumps(data_verification, indent=4, separators=(',', ': ')))
                        result['verifications']['parameter'] = data_verification
                        verification_results.append(result)
                        continue
                    
                    else:
                        data_verification['status'] = True
                        result['verifications']['parameter'] = data_verification
                        output_message = f"Stage: {stage}. Data verification passed for {entry['access_id']}"
                        log_text(filename=log_file, content=output_message)

                        stage = f"Verify Configuration {entry['epe_name']} ({entry['access_id']})"

                        '''
                        Verify Configuration (Huawei)
                        '''

                        url = 'http://pisa-huawei-svc/huawei/npe_epe/v1/'

                        if entry['epe_vendor'].lower() == 'huawei':
                            
                            '''
                            Get Service ID and NPE Loopback IP from EPE Configuration
                            '''
                            stage = f"Get Service Id. {entry['epe_name']} ({entry['access_id']})"
                            config_error = False
                            config_verification = {'status': False, 'details': {}}

                            # curl -X GET "https://pisa.tm.com.my/huawei/npe_epe/v1/show_config/?ne_ip=**********&command=dis%20cu%20int%20GigabitEthernet%204%2F0%2F1.508" -H "accept: application/json"
                            # using request to get the configuration from Huawei EPE
                            try:
                                response = requests.get(
                                    url=f"{url}show_config/",
                                    params={
                                        'ne_ip': entry['epe_device_id'],
                                        'command': f'dis cu int {entry["epe_interface"].lower()}'
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    # if status is success, proceed to process the configuration
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['epe_name']} ({entry['access_id']})")

                                        else:
                                            service_id_config = output['ne_response']
                                            service_id_exist = False
                                            for line in service_id_config.split('\n'):
                                                if f'mpls l2vc' in line:
                                                    service_id_exist = True
                                                    service_id = line.split()[3].strip()
                                                    npe_loopback_ip = line.split()[2].strip()
                                                    break
                                                
                                            if not service_id_exist:
                                                output_message = f"Stage: {stage}. Service ID or NPE Loopback IP not found in configuration for {entry['epe_name']} ({entry['access_id']})"
                                                log_text(filename=log_file, content=output_message, ne_output=str(service_id_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['service_id'] = 'not available'
                                                config_verification['details']['npe_loopback_ip'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue
                                            
                                            else:
                                                output_message = f"Stage: {stage}. Service ID: {service_id} and NPE Loopback IP: {npe_loopback_ip} found in configuration for {entry['epe_name']} ({entry['access_id']})"
                                                log_text(filename=log_file, content=output_message, ne_output=service_id_config)
                                                config_verification['details']['service_id'] = service_id
                                                config_verification['details']['npe_loopback_ip'] = npe_loopback_ip
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['epe_name']} ({entry['access_id']})")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['epe_name']} ({entry['access_id']})")
                            
                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['service_id'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue

                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['service_id'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            '''
                            Get NPE interface from NPE Configuration
                            '''

                            stage = f"Get NPE Interface ({entry['access_id']})"
                            try:
                                response = requests.get(
                                    url=f"{url}show_config/",
                                    params={
                                        'ne_ip': npe_loopback_ip,
                                        'command': f'dis mpls l2vc {service_id}'
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['access_id']}")

                                        else:
                                            npe_interface_config = output['ne_response']
                                            npe_interface_exist = False
                                            for line in npe_interface_config.split('\n'):
                                                if 'client interface' in line.lower():
                                                    npe_interface_exist = True
                                                    npe_interface = line.split()[3].strip()
                                                    break
                                                
                                            if not npe_interface_exist:
                                                output_message = f"Stage: {stage}. NPE Interface not found in configuration for {entry['epe_name']} ({entry['access_id']})"
                                                log_text(filename=log_file, content=output_message, ne_output=str(npe_interface_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['npe_interface'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue
                                            
                                            else:
                                                output_message = f"Stage: {stage}. NPE Interface: {npe_interface} found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=npe_interface_config)
                                                config_verification['details']['npe_interface'] = npe_interface
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['access_id']}")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['access_id']}")

                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['npe_interface'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['npe_interface'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            '''
                            Get IBSE Hostname and Port from NPE Configuration
                            '''
                            stage = f"Get IBSE Hostname and Port ({entry['access_id']})"
                            # Get NPE port number from NPE interface
                            npe_port_number = re.sub(r'^[^\d]+', '', npe_interface.split('.')[0])
                            try:
                                response = requests.get(
                                    url=f"{url}show_config/",
                                    params={
                                        'ne_ip': npe_loopback_ip,
                                        'command': f'dis lldp nei int gi {npe_port_number}'
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['access_id']}")

                                        else:
                                            ibse_config = output['ne_response']
                                            ibse_port_exist = False
                                            for line in ibse_config.split('\n'):
                                                if 'PortId:' in line:
                                                    ibse_port_exist = True
                                                    ibse_port = line.split(':')[1].strip()
                                                    continue
                                                if 'SysName:' in line:
                                                    ibse_hostname = line.split(':')[1].strip()
                                                    continue

                                            if not ibse_port_exist:
                                                output_message = f"Stage: {stage}. IBSE Port and Hostname not found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(ibse_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['ibse_hostname'] = 'not available'
                                                config_verification['details']['ibse_port'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue

                                            else:
                                                output_message = f"Stage: {stage}. IBSE Port: {ibse_port} and Hostname: {ibse_hostname} found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=ibse_config)
                                                config_verification['details']['ibse_hostname'] = ibse_hostname
                                                config_verification['details']['ibse_port'] = ibse_port
                                                result['verifications']['config'] = config_verification

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving NPE configuration for {entry['access_id']}")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving NPE configuration for {entry['access_id']}")

                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_hostname'] = output_message
                                config_verification['details']['ibse_port'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_hostname'] = output_message
                                config_verification['details']['ibse_port'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue


                            '''
                            Verify iBSE interface description match access id
                            '''

                            url = "http://pisa-huawei-svc/huawei/agg/v1/"

                            stage = f"Verify iBSE {ibse_hostname.upper()} description ({entry['access_id']})"
                            try:
                                response = requests.post(
                                    url=f"{url}show_config/",
                                    json={
                                        'ne_ip': f"{ibse_hostname}.tmone.my",
                                        'commands': [
                                            f"dis cu int {ibse_port}.500 | inc {npe_interface.split('.')[1]}",
                                            f"dis access-user int {ibse_port}.500 pe {npe_interface.split('.')[1]}"
                                        ],
                                        "username": serialized_data['staff_id'],
                                        "password": serialized_data['password']
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['data'][0]['ne_response'] and 'error' in output['data'][1]['ne_response']:
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration {ibse_hostname.upper()} for {entry['access_id']}")

                                        else:
                                            ibse_config = output['data'][0]['ne_response']
                                            ibse_description_match = False
                                            for line in ibse_config.split('\n'):
                                                if re.search(entry['access_id'], line, re.IGNORECASE):
                                                    ibse_description_match = True
                                                    ibse_interface_description = line
                                                    break
                                                
                                            if not ibse_description_match:
                                                output_message = f"Stage: {stage}. Description for interface {ibse_port}.500 does not match {entry['access_id']} for {ibse_hostname.upper()}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(ibse_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['ibse_interface_description'] = { 'matched_access_id': False, 'description': ibse_interface_description.strip()}
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue
                                            
                                            else:
                                                output_message = f"Stage: {stage}. Description for interface {ibse_port}.500 match {entry['access_id']} for {ibse_hostname.upper()}"
                                                log_text(filename=log_file, content=output_message, ne_output=ibse_config)
                                                config_verification['details']['ibse_interface_description'] = { 'matched_access_id': True, 'description': ibse_interface_description.strip()}
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                                stage = f"Verify iBSE {ibse_hostname.upper()} access user ({entry['access_id']})"
                                                ibse_access_user_config = output['data'][1]['ne_response']
                                                no_online_user = False
                                                for line in ibse_access_user_config.split('\n'):
                                                    if 'no online user' in line.lower():
                                                        no_online_user = True
                                                        ibse_access_user = line
                                                        break
                                                
                                                if not no_online_user:
                                                    output_message = f"Stage: {stage}. Online user found for {entry['access_id']} in {ibse_hostname.upper()}"
                                                    log_text(filename=log_file, content=output_message, ne_output=str(ibse_config))
                                                    config_error = True
                                                    config_verification['status'] = False
                                                    config_verification['details']['ibse_access_user'] = { 'no_online_user': False, 'access_user': ibse_access_user.strip() }
                                                    result['verifications']['config'] = config_verification
                                                    verification_results.append(result)
                                                    continue
                                                
                                                else:
                                                    output_message = f"Stage: {stage}. No online user found for {entry['access_id']} in {ibse_hostname.upper()}"
                                                    log_text(filename=log_file, content=output_message, ne_output=ibse_access_user_config)
                                                    config_verification['status'] = True
                                                    config_verification['details']['ibse_access_user'] = { 'no_online_user': True, 'access_user': ibse_access_user.strip() }
                                                    result['verifications']['config'] = config_verification
                                                    verification_results.append(result)
                                                    continue
                                            
                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {ibse_hostname.upper()} ({entry['access_id']})")
                                    
                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {ibse_hostname.upper()} ({entry['access_id']})")
                                
                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_interface_description'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_interface_description'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue

                        '''
                        Verify  configuration (Alcatel-Lucent)
                        '''                     

                        url = "http://pisa-nokia-svc/nokia/npe_epe/v1/"

                        if entry['epe_vendor'].lower() == 'alcatel-lucent':
                            '''
                            Get Service ID from EPE Configuration
                            '''
                            stage = f"Get Service Id. {entry['epe_name']} ({entry['access_id']})"
                            config_error = False
                            config_verification = {'status': False, 'details': {}}

                            try:
                                
                                formatted_epe_interface = re.sub(r'port ', '', entry['epe_interface'], flags=re.IGNORECASE).replace('.', ':').strip()

                                response = requests.get(
                                    url=f"{url}show_config",
                                    params={
                                        'ne_ip': entry['epe_device_id'],
                                        'command': f"show service sap-using sap {formatted_epe_interface}"
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['epe_name']} ({entry['access_id']})")

                                        else:
                                            epe_config = output['ne_response']
                                            service_id_exist = False
                                            for line in epe_config.split('\n'):
                                                if formatted_epe_interface in line and 'show service sap-using sap' not in line:
                                                    service_id_exist = True
                                                    service_id = line.split()[1].strip()
                                                    continue

                                            if not service_id_exist:
                                                output_message = f"Stage: {stage}. Service ID not found in configuration for {entry['epe_name']} ({entry['access_id']})"
                                                log_text(filename=log_file, content=output_message, ne_output=str(epe_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['service_id'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue
                                            
                                            else:
                                                output_message = f"Stage: {stage}. Service ID: {service_id} found in configuration for {entry['epe_name']} ({entry['access_id']})"
                                                log_text(filename=log_file, content=output_message, ne_output=epe_config)
                                                config_verification['details']['service_id'] = service_id
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['epe_name']} ( {entry['access_id']})")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['epe_name']} ({entry['access_id']})")

                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['service_id'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['service_id'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            

                            '''
                            Get NPE loopback IP from EPE Configuration
                            '''
                            stage = f"Get NPE Loopback IP. {entry['access_id']}"

                            try:
                                
                                response = requests.get(
                                    url=f"{url}show_config",
                                    params={
                                        'ne_ip': entry['epe_device_id'],
                                        'command': f"show service id {service_id} base"
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['access_id']}")

                                        else:
                                            epe_config = output['ne_response']
                                            npe_loopback_ip_exist = False
                                            for line in epe_config.split('\n'):
                                                if service_id in line and 'sdp' in line:
                                                    npe_loopback_ip_exist = True
                                                    # Extract the IP address between parentheses
                                                    npe_loopback_ip = re.search(r'\(([^)]+)\)', line).group(1)
                                                    break
                                                
                                            if npe_loopback_ip_exist:
                                                output_message = f"Stage: {stage}. NPE Loopback IP: {npe_loopback_ip} found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=epe_config)
                                                config_verification['details']['npe_loopback_ip'] = npe_loopback_ip
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                            else:
                                                output_message = f"Stage: {stage}. NPE Loopback IP not found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(epe_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['npe_loopback_ip'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['access_id']}")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['access_id']}")

                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['npe_loopback_ip'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            

                            '''
                            Get NPE Interface from NPE Configuration
                            '''

                            stage = f"Get NPE Interface {entry['access_id']}"

                            try:
                                
                                response = requests.get(
                                    url=f"{url}show_config",
                                    params={
                                        'ne_ip': npe_loopback_ip,
                                        'command': f"show service id {service_id} base"
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['access_id']}")

                                        else:
                                            npe_config = output['ne_response']
                                            npe_interface_exist = False
                                            for line in npe_config.split('\n'):
                                                if 'sap' in line and 'q-tag' in line:
                                                    npe_interface_exist = True
                                                    npe_interface = line.split()[0].strip().replace('sap:', '')
                                                    break
                                                
                                            if npe_interface_exist:
                                                output_message = f"Stage: {stage}. NPE Interface: {npe_interface} found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=npe_config)
                                                config_verification['details']['npe_interface'] = npe_interface
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                            else:
                                                output_message = f"Stage: {stage}. NPE Interface not found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(npe_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['npe_interface'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['access_id']}")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {entry['access_id']}")

                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['npe_interface'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['npe_interface'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            

                            '''
                            Get IBSE Hostname and Port from NPE Configuration
                            '''

                            stage = f"Get IBSE Hostname and Port ({entry['access_id']})"
                            npe_port_number = npe_interface.split(':')[0]

                            try:
                                
                                response = requests.get(
                                    url=f"{url}show_config",
                                    params={
                                        'ne_ip': npe_loopback_ip,
                                        'command': f"show port {npe_port_number} ethernet lldp remote-info"
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['ne_response'].lower():
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration for {entry['access_id']}")

                                        else:
                                            npe_config = output['ne_response']
                                            ibse_hostname_exist = False
                                            for line in npe_config.split('\n'):
                                                if 'ethernet' in line.lower() and not 'ethernet lldp remote-info' in line:
                                                    # Extract GigabitEthernet13/0/1 from \"GigabitEthernet13/0/1\" 
                                                    ibse_interface = line.replace('\"', '').strip()
                                                    continue
                                                if 'System Name' in line:
                                                    ibse_hostname_exist = True
                                                    ibse_hostname = line.split(':')[-1].strip()
                                                    break
                                                
                                            if ibse_hostname_exist:
                                                output_message = f"Stage: {stage}. IBSE Hostname: {ibse_hostname} and Interface: {ibse_interface} found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=npe_config)
                                                config_verification['details']['ibse_hostname'] = ibse_hostname
                                                config_verification['details']['ibse_interface'] = ibse_interface
                                                result['verifications']['config'] = config_verification

                                            else:
                                                output_message = f"Stage: {stage}. IBSE Hostname or Interface not found in configuration for {entry['access_id']}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(npe_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['ibse_hostname'] = 'not available'
                                                config_verification['details']['ibse_interface'] = 'not available'
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue

                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving NPE configuration for {entry['access_id']}")

                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving NPE configuration for {entry['access_id']}")

                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_hostname'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                output_message = f"Stage: {stage}. {error}"
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_hostname'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            
                            '''
                            Verify iBSE interface description match access id
                            '''

                            url = "http://pisa-huawei-svc/huawei/agg/v1/"

                            stage = f"Verify iBSE {ibse_hostname.upper()} description ({entry['access_id']})"
                            try:
                                response = requests.post(
                                    url=f"{url}show_config/",
                                    json={
                                        'ne_ip': f"{ibse_hostname}.tmone.my",
                                        'commands': [
                                            f"dis cu int {ibse_interface}.500 | inc {npe_interface.split(':')[1]}",
                                            f"dis access-user int {ibse_interface}.500 pe {npe_interface.split(':')[1]}"
                                        ],
                                        "username": serialized_data['staff_id'],
                                        "password": serialized_data['password']
                                    },
                                    headers={'accept': 'application/json'}
                                )
                                response.raise_for_status()

                                # If status = 200, then the request is successful
                                if response.status_code == 200:
                                    output = response.json()
                                    if output['status'] == 'success':
                                        # check if error in ne_response
                                        if 'error' in output['data'][0]['ne_response'] and 'error' in output['data'][1]['ne_response']:
                                            raise ConfigurationError(f"Stage: {stage}. Error in configuration {ibse_hostname.upper()} for {entry['access_id']}")

                                        else:
                                            ibse_config = output['data'][0]['ne_response']
                                            ibse_description_match = False
                                            for line in ibse_config.split('\n'):
                                                if re.search(entry['access_id'], line, re.IGNORECASE):
                                                    ibse_description_match = True
                                                    ibse_interface_description = line
                                                    break
                                                
                                            if not ibse_description_match:
                                                output_message = f"Stage: {stage}. Description for interface {ibse_interface}.500 does not match {entry['access_id']} for {ibse_hostname.upper()}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(ibse_config))
                                                config_error = True
                                                config_verification['status'] = False
                                                config_verification['details']['ibse_interface_description'] = { 'matched_access_id': False, 'description': ibse_interface_description.strip()}
                                                result['verifications']['config'] = config_verification
                                                verification_results.append(result)
                                                continue
                                            
                                            else:
                                                output_message = f"Stage: {stage}. Description for interface {ibse_interface}.500 match {entry['access_id']} for {ibse_hostname.upper()}"
                                                log_text(filename=log_file, content=output_message, ne_output=str(ibse_config))
                                                config_verification['details']['ibse_interface_description'] = { 'matched_access_id': True, 'description': ibse_interface_description.strip()}
                                                result['verifications']['config'] = config_verification
                                                # verification_results.append(result)

                                                stage = f"Verify iBSE {ibse_hostname.upper()} access user ({entry['access_id']})"
                                                ibse_access_user_config = output['data'][1]['ne_response']
                                                no_online_user = False
                                                for line in ibse_access_user_config.split('\n'):
                                                    if 'no online user' in line.lower():
                                                        no_online_user = True
                                                        ibse_access_user = line
                                                        break
                                                    
                                                if not no_online_user:
                                                    output_message = f"Stage: {stage}. Online user found for {entry['access_id']} in {ibse_hostname.upper()}"
                                                    log_text(filename=log_file, content=output_message, ne_output=str(ibse_config))
                                                    config_error = True
                                                    config_verification['status'] = False
                                                    config_verification['details']['ibse_access_user'] = { 'no_online_user': False, 'access_user': ibse_access_user.strip() }
                                                    result['verifications']['config'] = config_verification
                                                    verification_results.append(result)
                                                    continue
                                                
                                                else:
                                                    output_message = f"Stage: {stage}. No online user found for {entry['access_id']} in {ibse_hostname.upper()}"
                                                    log_text(filename=log_file, content=output_message, ne_output=ibse_access_user_config)
                                                    config_verification['status'] = True
                                                    config_verification['details']['ibse_access_user'] = { 'no_online_user': True, 'access_user': ibse_access_user.strip() }
                                                    result['verifications']['config'] = config_verification
                                                    verification_results.append(result)
                                                    continue
                                            
                                    else:
                                        raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {ibse_hostname.upper()} ({entry['access_id']})")
                                    
                                else:
                                    raise ConfigurationError(f"Stage: {stage}. Error in retrieving configuration for {ibse_hostname.upper()} ({entry['access_id']})")
                                
                            except ConfigurationError as error:
                                output_message = str(error)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(response.json(), indent=4, separators=(',', ': ')))
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_interface_description'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue
                            
                            except Exception as error:
                                # exc_type, exc_obj, exc_tb = sys.exc_info()
                                # error_details = {
                                #     'error_type': str(exc_type),
                                #     'error_details': str(exc_obj),
                                #     'error_line': str(exc_tb.tb_lineno),
                                # }
                                output_message = f"Stage: {stage}. {error}"
                                # log_text(filename=log_file, content=output_message, ne_output=dumps(error_details, indent=4, separators=(',', ': ')))
                                log_text(filename=log_file, content=output_message)
                                config_error = True
                                config_verification['status'] = False
                                config_verification['details']['ibse_interface_description'] = output_message
                                result['verifications']['config'] = config_verification
                                verification_results.append(result)
                                continue

                            
                        else:
                            output_message = f"Stage: {stage}. Vendor {entry['epe_vendor']} is not supported"
                            log_text(filename=log_file, content=output_message)
                            config_error = True
                            config_verification['status'] = False
                            config_verification['details']['service_id'] = output_message
                            result['verifications']['config'] = data_verification
                            continue

                except Exception as e:
                    error_msg = f"Stage: {stage}. Error processing XDSLAM entry: {str(e)}"
                    # Get the error details
                    exc_type, exc_obj, exc_tb = sys.exc_info()
                    error_details = {
                        'error_type': str(exc_type),
                        'error_details': str(exc_obj),
                        'error_line': str(exc_tb.tb_lineno),
                    }
                    # result['error'] = error_msg
                    # verification_results.append(result)
                    log_text(filename=log_file, content=error_msg, ne_output=dumps(error_details, indent=4, separators=(',', ': ')))

                    # Log to database
                    log_database(
                        data_DB={
                            'order_type': 'Verification',
                            'service_type': 'XDSLAM',
                            'input_details': parameters,
                            'message': output_message,
                            'api_output': str(verification_results),
                            'status': 'failed',
                            'log_file': log_file,
                            'user': user
                        }
                    )

                    # Return success response
                    return Response({
                        'status': 'failed',
                        'message': error_msg,
                        'data': verification_results,
                        'error_details': error_details,
                        'log_file': log_file
                    }, status=status.HTTP_200_OK)

            # Log final results
            output_message = "XDSLAM verification completed"
            log_text(filename=log_file, content=output_message, ne_output=dumps(verification_results, indent=4, separators=(',', ': ')))

            # Log to database
            log_database(
                data_DB={
                    'order_type': 'Verification',
                    'service_type': 'XDSLAM',
                    'input_details': parameters,
                    'message': output_message,
                    'api_output': str(verification_results),
                    'status': 'success',
                    'log_file': log_file,
                    'user': user
                }
            )

            # Return success response
            return Response({
                'status': 'success',
                'message': output_message,
                'data': verification_results,
                'log_file': log_file
            }, status=status.HTTP_200_OK)

        except Exception as error:
            # Get the error details
            exc_type, exc_obj, exc_tb = sys.exc_info()
            error_details = {
                'error_type': str(exc_type),
                'error_details': str(exc_obj),
                'error_line': str(exc_tb.tb_lineno),
            }

            # Generate error message
            error_msg = f"Stage: {stage}. Unexpected error occurred while processing the request"
            output_msg = f"{error_msg}\n [Error details: {error}]"

            # Log the error
            DB_data = {
                'order_type': 'Verification',
                'service_type': 'XDSLAM',
                'message': output_msg,
                'api_output': str(error),
                'status': 'failed',
                'user': user if 'user' in locals() else 'not_available'
            }

            if stage == 'Start Process':
                DB_data['input_details'] = 'N/A'
                DB_data['log_file'] = 'N/A'
            elif stage == 'Verify Data Format':
                DB_data['input_details'] = parameters
                DB_data['log_file'] = 'N/A'
            else:
                log_text(
                    filename=log_file,
                    content=output_msg,
                    ne_output=dumps(error_details, indent=4, separators=(',', ': '))
                )
                DB_data['input_details'] = parameters
                DB_data['log_file'] = log_file

            # Log to database
            log_database(data_DB=DB_data)

            return Response({
                'status': 'failed',
                'message': output_msg,
                'data': error_details,
                'log_file': log_file if 'log_file' in locals() else None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
