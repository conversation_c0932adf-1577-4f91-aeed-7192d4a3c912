from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import selfServeInsighVerificationSerializer
import requests, json, time, re, os, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps


class API(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        request_body=selfServeInsighVerificationSerializer,
        responses={201: selfServeInsighVerificationSerializer()},
        operation_summary="Workflow for Insight Verification Self Serve",
    )

    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = selfServeInsighVerificationSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            
             # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            # log_file = f"{serialized_data['module']}_{date_now}@{time_now}_by_{user}.txt"     # Create Log File

             # LOG CREATED TIME #
            output_message = "File created at {} {}".format(date_now, time_now)
            # log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            # log_text(filename=log_file, content=output_message, ne_output=dumps(
                # request.data, indent=4, separators=(',', ': ')))

            # if not 'huawei' in serialized_data['vendor'] or not 'zte' in serialized_data['vendor']:
            if serialized_data['vendor'] not in ['huawei', 'zte']:
                output_message = 'Invalid vendor type ({}). Only module vendor type (Huawei & ZTE) is allowed'.format(serialized_data['vendor'])
                # log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'Insight Verification',
                    'service_type':'SSPD',
                    'input_details':serialized_data,
                    'message':output_message,
                    'api_output':"Vendor type received by workflow is '{}'".format(serialized_data['vendor']),
                    'status':'failed',
                    # 'log_file':log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = "Vendor type name received by workflow is '{}'".format(serialized_data['vendor'])
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            # # # # # # # # # # # # # # # # HUAWEI # # # # # # # # # # # # # # # # 

            if 'huawei' in serialized_data['vendor']:

                url_huawei = 'http://pisa-huawei-svc'
                # url_huawei = 'https://pisa.tm.com.my'

                # Define commands list separately for better readability
                command_1 = 'display ip interface brief lo0'
                command_2 = f"display interface description | include {serialized_data['nid_upe_name']}"
                # Handle interface name splitting logic
                interface_name = serialized_data['interface_name']
                if '.' in interface_name:
                    # If interface contains dot, split and take the base interface
                    base_interface = interface_name.split('.')[0]
                    command_3 = f"display current-configuration interface {base_interface} | include eth-trunk"
                else:
                    # If no dot, use the interface name as is
                    command_3 = f"display current-configuration interface {interface_name} | include eth-trunk"
                
                commands_list = [
                    command_1,
                    command_2,
                    command_3
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_huawei + '/huawei/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=30  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output = response.json()

                # Log successful API call
                # log_text(filename=log_file, content=f"API call successful: {response.status_code}")

                # Testing
                api_response['status'] = 'success'
                api_response['message'] = 'Data retrieved successfully'
                api_response['data'] = output
                return Response(api_response, status=status.HTTP_200_OK)

            # # # # # # # # # # # # # # # # ZTE # # # # # # # # # # # # # # # #  # 

            elif 'zte' in serialized_data['vendor']:
            
                # Declare variable at the beginning of the ZTE section
                smartgroup_status = None
                output_smartgroup = None
                loppback_ip_address = None  # Initialize here at the top level
                loopback_status = "Configuration not found"  # Initialize status here too
                interface_name_router_status = None  # Initialize the flag to track interface existence
                smartgroup_info = None  # Initialize the variable to track smartgroup information
                smartgroup_info_cos = None
                interface_name_info = None
                pe_wan_ip_router = None  # Add new variable for PE WAN IP
                pe_wan_ip_router_status = "Configuration not found"  # Initialize status for PE WAN IP


                url_zte = 'http://pisa-zte-svc'
            
                # Define commands list separately for better readability
                command_1 = 'show ip interface brief | include loopback0'
            
                commands_list = [
                    command_1
                ]
            
                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }
            
                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_1 = response.json()
            
                for item in output_cmd_1["data"]:
                
                    # show ip interface brief | include loopback0
                    if item["command"] == command_1:  # Match the specific command
                        ne_response = item["ne_response"]
                        # Don't redeclare loopback_status here since it's already declared above
                
                        # Count occurrences of "loopback0"
                        loopback_count = ne_response.count("loopback0")
                
                        if loopback_count >= 1:  # Configuration exists if "loopback0" is found at least once
                            # Use regex to extract the IP address
                            match = re.search(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b', ne_response)
                            if match:
                                loppback_ip_address = match.group(0)  # Extract the matched IP address
                                loopback_status = "Configuration found"
                        else:
                            loopback_status = "Configuration not found"


                # Interface Description Command
                command_2 = f"show interface description | include {serialized_data['nid_upe_name']}"

                commands_list = [
                    command_2
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_2 = response.json()

                for item in output_cmd_2["data"]:

                    # show interface description | include {serialized_data['nid_upe_name']}
                    if item["command"] == command_2:  # Match the specific command
                        ne_response = item["ne_response"]
                    
                        config_nid_upe_found = 'Configuration not found'  # Initialize the flag to track configuration existence
                    
                        # Split the response into lines and process each line, skipping the first line (the command)
                        lines = ne_response.splitlines()[1:]
                        for line in lines:
                            if "AdminStatus" in line:  # Skip the header line
                                continue
                            if serialized_data['nid_upe_name'] in line:  # Check if the nid_upe_name exists in the line
                                config_nid_upe_found = 'Configuration found'
                                parts = line.split()
                                if len(parts) >= 5:
                                    # The description is everything after the 4th column
                                    interface_name_info = " ".join(parts[4:]).strip()
                                    # Split on the nid_upe_name and take the first part
                                    # splits = interface_name_info.split(serialized_data['nid_upe_name'])
                                    # interface_name_info = splits[0].strip()  # Take the first part before the nid_upe_name
                                break

                command_6 = f"show ip interface brief | include {serialized_data['pe_wan_ip']}"

                commands_list = [
                    command_6
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_6 = response.json()

                for item in output_cmd_6["data"]:
                
                    # command_6 = f"show ip interface brief | include {serialized_data['pe_wan_ip']}"
                    if item["command"] == command_6:  # Match the specific command
                        ne_response = item["ne_response"]
                        # Initialize variables
                        pe_wan_ip_router_status = "Configuration not found"
                    
                        # Skip the first line (command line) and process the remaining lines
                        lines = ne_response.splitlines()[1:]  # Skip first line
                        response_content = '\n'.join(lines)  # Rejoin without the first line
                        
                        # Check if pe_wan_ip exists in the response
                        if serialized_data['pe_wan_ip'] in response_content:
                            # Use regex to extract the interface name before the IP address
                            # This pattern captures the interface name followed by the IP address
                            pattern = rf'(\S+)\s+{re.escape(serialized_data["pe_wan_ip"])}'
                            match = re.search(pattern, response_content)
                            if match:
                                smartgroup_info_cos = match.group(1)  # Extract the interface name (e.g., "sat_gei-3/1/0/19.1000")
                                pe_wan_ip_router_status = "Configuration found"
                                pe_wan_ip_router = serialized_data['pe_wan_ip']  # Assign the PE WAN IP value


                # Handle interface name splitting logic
                interface_name = serialized_data['interface_name']
                if '.' in interface_name:
                    # If interface contains a dot, split and take the base interface
                    base_interface = interface_name.split('.')[0]
                else:
                    base_interface = interface_name

                # Extract the desired part of the interface name
                if '-' in base_interface:
                    extracted_interface = base_interface.split('-')[-1]  # Take the part after the last '-'
                else:
                    extracted_interface = base_interface.split('_')[-1]  # Take the part after the last '_'

                # Use the extracted interface in the command
                command_3 = f"show interface brief | include {extracted_interface}"

                commands_list = [
                    command_3
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_3 = response.json()

                for item in output_cmd_3["data"]:

                    # show interface brief | include {extracted_interface}
                    if item["command"] == command_3:  # Match the specific command
                        ne_response = item["ne_response"]
                        # Use regex to extract the interface name before "optical"
                        match = re.search(r'(\S+)\s+optical', ne_response)
                        if match:
                            interface_name_router = match.group(1)  # Extract the matched interface name
                            # Check if the extracted interface name exists
                            if interface_name_router:
                                interface_name_router_status = "Interface exists"
                            else:
                                interface_name_router_status = "Interface does not exist"
                        else:
                            interface_name_router_status = "Interface does not exist"


                if interface_name_router_status == "Interface exists":

                    command_4 = f"show running-config-interface {interface_name_router} | include smartgroup"

                    commands_list = [
                        command_4
                    ]

                    payload = {
                        'ne_ip': serialized_data['ne_ip'],
                        'commands': commands_list
                    }

                    # Make API request with proper URL joining and error handling
                    # api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                    response = requests.post(
                        url=api_endpoint,
                        json=payload,  # Use json instead of data for proper serialization
                        verify=False,
                        timeout=240  # Add timeout to prevent hanging
                    )
                    response.raise_for_status()  # Raise exception for bad HTTP status codes
                    output_cmd_4 = response.json()

                    for item in output_cmd_4["data"]:
                        if item["command"] == command_4:  # Match the specific command
                            ne_response = item["ne_response"]
                            # Split the response into lines and ignore the first line (command line)
                            response_lines = ne_response.splitlines()[1:]
                            # Join the remaining lines into a single string
                            cleaned_response = " ".join(response_lines).replace("\r", "").replace("\n", " ")
                            
                            # Check if "mode on" exists in the response
                            # if "mode on" in cleaned_response:
                            if re.search(r'mode\s+(on|active)', cleaned_response):
                                # Use regex to extract the "smartgroup" information
                                match = re.search(r'\bsmartgroup\s+(\d+)\b', cleaned_response)
                                if match:
                                    smartgroup_info = f"smartgroup{match.group(1)}"  # Combine "smartgroup" with the extracted number
                                    smartgroup_status = "Smartgroup found"
                                else:
                                    smartgroup_status = "Smartgroup not found"
                            else:
                                smartgroup_status = "Smartgroup not found"

                    # Use the trimmed smartgroup_info in command_5
                    if smartgroup_status == "Smartgroup found":
                        # Extract VLAN ID from smartgroup_info_cos (e.g., "sat_gei-3/1/0/19.1000" -> "1000")
                        if '.' in smartgroup_info_cos:
                            # vlan_id = smartgroup_info_cos.split('.')[1]  # Get the part after the dot
                            vlan_id = serialized_data['interface_name'].split('.')[1]
                            command_5 = f"show running-config-interface {smartgroup_info}.{vlan_id} | include description"
                        else:
                            # Fallback if no dot found
                            command_5 = f"show running-config-interface {smartgroup_info} | include description"
                    else:
                        command_5 = f"show running-config-interface {interface_name_router} | include description"

                    commands_list = [
                        command_5
                    ]

                    payload = {
                        'ne_ip': serialized_data['ne_ip'],
                        'commands': commands_list
                    }

                    # Make API request with proper URL joining and error handling
                    # api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                    response = requests.post(
                        url=api_endpoint,
                        json=payload,  # Use json instead of data for proper serialization
                        verify=False,
                        timeout=240  # Add timeout to prevent hanging
                    )
                    response.raise_for_status()  # Raise exception for bad HTTP status codes
                    output_cmd_5 = response.json()

                    # Extract the relevant data
                    for item in output_cmd_5["data"]:
                        if item["command"] == command_5:  # Match the specific command
                            ne_response = item["ne_response"]
                            # Split the response into lines and ignore the first line (command line)
                            response_lines = ne_response.splitlines()[1:]
                            # Join the remaining lines into a single string
                            cleaned_response = " ".join(response_lines).replace("\r", "").replace("\n", " ")
                    
                            # Check if "description" exists in the response
                            if "description" in cleaned_response:
                                # Use regex to extract the description, stopping before the device prompt
                                match = re.search(r'description\s+(.+?)\s+\w+\.\w+#', cleaned_response)
                                if match:
                                    description_info = match.group(1).strip()  # Extract and clean the matched description
                                    description_status = "Configuration found"
                                else:
                                    # Fallback: try without the device prompt pattern
                                    match = re.search(r'description\s+(.+)', cleaned_response)
                                    if match:
                                        # Remove the device prompt manually if it exists
                                        description_text = match.group(1).strip()
                                        # Remove device prompt pattern (e.g., "aggzt41.kli#")
                                        description_info = re.sub(r'\s+\w+\.\w+#$', '', description_text).strip()
                                        description_status = "Configuration found"
                                    else:
                                        description_info = None
                                        description_status = "Configuration not found"
                            else:
                                description_info = None  # Set to None if "description" is not found
                                description_status = "Configuration not found"


                if serialized_data['cos'] == 'classic':
                    # command_7 = f'show running-config-interface {interface_name_router}.{serialized_data["interface_name"].split(".")[1]} | include rate-limit'
                    command_7 = f'show running-config-interface {smartgroup_info_cos.split(".")[0]}.{serialized_data["interface_name"].split(".")[1]} | include rate-limit'
                else:
                    # command_7 = f'show running-config-interface {interface_name_router}.{serialized_data["interface_name"].split(".")[1]} | include service-policy'
                    command_7 = f'show running-config-interface {smartgroup_info_cos.split(".")[0]}.{serialized_data["interface_name"].split(".")[1]} | include service-policy'

                commands_list = [
                    command_7
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                # api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_7 = response.json()

                # Extract the relevant data
                cos = None
                bandwidth = None

                for item in output_cmd_7["data"]:
                    if item["command"] == command_7:  # Match the specific command
                        ne_response = item["ne_response"]

                        if "service-policy" in ne_response:
                            # match = re.search(r'service-policy\s+\S+\s+(input|output)\s+(\S+)', ne_response)
                            match = re.search(r'service-policy\s+\S+\s+(input|output)\s+(\S+)(?:_(\d+)([MKmk]))?', ne_response)
                            if match:
                                cos_full = match.group(2)  # "CONVERGENCE_2000K"
                                parts = cos_full.split('_')
                                cos = parts[0]  # "CONVERGENCE"
                                if len(parts) > 1:
                                    bandwidth = parts[1].upper()  # "2000K"
                                else:
                                    bandwidth = None
                        elif "rate-limit" in ne_response:
                            # Try to match the kbps pattern first
                            match = re.search(r'rate-limit\s+output\s+localport\s+cir\s+(\d+)\s+kbps', ne_response)
                            if match:
                                cos = "CLASSIC"
                                bandwidth_value = int(match.group(1)) / 1000
                                # Format as integer if it's a whole number, otherwise keep decimal
                                if bandwidth_value == int(bandwidth_value):
                                    bandwidth = f"{int(bandwidth_value)}M"
                                else:
                                    bandwidth = f"{bandwidth_value}M"
                            else:
                                # Try to match the mbps pattern
                                match = re.search(r'rate-limit\s+output\s+localport\s+cir\s+(\d+)\s+mbps', ne_response)
                                if match:
                                    cos = "CLASSIC"
                                    bandwidth_value = int(match.group(1))
                                    bandwidth = f"{bandwidth_value}M"
                                else:
                                    cos = "CLASSIC"
                                    bandwidth = None  # Could not determine bandwidth

                output_cmd_8 = None
                ipv6_address = None
                ipv6_status = "IPv6 address not found"

                if smartgroup_status == "Smartgroup found":
                    # Extract VLAN ID from smartgroup_info_cos (e.g., "sat_gei-3/1/0/19.1000" -> "1000")
                    if '.' in smartgroup_info_cos:
                        vlan_id = smartgroup_info_cos.split('.')[1]  # Get the part after the dot
                        command_8 = f"show running-config-interface {smartgroup_info_cos.split('.')[0]}.{serialized_data['interface_name'].split('.')[1]} | include ipv6 address"
                    else:
                        command_8 = f"show running-config-interface {smartgroup_info_cos}.{serialized_data['interface_name'].split('.')[1]} | include ipv6 address"
                else:
                    command_8 = f"show running-config-interface {smartgroup_info_cos.split('.')[0]}.{serialized_data['interface_name'].split('.')[1]} | include ipv6 address"

                commands_list = [
                    command_8
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_8 = response.json()

                # Extract the relevant data
                for item in output_cmd_8["data"]:
                
                    # f"show running-config-interface {serialized_data['interface_name']} | include ipv6 address"
                    if item["command"] == command_8:
                        ne_response = item["ne_response"]
                        
                        # Improved regex to explicitly look for "ipv6 address" followed by the IPv6 address
                        ipv6_match = re.search(r'ipv6 address ([a-fA-F0-9:]+/\d+)', ne_response)
                        if ipv6_match:
                            ipv6_address = ipv6_match.group(1)  # Group 1 contains just the address without "ipv6 address"
                            ipv6_status = "Configuration found"
                        else:
                            # Fallback to the more generic pattern if the specific format isn't found
                            ipv6_match = re.search(r'\b([a-fA-F0-9:]+(?:/\d+)?)\b', ne_response)
                            if ipv6_match and ":" in ipv6_match.group(1) and not re.match(r'\d{2}:\d{2}:\d{2}', ipv6_match.group(1)):
                                ipv6_address = ipv6_match.group(1)
                                ipv6_status = "Configuration found"

                # If GOV, get lan IP
                output_cmd_10 = None
                output_cmd_11 = None
                # Extract the relevant data
                lan_ips_v4 = []
                prefix_status_v4 = "Configuration not found"

                if serialized_data['product'] == '1gov':

                    commands_list = [
                        f"show running-config | include ip prefix-list.*{serialized_data['vpn_name']}.*{serialized_data['site_abr']}"
                    ]

                    payload = {
                        'ne_ip': serialized_data['ne_ip'],
                        'commands': commands_list
                    }

                    # Make API request with proper URL joining and error handling
                    api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                    response = requests.post(
                        url=api_endpoint,
                        json=payload,  # Use json instead of data for proper serialization
                        verify=False,
                        timeout=240  # Add timeout to prevent hanging
                    )
                    response.raise_for_status()  # Raise exception for bad HTTP status codes
                    output_cmd_10 = response.json()

                    for item in output_cmd_10["data"]:
                        # Check if this is the IPv4 prefix list command
                        if "ip prefix-list" in item["command"] and serialized_data['vpn_name'] in item["command"] and serialized_data['site_abr'] in item["command"]:
                            ne_response = item["ne_response"]
                            
                            # Check if "ip prefix-list" exists in the response
                            if "ip prefix-list" in ne_response:
                                # Use regex to extract all IPv4 addresses with subnet masks after "permit"
                                matches = re.findall(r'permit\s+((?:\d{1,3}\.){3}\d{1,3}\s+\d+)', ne_response)
                                if matches:
                                    lan_ips_v4 = matches  # Extract all matched IPv4 addresses with subnet masks
                                    prefix_status_v4 = "Configuration found"

                # Initialize IPv6 variables
                lan_ips_v6 = []
                prefix_status_v6 = "Configuration not found"

                if serialized_data['product'] == '1gov' and serialized_data.get('pev6_wan_ip') is not None:

                    commands_list = [
                        f"show running-config | include ipv6 prefix-list.*{serialized_data['vpn_name']}.*{serialized_data['site_abr']}"
                    ]

                    payload = {
                        'ne_ip': serialized_data['ne_ip'],
                        'commands': commands_list
                    }

                    # Make API request with proper URL joining and error handling
                    api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                    response = requests.post(
                        url=api_endpoint,
                        json=payload,  # Use json instead of data for proper serialization
                        verify=False,
                        timeout=240  # Add timeout to prevent hanging
                    )
                    response.raise_for_status()  # Raise exception for bad HTTP status codes
                    output_cmd_11 = response.json()

                    for item in output_cmd_11["data"]:
                        # Check if this is the IPv6 prefix list command
                        if "ipv6 prefix-list" in item["command"] and serialized_data['vpn_name'] in item["command"] and serialized_data['site_abr'] in item["command"]:
                            ne_response = item["ne_response"]
                            
                            # Check if "ipv6 prefix-list" exists in the response
                            if "ipv6 prefix-list" in ne_response:
                                # Use regex to extract all IPv6 addresses with prefix lengths after "permit"
                                matches = re.findall(r'permit\s+([a-fA-F0-9:]+(?:/\d+)?)', ne_response)
                                if matches:
                                    lan_ips_v6 = matches  # Extract all matched IPv6 addresses with prefix lengths
                                    prefix_status_v6 = "Configuration found"


                command_12 = f"show running-config-vrf vrf {serialized_data['vrf_name']} | include {serialized_data['ce_wan_ip']} remote-as"

                commands_list = [
                    command_12
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_12 = response.json()

               # Extract the relevant data
                for item in output_cmd_12["data"]:

                    if item["command"] == command_12:  # Match the specific command
                        ne_response = item["ne_response"]
                        # Initialize variables
                        remote_as_value = None
                        remote_as_status = "Configuration not found"

                        # Check if the configuration exists in the ne_response
                        if "remote-as" in ne_response:
                            # Use regex to extract the value after "remote-as"
                            # match = re.search(r'neighbor\s+\d+\.\d+\.\d+\.\d+\s+remote-as\s+(\d+)', ne_response)
                            # if match:
                            #     remote_as_value = match.group(1)  # Extract the matched value (e.g., 64647)
                            #     remote_as_status = "Configuration found"
                            match = re.search(r'neighbor\s+\d+\.\d+\.\d+\.\d+\s+remote-as\s+(\d+(?:\.\d+)?)', ne_response)
                            if match:
                                remote_as_value = match.group(1)  # Extract the matched value (e.g., 64647 or 64086.60180)
                                remote_as_status = "Configuration found"


                # Initialize variables
                v4_route_map_value = None
                v4_route_map_status = "Configuration not found"

                command_13 = f"show running-config | include {serialized_data['ce_wan_ip']} route-map"

                commands_list = [
                    command_13
                ]

                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }

                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_13 = response.json()

                # Extract the relevant data
                for item in output_cmd_13["data"]:

                    if item["command"] == command_13:  # Match the specific command
                        ne_response = item["ne_response"]

                        # Check if "neighbor" exists in the response
                        if "neighbor" in ne_response:
                            # Use regex to extract the value after "route-map"
                            match = re.search(r'neighbor\s+\d+\.\d+\.\d+\.\d+\s+route-map\s+(\S+)\s+(in|out)', ne_response)
                            if match:
                                v4_route_map_value = match.group(1)  # Extract the matched value (e.g., PRI_IPVPN_COLO_IN)
                                v4_route_map_status = "Configuration found"

                # Initialize variables
                output_cmd_14 = None
                v4_local_pref_value = None
                v4_local_pref_status = "Configuration not found"

                if v4_route_map_status == "Configuration found":

                    command_14 = f"show route-map {v4_route_map_value}"

                    commands_list = [
                        command_14
                    ]

                    payload = {
                        'ne_ip': serialized_data['ne_ip'],
                        'commands': commands_list
                    }

                    # Make API request with proper URL joining and error handling
                    api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                    response = requests.post(
                        url=api_endpoint,
                        json=payload,  # Use json instead of data for proper serialization
                        verify=False,
                        timeout=240  # Add timeout to prevent hanging
                    )
                    response.raise_for_status()
                    output_cmd_14 = response.json()
                    # Extract the relevant data
                    for item in output_cmd_14["data"]:
                    
                        if item["command"] == command_14:  # Match the specific command
                            ne_response = item["ne_response"]
                    
                            # Check if "local-preference" exists in the response
                            local_preference_match = re.search(r'local-preference\s+(\d+)', ne_response)
                            if local_preference_match:
                                v4_local_pref_value = local_preference_match.group(1)  # Extract just the number (e.g., "150")
                                v4_local_pref_status = "Configuration found"


                # Initialize variables
                output_cmd_15 = None
                v6_route_map_value = None
                v6_route_map_status = "Configuration not found"
                output_cmd_16 = None
                v6_local_pref_value = None
                v6_local_pref_status = "Configuration not found"

                if serialized_data['cev6_wan_ip'] is not None:

                    command_15 = f"show running-config | include {serialized_data['cev6_wan_ip']} route-map"

                    commands_list = [
                        command_15
                    ]

                    payload = {
                        'ne_ip': serialized_data['ne_ip'],
                        'commands': commands_list
                    }

                    # Make API request with proper URL joining and error handling
                    api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                    response = requests.post(
                        url=api_endpoint,
                        json=payload,  # Use json instead of data for proper serialization
                        verify=False,
                        timeout=240  # Add timeout to prevent hanging
                    )
                    response.raise_for_status()  # Raise exception for bad HTTP status codes
                    output_cmd_15 = response.json()

                    # Extract the relevant data
                    for item in output_cmd_15["data"]:

                        if item["command"] == output_cmd_15:  # Match the specific command
                            ne_response = item["ne_response"]

                            # Check if "neighbor" exists in the response
                            if "neighbor" in ne_response:
                                # Use regex to extract the value after "route-map"
                                match = re.search(r'neighbor\s+\d+\.\d+\.\d+\.\d+\s+route-map\s+(\S+)\s+(in|out)', ne_response)
                                if match:
                                    v6_route_map_value = match.group(1)  # Extract the matched value (e.g., PRI_IPVPN_COLO_IN)
                                    v6_route_map_status = "Configuration found"

                    if v6_route_map_value == "Configuration found":

                        command_16 = f"show route-map {v4_route_map_value}"

                        commands_list = [
                            command_16
                        ]

                        payload = {
                            'ne_ip': serialized_data['ne_ip'],
                            'commands': commands_list
                        }

                        # Make API request with proper URL joining and error handling
                        api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                        response = requests.post(
                            url=api_endpoint,
                            json=payload,  # Use json instead of data for proper serialization
                            verify=False,
                            timeout=240  # Add timeout to prevent hanging
                        )
                        response.raise_for_status()
                        output_cmd_16 = response.json()
                        # Extract the relevant data
                        for item in output_cmd_16["data"]:
                        
                            if item["command"] == command_16:  # Match the specific command
                                ne_response = item["ne_response"]
                        
                                # Check if "local-preference" exists in the response
                                local_preference_match = re.search(r'local-preference\s+(\d+)', ne_response)
                                if local_preference_match:
                                    v4_local_pref_value = local_preference_match.group(1)  # Extract just the number (e.g., "150")
                                    v4_local_pref_status = "Configuration found"

                # Initialize variables
                soo_value = None
                soo_status = "Configuration not found"
                
                command_17 = f"show running-config-vrf vrf {serialized_data['vrf_name']} | include {serialized_data['ce_wan_ip']} soo"
                
                commands_list = [
                    command_17
                ]
                
                payload = {
                    'ne_ip': serialized_data['ne_ip'],
                    'commands': commands_list
                }
                
                # Make API request with proper URL joining and error handling
                api_endpoint = url_zte + '/zte/agg/v1/show_config/'
                response = requests.post(
                    url=api_endpoint,
                    json=payload,  # Use json instead of data for proper serialization
                    verify=False,
                    timeout=240  # Add timeout to prevent hanging
                )
                response.raise_for_status()  # Raise exception for bad HTTP status codes
                output_cmd_17 = response.json()
                
                # Extract the relevant data
                for item in output_cmd_17["data"]:
                    if item["command"] == command_17:  # Match the specific command
                        ne_response = item["ne_response"]
                
                        # Skip the first line (command line) and process the remaining lines
                        lines = ne_response.splitlines()[1:]  # Skip first line
                        response_content = '\n'.join(lines)  # Rejoin without the first line
                        
                        # Check if the CE WAN IP and "soo" exist in the response
                        if serialized_data['ce_wan_ip'] in response_content and "soo" in response_content:
                            # Use regex to extract the SOO value after the specific neighbor IP
                            pattern = rf'neighbor\s+{re.escape(serialized_data["ce_wan_ip"])}\s+soo\s+(\d+:\d+)'
                            match = re.search(pattern, response_content)
                            if match:
                                soo_value = match.group(1)  # Extract the matched SOO value (e.g., 23736:452)
                                soo_status = "Configuration found"
                            else:
                                soo_status = "Configuration not found"
                        else:
                            soo_status = "Configuration not found"


                api_response['status'] = 'success'
                api_response['message'] = 'Data retrieved successfully'
                api_response['data'] = {
                    "loopback_ip": {
                        "status": loopback_status,
                        "value": loppback_ip_address,
                    },
                    "nid_upe_name" : {
                        "status": config_nid_upe_found,
                        "value": interface_name_info,
                    },
                    "pe_ipv4_address": {
                        "status": pe_wan_ip_router_status,
                        "value": pe_wan_ip_router,
                    },
                    "pe_ipv6_address": {
                        "status": ipv6_status,
                        "value": ipv6_address,
                    },
                    "smartgroup_interface": {
                        "status": smartgroup_status,
                        "value": smartgroup_info,
                    },
                    "pe_interface": {
                        "status": pe_wan_ip_router_status,
                        "value": smartgroup_info_cos,
                    },
                    "description": {
                        "status": description_status,
                        "value": description_info,
                    },
                    "bandwidth": {
                        "status": "Configuration found" if bandwidth else "Configuration not found",
                        "value": bandwidth,
                    },
                    "cos": {
                        "status": "Configuration found" if cos else "Configuration not found",
                        "value": cos,
                    },
                    "lan_ips_v4": {
                        "status": prefix_status_v4,
                        "value": lan_ips_v4,
                    },
                    "lan_ips_v6": {
                        "status": prefix_status_v6,
                        "value": lan_ips_v6,
                    },
                    "remote_as": {
                        "status": remote_as_status,
                        "value": remote_as_value,
                    },
                    "route_map_ipv4": {
                        "status": v4_route_map_status,
                        "value": v4_route_map_value,
                    },
                    "local_preference_v4": {
                        "status": v4_local_pref_status,
                        "value": v4_local_pref_value,
                    },
                    "route_map_ipv6": {
                        "status": v6_route_map_status,
                        "value": v6_route_map_value,
                    },
                    "local_preference_v6": {
                        "status": v6_local_pref_status,
                        "value": v6_local_pref_value,
                    },
                    "soo": {
                        "status": soo_status,
                        "value": soo_value,
                    },
                }
                

                api_response['ne_response_cmd_1'] = output_cmd_1
                api_response['ne_response_cmd_2'] = output_cmd_2
                api_response['ne_response_cmd_3'] = output_cmd_3
                api_response['ne_response_cmd_4'] = output_cmd_4
                api_response['ne_response_cmd_5'] = output_cmd_5
                api_response['ne_response_cmd_6'] = output_cmd_6
                api_response['ne_response_cmd_7'] = output_cmd_7
                api_response['ne_response_cmd_8'] = output_cmd_8
                api_response['ne_response_cmd_10'] = output_cmd_10
                api_response['ne_response_cmd_11'] = output_cmd_11
                api_response['ne_response_cmd_12'] = output_cmd_12
                api_response['ne_response_cmd_13'] = output_cmd_13
                api_response['ne_response_cmd_14'] = output_cmd_14
                api_response['ne_response_cmd_15'] = output_cmd_15
                api_response['ne_response_cmd_16'] = output_cmd_16
                api_response['ne_response_cmd_17'] = output_cmd_17

                # Return the response
                return Response(api_response, status=status.HTTP_200_OK)


        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            # log_text(filename=log_file, content=output_message)
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            api_response['data'] = output_error
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)