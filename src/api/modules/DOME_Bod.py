from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import DomeBodSerializer
import requests, json, time, re, os, ipaddress, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps

class API(APIView):

    # authentication_classes = ()     #exclude from global authentication
    # permission_classes = ()

    @swagger_auto_schema(
        request_body=DomeBodSerializer,
        responses={201: DomeBodSerializer()},
    )

    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = DomeBodSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':request.data,
                    'message':output_message,
                    'api_output':str(input_data.errors),
                    'status':'failed',
                    'log_file':'N/A',
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = 'DOME_BOD_{}_{}_{}@{}_by_{}.txt'.format(serialized_data['service_id'], serialized_data['node_name'], date_now, time_now, user)		# Create Log File

            # LOG CREATED TIME #
            output_message = "File created at {} {}".format(date_now, time_now) 
            log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

            #                                                #
            #              CHECK SERVICE TYPE                #
            #                                                #
            if not 'Direct Service (Metro Ethernet)' in serialized_data['product_name']:
                output_message = 'Invalid service type ({}). Only product Direct Service (Metro Ethernet) is allowed'.format(serialized_data['product_name'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':output_message,
                    'api_output':output_message,
                    'status':'failed',
                    'log_file':log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output_message
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)


            #                                      #
            #        GET PORT DETAILS              #
            #                                      #

            # sample API output
            # {
            # "status": "success",
            # "output": 
            # {
            #     "name": "xe-5/2/3",
            #     "description": "Test",
            #     "disable": [
            #     null
            #     ],
            #     "per-unit-scheduler": [
            #     null
            #     ],
            #     "flexible-vlan-tagging": [
            #     null
            #     ],
            #     "mtu": 9192,
            #     "framing": {
            #     "lan-phy": [
            #         null
            #     ]
            #     }
            # }
            # }

            # {
            # "output": {
            #     "name": "xe-7/3/0",
            #     "description": "LINK COC to NPE01HWBAU gi-7/0/0",
            #     "per-unit-scheduler": [
            #     null
            #     ],
            #     "flexible-vlan-tagging": [
            #     null
            #     ],
            #     "mtu": 9192,
            #     "framing": {
            #     "lan-phy": [
            #         null
            #     ]
            #     }
            # }
            # }

            url = 'https://pisa-juniper.prdin.kubix.tm/juniper/pe/' # declare main url variable

            interface_split = serialized_data['interface'].split('.')
            port = interface_split[0]
            vlan = interface_split[1]

            payload = {
                'ne_ip': serialized_data['device_id'],
                'port': port
            }
            response = requests.get(url+'v2/show_port/', params=payload, verify=False)
            output = response.json()

            if output['status'] == 'failed':
                error_message = 'An error has occurred while retrieving port ({}) details'.format(port)
                output_message = error_message + '\n[error message: {}]'.format(output['output'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':error_message,
                    'api_output':str(output['output']),
                    'status':'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            else:
                if output['output'] == 'port is not exist':
                    error_message = 'Port ({}) is not exist in PE ({})'.format(port, serialized_data['node_name'])
                    log_text(filename=log_file, content=error_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    port_details = output['output']
                    output_message = 'Successfully retrieved port ({}) details'.format(port)
                    log_text(filename=log_file, content=output_message, ne_output=dumps(port_details, indent=4, separators=(',', ': ')))

            #                                                #
            #        CHECK STATUS DISABLE IN PORT            #
            #                                                #

            if 'disable' in port_details.keys():
                error_message = 'Port ({}) is disable in PE ({})'.format(port, serialized_data['node_name'])
                log_text(filename=log_file, content=error_message, ne_output=dumps(port_details, indent=4, separators=(',', ': ')))
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':error_message,
                    'api_output':str(output['output']),
                    'status':'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                      #
            #        GET INTERFACE DETAILS         #
            #                                      #

            # sample API output
            # {
            # "status": "success",
            # "output": {
            #     "name": "xe-7/3/0",
            #     "unit": [
            #     {
            #         "name": 1038,
            #         "description": "DOME - SYARIKAT PERUMAHAN NEGARA BERHAD (SPNB) - SVC NO: LD1048275511 / PS1048275512 - 30M",
            #         "bandwidth": "10m",
            #         "vlan-id": 1038,
            #         "family": {
            #         "inet": {
            #             "mtu": 1500,
            #             "policer": {
            #             "input": "POLICER_30M_10GPIC"
            #             },
            #             "address": [
            #             {
            #                 "name": "*********/30"
            #             }
            #             ]
            #         }
            #         }
            #     }
            #     ]
            # }
            # }

            payload = {
                'ne_ip': serialized_data['device_id'],
                'interface': serialized_data['interface']
            }
            response = requests.get(url+'v2/show_interface/', params=payload, verify=False)
            output = response.json()

            if output['status'] == 'failed':
                error_message = 'An error has occurred while retrieving interface ({}) details'.format(serialized_data['interface'])
                output_message = error_message + '\n[error message: {}]'.format(output['output'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':error_message,
                    'api_output':str(output['output']),
                    'status':'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            else:
                if output['output'] == 'interface is not exist':
                    error_message = 'Interface ({}) is not exist in PE ({})'.format(serialized_data['interface'], serialized_data['node_name'])
                    log_text(filename=log_file, content=error_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    interface_details = output['output']
                    output_message = 'Successfully retrieved interface ({}) details'.format(serialized_data['interface'])
                    log_text(filename=log_file, content=output_message, ne_output=dumps(interface_details, indent=4, separators=(',', ': ')))

            #                                                #
            #        CHECK STATUS DISABLE IN INTERFACE       #
            #                                                #

            if 'disable' in interface_details['unit'].keys():
                error_message = 'Interface ({}) is disable in PE ({})'.format(serialized_data['interface'], serialized_data['node_name'])
                log_text(filename=log_file, content=error_message, ne_output=dumps(interface_details, indent=4, separators=(',', ': ')))
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':error_message,
                    'api_output':str(output['output']),
                    'status':'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = output['output']
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                                #
            #        CHECK PE IP AND INTERFACE MATCH         #
            #                                                #

            pe_wan_ip_subnet = interface_details['unit']['family']['inet']['address']['name']
            pe_wan_ip = pe_wan_ip_subnet.split('/')[0]

            if pe_wan_ip == serialized_data['pe_wan_ip']:
                output_message = 'PE IP Address ({}) match with Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
                log_text(filename=log_file, content=output_message)
            else:
                error_message = 'Mismatched PE IP Address ({}) and Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
                output_message = error_message + '\n[error message: PE IP Address retrieved from NE is {}]'.format(pe_wan_ip)
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':error_message,
                    'api_output': 'PE IP Address retrieved from NE is {}'.format(pe_wan_ip),
                    'status':'failed',
                    'log_file': log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = 'PE IP Address retrieved from NE is {}'.format(pe_wan_ip)
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                                    #
            #        CHECK PE INT DESCRIPTION (EXTENSION)        #
            #                                                    #

            if re.search('extension', interface_details['unit']['description'], re.IGNORECASE):
                error_message = 'Change of bandwidth is not required due to BOD extension'
                output_message = error_message + "\n[error message: Interface description retrieved from NE is '{}']".format(interface_details['unit']['description'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':error_message,
                    'api_output':"Interface description retrieved from NE is '{}'".format(interface_details['unit']['description']),
                    'status':'failed',
                    'log_file':log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = error_message
                api_response['data'] = "Interface description retrieved from NE is '{}'".format(interface_details['unit']['description'])
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                                           #
            #        DOWNGRADE: CHECK OVP FROM PE INT DESCRIPTION       #
            #                                                           #

            over_provision = False
            if re.search('- O$', interface_details['unit']['description'], re.IGNORECASE):

                if int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) >  int(re.sub(r'\D', '', serialized_data['new_bandwidth'])):
                    error_message = 'Change of bandwidth is not required due to Over Provision'
                    output_message = error_message + "\n[error message: Interface description retrieved from NE is '{}']".format(interface_details['unit']['description'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':"Interface description retrieved from NE is '{}'".format(interface_details['unit']['description']),
                        'status':'failed',
                        'log_file':log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = "Interface description retrieved from NE is '{}'".format(interface_details['unit']['description'])
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    over_provision = True
                    output_message = "Interface description ({}) retrieved from NE shows this circuit is circuit Over Provision".format(interface_details['unit']['description'])
                    log_text(filename=log_file, content=output_message)

            # if int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) >  int(re.sub(r'\D', '', serialized_data['new_bandwidth'])):

            #     if re.search('-o', interface_details['unit']['description'], re.IGNORECASE):
            #         error_message = 'Change of bandwidth is not required due to Over Provision'
            #         output_message = error_message + "\n[error message: Interface description retrieved from NE is '{}']".format(interface_details['unit']['description'])
            #         log_text(filename=log_file, content=output_message)
            #         DB_data = {
            #             'order_type':'BOD',
            #             'service_type':'DOME',
            #             'input_details':serialized_data,
            #             'message':error_message,
            #             'api_output':"Interface description retrieved from NE is '{}'".format(interface_details['unit']['description']),
            #             'status':'failed',
            #             'log_file':log_file,
            #             'config_file': 'N/A',
            #             'user': user
            #         }
            #         api_response['status'] = 'failed'
            #         api_response['message'] = error_message
            #         api_response['data'] = "Interface description retrieved from NE is '{}'".format(interface_details['unit']['description'])
            #         log_database(data_DB=DB_data)
            #         return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                        #
            #        CHECK INTERFACE BW EXIST        #
            #                                        #

            bw = str()
            if 'bandwidth' in interface_details['unit'].keys():
                bw = interface_details['unit']['bandwidth']
            else:

                if re.search('imse', serialized_data['node_name'], re.IGNORECASE):

                    #                                          #
                    #        GET INTERFACE COS DETAILS         #
                    #                                          #

                    # sample API output
                    # {
                    # "status": "success",
                    # "output": {
                    #     "name": "xe-7/3/0",
                    #     "unit": [
                    #     {
                    #         "name": 1038,
                    #         "forwarding-class": "SG2",
                    #         "scheduler-map": "SCHED_CLASSIC",
                    #         "shaping-rate": {
                    #         "rate": "10m"
                    #         }
                    #     }
                    #     ]
                    # }
                    # }

                    response = requests.get(url+'v2/show_interface_cos/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = 'An error has occurred while retrieving interface ({}) COS details'.format(serialized_data['interface'])
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        if output['output'] == 'interface CoS is not exist':
                            error_message = 'Interface ({}) CoS is not exist in PE ({})'.format(serialized_data['interface'], serialized_data['node_name'])
                            log_text(filename=log_file, content=error_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':error_message,
                                'api_output':output['output'],
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = error_message
                            api_response['data'] = output['output']
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            interface_cos_details = output['output']
                            output_message = 'Successfully retrieved interface ({}) COS details'.format(serialized_data['interface'])
                            log_text(filename=log_file, content=output_message, ne_output=dumps(interface_cos_details, indent=4, separators=(',', ': ')))

                            bw = interface_cos_details['unit']['shaping-rate']['rate']

                else:
                    error_message = 'Interface ({}) bandwidth is not exist in PE ({})'.format(serialized_data['interface'], serialized_data['node_name'])
                    log_text(filename=log_file, content=error_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':"Interface retrieved from NE is '{}'".format(interface_details),
                        'status':'failed',
                        'log_file':log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = "Interface retrieved from NE is '{}'".format(interface_details)
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            #                                   #
            #        COMPARE EXISTING BW        #
            #                                   #
            cgate_error_message = str()
            config_file = str()
            pe_configuration = str()
            ce_configuration = str()

            if not re.match(bw, serialized_data['old_bandwidth'], re.IGNORECASE) and not re.match(bw, serialized_data['new_bandwidth'], re.IGNORECASE):
                output_message = 'Existing bandwidth ({}) is not match old bandwidth ({}) and new bandwidth ({})'.format(bw, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
                log_text(filename=log_file, content=output_message)
                DB_data = {
                    'order_type':'BOD',
                    'service_type':'DOME',
                    'input_details':serialized_data,
                    'message':output_message,
                    'api_output':'Existing bandwidth ({}) retrieved from PE'.format(bw),
                    'status':'failed',
                    'log_file':log_file,
                    'config_file': 'N/A',
                    'user': user
                }
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = 'Existing bandwidth ({}) retrieved from PE'.format(bw)
                log_database(data_DB=DB_data)
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

            elif re.match(bw, serialized_data['new_bandwidth'], re.IGNORECASE):
                output_message = 'Existing bandwidth ({}) match new bandwidth ({}). No change is required at PE'.format(bw, serialized_data['new_bandwidth'])
                log_text(filename=log_file, content=output_message)
                # Initialize variable due to no RFC created
                cgate_error_message = "RFC application is not required"
                config_file = "N/A"

            elif re.match(bw, serialized_data['old_bandwidth'], re.IGNORECASE):

                #                                                #
                #               CHECK PROTOCOL TYPE              #
                #                                                #

                if re.search('BGP', serialized_data['protocol'], re.IGNORECASE):

                    #                                                #
                    #               CHECK BGP NEIGHBOR STATUS        #
                    #                                                #

                    # Sample API Output
                    # {
                    # "status": "success",
                    # "output": {
                    #     "state": "Established"
                    # },
                    # "ne_response": "--- JUNOS 18.4R3-S4.2 Kernel 64-bit  JNPR-11.0-20200618.2bc7e35_buil\r\nJUNOS tip:\r\nUse ESC-/ in the CLI to expand strings into matching words from the\r\ncommand line history.\r\n\r\n{master}\r\<EMAIL>> show bgp neighbor ************** | match state \r\n  Type: External    State: Established    Flags: <Sync RSync PeerIntfNoMpls>\r\n  Last State: EstabSync     Last Event: RecvKeepAlive\r\n  I/O Session Thread: bgpio-0 State: Enabled\r\n    RIB State: BGP restart is complete\r\n    RIB State: VPN restart is complete\r\n    Send state: in sync\r\n\r\n{master}\r\<EMAIL>> "
                    # }

                    payload = {
                        'ne_ip': serialized_data['device_id'],
                        'ce_ip': serialized_data['ce_wan_ip']
                    }

                    response = requests.get(url+'v1/show_bgp_neighbor_status/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = 'An error has occurred while retrieving BGP Neighbor status from PE'
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': 'N/A',
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = str(output['output'])
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        if output['output'] == 'No state is found':
                            output_message = 'No state is found for CE IP Address ({}) returned from PE'.format(serialized_data['ce_wan_ip'])
                            log_text(filename=log_file, content=output_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':output_message,
                                'api_output':'No state is found for CE IP Address ({})'.format(serialized_data['ce_wan_ip']),
                                'status':'failed',
                                'log_file':log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = 'No state is found for CE IP Address ({})'.format(serialized_data['ce_wan_ip'])
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            if output['output']['state'] != 'Established':
                                output_message = 'BGP Neighbor status for CE IP Address ({}) returned from PE ({}) is not established'.format(serialized_data['ce_wan_ip'], output['output']['state'])
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':output_message,
                                    'api_output':'BGP Neighbor status retrieved from PE is ({})'.format(output['output']['state']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': 'N/A',
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = output_message
                                api_response['data'] = 'BGP Neighbor status retrieved from PE is ({})'.format(output['output']['state'])
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                            else:
                                output_message = 'BGP Neighbor status for CE IP Address ({}) returned from PE is ({})'.format(serialized_data['ce_wan_ip'], output['output']['state'])
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])


                #                                               #
                #               PING IP (CE WAN)                #
                #                                               #

                ping_ip_ce_wan = str()
                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'destination_ip': serialized_data['ce_wan_ip'],
                    'source_ip': serialized_data['pe_wan_ip']
                }

                response = requests.get(url+'v2/ping_ip/', params=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occurred while retrieving ping ip ce wan ({}) from PE'.format(serialized_data['ce_wan_ip'])
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': 'N/A',
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = str(output['output'])
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output']['result'] == 'ping is failed':
                        ping_ip_ce_wan = 'fail'
                        output_message = 'Ping IP CE Wan ({}) is failed'.format(serialized_data['ce_wan_ip'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                    else:
                        ping_ip_ce_wan = 'success'
                        output_message = 'Ping IP CE Wan ({}) is success'.format(serialized_data['ce_wan_ip'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

                #                                               #
                #               PING IP (CE LAN)                #
                #                                               #

                ping_ip_ce_lan = str()

                if serialized_data['ce_lan_ip'] is not None:

                    if serialized_data['ce_lan_ip'] != '':

                        networkip = ipaddress.IPv4Address(serialized_data['ce_lan_ip'])
                        networkip = networkip + 1

                        payload = {
                            'ne_ip': serialized_data['device_id'],
                            'destination_ip': networkip,
                            'source_ip': serialized_data['pe_wan_ip']
                        }

                        response = requests.get(url+'v2/ping_ip/', params=payload, verify=False)
                        output = response.json()

                        if output['status'] == 'failed':
                            error_message = 'An error has occurred while retrieving ping ip ce lan ({}) from PE'.format(networkip)
                            output_message = error_message + '\n[error message: {}]'.format(output['output'])
                            log_text(filename=log_file, content=output_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':error_message,
                                'api_output':str(output['output']),
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = error_message
                            api_response['data'] = str(output['output'])
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            if output['output']['result'] == 'ping is failed':
                                ping_ip_ce_lan = 'fail'
                                output_message = 'Ping IP CE Lan ({}) is failed'.format(networkip)
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            else:
                                ping_ip_ce_lan = 'success'
                                output_message = 'Ping IP CE Lan ({}) is success'.format(networkip)
                                log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

                #                                               #
                #               CHECK INPUT POLICER             #
                #                                               #

                interface_policer, global_policer = False, False
                # check if interface details dictionary key contains policer and input
                if 'policer' in interface_details['unit']['family']['inet'].keys():
                    if 'input' in interface_details['unit']['family']['inet']['policer'].keys():
                        interface_policer = True

                        #                                                        #
                        #               CHECK GLOBAL POLICER EXIST               #
                        #                                                        #
                        # sample output
                        # "output": {
                        # 	"name": "POLICER_8M_10GPIC",
                        # 	"logical-interface-policer": [
                        # 		null
                        # 	],
                        # 	"if-exceeding": {
                        # 		"bandwidth-limit": "8m",
                        # 		"burst-size-limit": 6250000
                        # 	},
                        # 	"then": {
                        # 		"discard": [
                        # 			null
                        # 		]
                        # 	}
                        # }

                        # Generate new policer name
                        policer_name = str()
                        if 'ge' in interface_details['name']:
                            policer_name = 'POLICER_{}_1GPIC'.format(serialized_data['new_bandwidth'].upper())
                        elif 'xe' in interface_details['name']:
                            policer_name = 'POLICER_{}_10GPIC'.format(serialized_data['new_bandwidth'].upper())

                        payload = {
                            'ne_ip': serialized_data['device_id'],
                            'policer': policer_name
                        }

                        response = requests.get(url+'v2/check_global_policer/', params=payload, verify=False)
                        output = response.json()

                        if output['status'] == 'failed':
                            error_message = 'An error has occurred while retrieving Global Policer from PE'
                            output_message = error_message + '\n[error message: {}]'.format(output['output'])
                            log_text(filename=log_file, content=output_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':error_message,
                                'api_output':str(output['output']),
                                'status':'failed','log_file': log_file,
                                'config_file': 'N/A',
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = error_message
                            api_response['data'] = str(output['output'])
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            if output['output'] == 'policer is not exist':
                                # global_policer = False
                                output_message = 'Global Policer ({}) is not exist in PE'.format(policer_name)
                                log_text(filename=log_file, content=output_message, ne_output=output['output'])
                            else:
                                global_policer = True
                                output_message = 'Global Policer ({}) is exist in PE'.format(policer_name)
                                log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))


                #                                                         #
                #          Validate PACKAGE FROM INT DESCRIPTION          #
                #                                                         #

                package = str()
                if re.search('SLG', interface_details['unit']['description'], re.IGNORECASE) and re.search('PRIMARY', interface_details['unit']['description'], re.IGNORECASE):
                    package = 'GOLD'
                elif re.search('SLG', interface_details['unit']['description'], re.IGNORECASE) and re.search('SECONDARY', interface_details['unit']['description'], re.IGNORECASE):
                    package = 'GOLD'
                elif re.search('SLG', interface_details['unit']['description'], re.IGNORECASE):
                    package = 'SILVER'
                else:
                    package = 'NORMAL'

                #                                       #
                #          CHECK FAMILY INETv6          #
                #                                       #

                # sample output
                # {
                # 	"name": "xe-7/3/0",
                # 	"unit": [
                # 	{
                # 		"name": 1020,
                # 		"description": "IPVPN - DORADO - SVC NO:PS3000162419",
                # 		"bandwidth": "8m",
                # 		"vlan-id": 1020,
                # 		"family": {
                # 			"inet": {
                # 				"policer": {
                # 					"input": "POLICER_8M_10GPIC"
                # 				},
                # 				"address": [
                # 				{
                # 					"name": "**************/30"
                # 				}
                # 				]
                # 			},
                # 			"inet6": {
                # 				"policer": {
                # 					"input": "POLICER_8M_10GPIC"
                # 				},
                # 				"address": [
                # 				{
                # 					"name": "2400:7400:0002:0000:0000:0000:0000:0b95/127"
                # 				}
                # 				]
                # 			}
                # 		}
                # 	}
                # 	]
                # }

                ipv6 = False
                if 'inet6' in interface_details['unit']['family'].keys():
                    ipv6 = True

                #                                                      #
                #          GENERATE PE DOME BOD CONFIGURATION          #
                #                                                      #
                config_file = 'PE_DOME_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['node_name'],  date_now, time_now, user)		#create config file

                payload = {
                    'pe_node': serialized_data['node_name'],
                    'interface': serialized_data['interface'],
                    'customer': serialized_data['cust_name'],
                    'leg_name': serialized_data['leg_id'],
                    'service_id': serialized_data['service_id'],
                    'bandwidth': serialized_data['new_bandwidth'].lower(),
                    'global_policer': not global_policer,
                    'over_provision': over_provision,
                    'ipv6': ipv6,
                    'filename': config_file,
                    'package': package
                }

                response = requests.post(url+'v2/dome/generate_config_modify_bw/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while generating PE configurations'
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = output['output']
                    pe_configuration = 'Proof of Config (PE)\n' + config_file + '\n' + output['configuration']
                    log_text(filename=log_file, content=output_message)

                #                                                #
                #                   CHECK CPU                    #
                #                                                #
                match, count, ne_output, master_util_success, master_util_failed = False, int(), str(), dict(), dict()
                while not match:
                    # if failed check 5 times
                    if count > 4:
                        break

                    # create 5s delay to re-chek CPU
                    if count > 0:
                        time.sleep(10)

                    payload = {
                        'ne_ip': serialized_data['device_id'],
                    }

                    response = requests.get(url+'v1/check_cpu/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = 'An error has occured while retrieving CPU utilization info from PE'
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        output_keys = output['output'].keys()
                        for key in output_keys:
                            if re.findall('main', key, re.IGNORECASE):
                                if int(output['output'][key]['memory_utilization']) < 70 and int(output['output'][key]['idle']) > 10:
                                    match = True
                                    master_util_success[key] = {
                                        'memory_utilization':output['output'][key]['memory_utilization'],
                                        'idle':output['output'][key]['idle']
                                    }
                                else:
                                    match = False
                                    master_util_failed[key] = {
                                        'memory_utilization':output['output'][key]['memory_utilization'],
                                        'idle':output['output'][key]['idle']
                                    }

                        # increase counter if does not match threshold
                        if not match:
                            count += 1
                        ne_output += output['ne_response']		# accumulate ne output in a var

                if not match:
                    output_message = str()
                    for key in master_util_failed:
                        if output_message == '':
                            output_message = "CPU utilization is outside threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
                        else:
                            output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
                    log_text(filename=log_file, content=output_message, ne_output=ne_output)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':output_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = output_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = str()
                    for key in master_util_success:
                        if output_message == '':
                            output_message = "CPU utilization is within threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(master_util_success[key]['memory_utilization']) + ' percent' , str(master_util_success[key]['idle']) + ' percent')
                        else:
                            output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(master_util_success[key]['memory_utilization'])+' percent', str(master_util_success[key]['idle'])+' percent')
                    log_text(filename=log_file, content=output_message, ne_output=ne_output)

                #                                                #
                #                 PUSH PE CONFIG                 #
                #                                                #
                payload = {
                    'ne_ip': serialized_data['device_id'],
                    'filename': config_file
                }

                response = requests.post(url+'v1/load_config_modify/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while loading the generated configurations to PE'
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] != 'Successfully loaded configurations at PE':
                        error_message = 'Configurations loading process is unsuccessful'
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])

                #                                               #
                #               PING IP (CE WAN)                #
                #                                               #

                if ping_ip_ce_wan == "success":
                    payload = {
                        'ne_ip': serialized_data['device_id'],
                        'destination_ip': serialized_data['ce_wan_ip'],
                        'source_ip': serialized_data['pe_wan_ip']
                    }

                    response = requests.get(url+'v2/ping_ip/', params=payload, verify=False)
                    output = response.json()

                    if output['status'] == 'failed':
                        error_message = 'An error has occurred while retrieving ping ip ce wan ({}) from PE'.format(serialized_data['ce_wan_ip'])
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message)
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = str(output['output'])
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        if output['output']['result'] == 'ping is failed':
                            output_message = 'Ping IP CE Wan ({}) is failed'.format(serialized_data['ce_wan_ip'])       
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':output_message,
                                'api_output':'Ping IP CE WAN ({}) with source IP ({}) is failed'.format(serialized_data['ce_wan_ip'], serialized_data['pe_wan_ip']),
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': config_file,
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = 'Ping IP CE WAN ({}) with source IP ({}) is failed'.format(serialized_data['ce_wan_ip'], serialized_data['pe_wan_ip'])
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            ping_ip_ce_wan = 'success'
                            output_message = 'Ping IP CE Wan ({}) is success'.format(serialized_data['ce_wan_ip'])
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])

                #                                               #
                #               PING IP (CE LAN)                #
                #                                               #

                if serialized_data['ce_lan_ip'] is not None:

                    if serialized_data['ce_lan_ip'] != '':

                        if ping_ip_ce_wan == "success" and ping_ip_ce_lan == "success":
                            networkip = ipaddress.IPv4Address(serialized_data['ce_lan_ip'])
                            networkip = networkip + 1

                            payload = {
                                'ne_ip': serialized_data['device_id'],
                                'destination_ip': networkip,
                                'source_ip': serialized_data['pe_wan_ip']
                            }

                            response = requests.get(url+'v2/ping_ip/', params=payload, verify=False)
                            output = response.json()

                            if output['status'] == 'failed':
                                error_message = 'An error has occurred while retrieving ping ip ce lan ({}) from PE'.format(networkip)
                                output_message = error_message + '\n[error message: {}]'.format(output['output'])
                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':error_message,
                                    'api_output':str(output['output']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = error_message
                                api_response['data'] = str(output['output'])
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                            else:
                                if output['output']['result'] == 'ping is failed':
                                    output_message = 'Ping IP CE LAN ({}) is failed'.format(networkip)       
                                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                                    DB_data = {
                                        'order_type':'BOD',
                                        'service_type':'DOME',
                                        'input_details':serialized_data,
                                        'message':output_message,
                                        'api_output':'Ping IP CE LAN ({}) with source IP ({}) is failed'.format(networkip, serialized_data['pe_wan_ip']),
                                        'status':'failed',
                                        'log_file': log_file,
                                        'config_file': config_file,
                                        'user': user
                                    }
                                    api_response['status'] = 'failed'
                                    api_response['message'] = output_message
                                    api_response['data'] = 'Ping IP CE WAN ({}) with source IP ({}) is failed'.format(networkip, serialized_data['pe_wan_ip'])
                                    log_database(data_DB=DB_data)
                                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                                else:
                                    output_message = 'Ping IP CE Lan ({}) is success'.format(networkip)
                                    log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])


                #                                                #
                #              CREATE RFC in CGATE               #
                #                                                #
                description = 'Order Number: {}, Leg ID: {}, Service ID: {}, PE loopback IP : {}, PE interface : {}, New bandwidth : {}, Old bandwidth : {}, Customer Name: {}, Start BOD: {}, End BOD: {}, Cust_Abbr: {}, Site Abbr: {}'.format(serialized_data['order_no'], serialized_data['leg_id'], serialized_data['service_id'], serialized_data['device_id'], serialized_data['interface'], serialized_data['new_bandwidth'], serialized_data['old_bandwidth'], serialized_data['cust_name'], serialized_data['bod_start'], serialized_data['bod_end'], serialized_data['cust_abbr'], serialized_data['site_abbr'])
                file_config = open('/root/home/<USER>/{}.txt'.format(config_file), 'r')
                workplan = file_config.read()
                datetime_provi = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                payload = {
                    'rfc_status': 'close',
                    'title': 'DOME BOD - {} ({})'.format(serialized_data['service_id'], serialized_data['cust_name']),
                    'description': description,
                    'impact': 'Low',
                    'work_plan': workplan,
                    'fall_back': 'Rollback',
                    'reason': 'Provisioning',
                    'start': datetime_provi,
                    'end': datetime_provi,
                    'hostname': serialized_data['node_name'],
                    'region': serialized_data['pe_region'],
                    'actual_start': datetime_provi,
                    'actual_end': datetime_provi,
                    'task_completed': 'Successful',
                    'inventory_update': 'No'
                }

                try:
                    response = requests.get('https://cgate.tm.com.my/cms/api/pisa/createRFC.php',params=payload, verify=False, timeout=40)
                    cgate_output = response.json()

                    cgate_error_message = str()

                    if cgate_output[0]['HEADER']['CREATE_STATUS']=='FAIL':
                        cgate_error_message = 'Failed to create RFC in Cgate'
                        output_message = cgate_error_message + '\n[error message: {}]'.format(cgate_output[0]['HEADER']['ERROR_MSG'])
                        log_text(filename=log_file, content=output_message)
                    else:
                        cgate_success_message = 'Successfully created RFC in Cgate. RFC ID [{}].'.format(cgate_output[0]['HEADER']['RFC_ID'])
                        log_text(filename=log_file, content=cgate_success_message)

                except Exception as error:
                    cgate_error_message = 'Failed to create RFC in Cgate'
                    output_message = cgate_error_message + '\n[error message: {}]'.format(str(error))
                    log_text(filename=log_file, content=output_message)

            #                                                #
            #                 CHECK CE TYPE                  #
            #                                                #

            if not re.findall('premium', serialized_data['l3_service_ce'], re.IGNORECASE):
                if cgate_error_message == 'Failed to create RFC in Cgate':
                    output_message = 'Successfully provisioned new bandwidth at PE only (unmanaged CE) but failed to create RFC in CGate'
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':output_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = output_message
                    api_response['data'] = cgate_error_message
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = 'Successfully provisioned new bandwidth at PE only (unmanaged CE)'
                    if cgate_error_message == 'RFC application is not required':
                        output_message = 'No change is required at PE and unmanaged CE'
                        pe_configuration = ''
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':output_message,
                        'api_output':str(output['output']),
                        'status':'success',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'success'
                    api_response['message'] = output_message
                    # api_response['data'] = 'No error'
                    api_response['data'] = pe_configuration.replace('\n', '<br>')
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_200_OK)

            #                                                #
            #                CHECK CE VENDOR                 #
            #                                                #
            if not re.findall('cisco', serialized_data['cpe_type'], re.IGNORECASE) and not re.findall('juniper', serialized_data['cpe_type'], re.IGNORECASE):
                if cgate_error_message == 'Failed to create RFC in Cgate':
                    output_message = 'Successfully provisioned new bandwidth at PE only but not managed {} CE which is not supported and failed to create RFC in CGate'.format(serialized_data['cpe_type'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':output_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = output_message
                    api_response['data'] = cgate_error_message
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = 'Successfully provisioned new bandwidth at PE only but not managed {} CE which is not supported'.format(serialized_data['cpe_type'])
                    if cgate_error_message == 'RFC application is not required':
                        output_message = 'No change is required at PE and failed to provision managed {} CE which is not supported'.format(serialized_data['cpe_type'])
                        pe_configuration = ''
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':output_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = output_message
                    api_response['data'] = output_message
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)


            #                                                #
            #     CONVERT BW OLD & NEW TO MBPS               #
            #                                                #

            # Convert New BW to Mbps
            if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
                ce_new_bandwidth = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) / 1000
            elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
                ce_new_bandwidth = int(re.sub(r'\D', '', serialized_data['new_bandwidth']))
            elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
                ce_new_bandwidth = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 1000

            # Convert Old BW to Mbps
            if re.findall('k', serialized_data['old_bandwidth'], re.IGNORECASE):
                ce_old_bandwidth = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) / 1000
            elif re.findall('m', serialized_data['old_bandwidth'], re.IGNORECASE):
                ce_old_bandwidth = int(re.sub(r'\D', '', serialized_data['old_bandwidth']))
            elif re.findall('g', serialized_data['old_bandwidth'], re.IGNORECASE):
                ce_old_bandwidth = int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) * 1000


            #                                                #
            #                   JUNIPER CE                   #
            #                                                #
            if re.findall('juniper', serialized_data['cpe_type'], re.IGNORECASE):

                url = 'https://pisa-juniper.prdin.kubix.tm/juniper/ce/'

                #                                                        #
                #                GET JUNIPER CE INTERFACE                #
                #                                                        #

                payload = {
                    'ce_ip': serialized_data['ce_wan_ip']
                }
                # response = requests.get(url+'v2/get_interface/', params=payload, verify=False)
                response = requests.get(url+'noproxy/v1/get_interface/', params=payload, verify=False)
                output = response.json()

                # expected output
                # {
                # "status": "success",
                # "output": {
                #     "name": "ge-0/0/0",
                #     "unit": [
                #     {
                #         "admin": "up",
                #         "link": "up",
                #         "local": "**************/30",
                #         "name": "0"
                #     }
                #     ]
                # },
                # "ne_response": " show interfaces terse | match ************** \r\nge-0/0/0.0              up    up   inet     **************/30\r\n\r\nTMIPVPN@CE_Branch> "
                # }

                if output['status']=='failed':
                    error_message = 'An error has occured while retrieving interface from CE'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    ce_port = output['output']['name']
                    serialized_data['ce_interface'] = '{}.{}'.format(output['output']['name'], output['output']['unit'][0]['name'])
                    output_message = 'Successfully retrieved CE Interface ({})'.format(serialized_data['ce_interface'])
                    log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

                #                                                                               #
                #                    BANDWIDTH VERIFY FOR UPGRADE ACTIVITY                      #
                #                                                                               #
                if ce_new_bandwidth > ce_old_bandwidth:

                    if ce_old_bandwidth <= 100 and ce_new_bandwidth >= 100:

                        #                                                            #
                        #                SHOW JUNIPER CE PORT                        #
                        #                                                            #

                        payload = {
                            'ce_ip': serialized_data['ce_wan_ip'],
                            'port': ce_port
                        }
                        response = requests.get(url+'noproxy/v1/show_port/', params=payload, verify=False)
                        output = response.json()

                        # {
                        # "status": "success",
                        # "output": {
                        #     "name": "ge-0/0/0",
                        #     "per-unit-scheduler": null,
                        #     "speed": "1g",
                        #     "link-mode": "full-duplex",
                        #     "gigether-options": {
                        #       "auto-negotiation": null
                        #     }
                        # },
                        # "ne_response": " show configuration interfaces ge-0/0/0 \r\nper-unit-scheduler;\r\nspeed 1g;\r\nlink-mode full-duplex;\r\ngigether-options {\r\n    auto-negotiation;\r\n}\r\nunit 0 {\r\n    description \"DOME - SYARIKAT PERUMAHAN NEGARA BERHAD (SPNB) - SVC NO : LD1048275511 / PS12345-DOMEBOD - 65M\";\r\n    bandwidth 10m;\r\n    family inet {\r\n        address ***********/30;\r\n    }\r\n    family inet6 {\r\n        address 2001:e68:4200:13:0:3::31/127;\r\n    }\r\n}\r\n\r\<EMAIL>> "
                        # }

                        if output['status']=='failed':
                            error_message = 'An error has occured while retrieving port from CE'
                            if cgate_error_message == 'Failed to create RFC in Cgate':
                                error_message = '{} .{}'.format(error_message, cgate_error_message)
                            output_message = error_message + '\n[error message: {}]'.format(output['output'])
                            log_text(filename=log_file, content=output_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':error_message,
                                'api_output':str(output['output']),
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': config_file,
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = error_message
                            api_response['data'] = output['output']
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            output_message = 'Successfully retrieved CE Port ({})'.format(ce_port)
                            ce_port_details = output['output']
                            log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

                        #                                                            #
                        #                JUNIPER CE PORT SPEED                       #
                        #                                                            #
                        if 'speed' in ce_port_details.keys() and 'gigether-options' in ce_port_details.keys():

                            if ce_port_details['speed'] == "100m":

                                output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed setting is ({}) and port setting is ({})'.format(serialized_data['cpe_type'],ce_port_details['speed'],ce_port_details['gigether-options'])

                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed setting is ({}) and port setting is ({}) and failed to create RFC in CGate'.format(serialized_data['cpe_type'],ce_port_details['speed'],ce_port_details['gigether-options'])
                                
                                elif cgate_error_message == 'RFC application is not required':
                                    output_message = 'No change is required at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed setting is ({}) and port setting is ({})'.format(serialized_data['cpe_type'], ce_port_details['speed'],ce_port_details['gigether-options'])

                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':output_message,
                                    'api_output':str(output['output']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = output_message
                                api_response['data'] = 'CE port speed setting is (100m)'
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                        elif 'speed' not in ce_port_details.keys() and 'gigether-options' in ce_port_details.keys():

                            if 'no-auto-negotiation' in ce_port_details['gigether-options'].keys():

                                output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and no CE port speed setting and port setting is ({})'.format(serialized_data['cpe_type'],ce_port_details['gigether-options'])

                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and no CE port speed setting and port setting is ({}) and failed to create RFC in CGate'.format(serialized_data['cpe_type'],ce_port_details['gigether-options'])
                                
                                elif cgate_error_message == 'RFC application is not required':
                                    output_message = 'No change is required at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and no CE port speed setting and port setting is ({})'.format(serialized_data['cpe_type'], ce_port_details['gigether-options'])

                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':output_message,
                                    'api_output':str(output['output']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = output_message
                                api_response['data'] = 'No CE Port speed setting and port setting is no-auto-negotiation'
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                        else:

                            output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed is not found and port setting is not auto-nego'.format(serialized_data['cpe_type'],ce_port_details['gigether-options'])

                            if cgate_error_message == 'Failed to create RFC in Cgate':
                                output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed is not found and port setting is not auto-nego and failed to create RFC in CGate'.format(serialized_data['cpe_type'],ce_port_details['gigether-options'])
                            
                            elif cgate_error_message == 'RFC application is not required':
                                output_message = 'No change is required at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed is not found and port setting is not auto-nego'.format(serialized_data['cpe_type'], ce_port_details['gigether-options'])

                            log_text(filename=log_file, content=output_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':output_message,
                                'api_output':str(output['output']),
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': config_file,
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = 'CE port speed is not found and port setting is not auto-nego'
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                #                                                            #
                #                GEN JUNIPER CE CONFIGURATION                #
                #                                                            #
                ce_interface_split = serialized_data['ce_interface'].split('.')
                ce_port = ce_interface_split[0]
                ce_vlan = ce_interface_split[1]
                
                ce_config_file = 'CE_DOME_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ce_wan_ip'],  date_now, time_now, user)		#create config file

                payload = {
                    # 'port': ce_port,
                    # 'vlan': ce_vlan,
                    'interface': serialized_data['ce_interface'],
                    'customer': serialized_data['cust_name'],
                    'leg_name': serialized_data['leg_id'],
                    'service_id': serialized_data['service_id'],
                    'bandwidth': serialized_data['new_bandwidth'],
                    'filename': ce_config_file
                }

                response = requests.post(url+'v2/dome/generate_config_modify_bw/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while generating CE configurations'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = output['output']
                    ce_configuration = 'Proof of Config (CE)\n' + ce_config_file + '\n' + output['configuration']
                    log_text(filename=log_file, content=output_message)

                #                                                  #
                #              PUSH JUNIPER CE CONFIG              #
                #                                                  #
                payload = {
                    'ce_ip': serialized_data['ce_wan_ip'],
                    'filename': ce_config_file
                }

                # response = requests.post(url+'v1/load_config/', data=payload, verify=False)
                response = requests.post(url+'noproxy/v1/load_config/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while loading the generated configurations to CE'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file + ',' + ce_config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] != 'Successfully loaded configurations at CE':
                        error_message = 'CE configurations loading process is unsuccessful'
                        if cgate_error_message == 'Failed to create RFC in Cgate':
                            error_message = '{} .{}'.format(error_message, cgate_error_message)
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': config_file + ',' + ce_config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        if cgate_error_message == 'Failed to create RFC in Cgate':
                            output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in CGate'
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':output_message,
                                'api_output':str(output['output']),
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': config_file + ',' + ce_config_file,
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = output_message
                            api_response['data'] = cgate_error_message
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        elif cgate_error_message == 'RFC application is not required':
                            output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
                            log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':output_message,
                                'api_output':str(output['output']),
                                'status':'success',
                                'log_file': log_file,
                                'config_file': ce_config_file,
                                'user': user
                            }
                            api_response['status'] = 'success'
                            api_response['message'] = output_message
                            # api_response['data'] = 'No error'
                            api_response['data'] = ce_configuration.replace('\n', '<br>')
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_200_OK)
                        else:
                            success_message = 'Successfully provisioned new bandwidth at PE and CE'
                            log_text(filename=log_file, content=success_message, ne_output=output['ne_response'])
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message': success_message,
                                'api_output':str(output['output']),
                                'status':'success',
                                'log_file': log_file,
                                'config_file': config_file + ',' + ce_config_file,
                                'user': user
                            }
                            api_response['status'] = 'success'
                            api_response['message'] = success_message
                            # api_response['data'] = 'No error'
                            api_response['data'] = pe_configuration.replace('\n', '<br>') + '<br><br>' + ce_configuration.replace('\n', '<br>')
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_200_OK)

            #                                                #
            #                    CISCO CE                    #
            #                                                #
            if re.findall('cisco', serialized_data['cpe_type'], re.IGNORECASE):

                url = 'https://pisa-cisco.prdin.kubix.tm/cisco/ce/'

                #                                                #
                #                GET CE INTERFACE                #
                #                                                #
                payload = {
                    'ce_ip': serialized_data['ce_wan_ip']
                }

                # response = requests.get(url+'v2/get_interface/', params=payload, verify=False)
                response = requests.get(url+'noproxy/v1/get_interface/', params=payload, verify=False)
                output = response.json()

                # expected output
                # {
                # 	"name": "GigabitEthernet0/1",
                # 	"ip-address": "**************",
                # 	"status": "up",
                # 	"protocol": "up"
                # }

                if output['status']=='failed':
                    error_message = 'An error has occured while retrieving interface from CE'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    serialized_data['ce_interface'] = output['output']['name']
                    output_message = 'Successfully retrieved CE Interface ({})'.format(serialized_data['ce_interface'])
                    log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

                #                                                                               #
                #                    BANDWIDTH VERIFY FOR UPGRADE ACTIVITY                      #
                #                                                                               #
                # if int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) > int(re.sub(r'\D', '', serialized_data['old_bandwidth'])):
                if ce_new_bandwidth > ce_old_bandwidth:

                    # if int(re.sub(r'\D', '', serialized_data['old_bandwidth'])) <= 100 and int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) > 100:
                    if ce_old_bandwidth <= 100 and ce_new_bandwidth >= 100:

                        #                                                            #
                        #                SHOW JUNIPER CE PORT                        #
                        #                                                            #

                        payload = {
                            'ce_ip': serialized_data['ce_wan_ip'],
                            'ce_interface': serialized_data['ce_interface']
                        }
                        response = requests.get(url+'noproxy/v1/show_interface/', params=payload, verify=False)
                        output = response.json()

                        # {
                        # "status": "success",
                        # "output": {
                        #     "bandwidth": "10000",
                        #     "ip-address": "***********",
                        #     "negotiation": "auto",
                        #     "name": "GigabitEthernet0/0/0"
                        # },
                        # "ne_response": "show running-config interface GigabitEthernet0/0/0\r\nBuilding configuration...\r\n\r\nCurrent configuration : 234 bytes\r\n!\r\ninterface GigabitEthernet0/0/0\r\n description \"DOME - SITDOMETS1 - SVC NO: LD1007433525 / PS1007433526 - 10M\"\r\n bandwidth 10000\r\n ip address *********** ***************\r\n negotiation auto\r\n ipv6 address 2001:E68:4200:13:0:3:0:27/127\r\nend\r\n\r\nDOME#"
                        # }

                        if output['status']=='failed':
                            error_message = 'An error has occured while retrieving port from CE'
                            if cgate_error_message == 'Failed to create RFC in Cgate':
                                error_message = '{} .{}'.format(error_message, cgate_error_message)
                            output_message = error_message + '\n[error message: {}]'.format(output['output'])
                            log_text(filename=log_file, content=output_message)
                            DB_data = {
                                'order_type':'BOD',
                                'service_type':'DOME',
                                'input_details':serialized_data,
                                'message':error_message,
                                'api_output':str(output['output']),
                                'status':'failed',
                                'log_file': log_file,
                                'config_file': config_file,
                                'user': user
                            }
                            api_response['status'] = 'failed'
                            api_response['message'] = error_message
                            api_response['data'] = output['output']
                            log_database(data_DB=DB_data)
                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                        else:
                            output_message = 'Successfully retrieved CE Interface ({})'.format(serialized_data['ce_interface'])
                            ce_port_details = output['output']
                            log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

                        if 'speed' in ce_port_details.keys():

                            if ce_port_details['speed'] == '100':

                                output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed setting is (100) '.format(serialized_data['cpe_type'])

                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed setting is (100) and failed to create RFC in CGate'.format(serialized_data['cpe_type'])
                                
                                elif cgate_error_message == 'RFC application is not required':
                                    output_message = 'No change is required at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port speed setting is (100)'.format(serialized_data['cpe_type'])

                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':output_message,
                                    'api_output':str(output['output']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = output_message
                                api_response['data'] = 'CE port speed setting is (100)'
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                        elif 'negotiation' in ce_port_details.keys():

                            if re.match('no negotiation auto', ce_port_details['negotiation']) or re.match('no negotiation', ce_port_details['negotiation']):

                                output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port setting is (no negotiation auto)'.format(serialized_data['cpe_type'])

                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port setting is (no negotiation auto) and failed to create RFC in CGate'.format(serialized_data['cpe_type'])
                                
                                elif cgate_error_message == 'RFC application is not required':
                                    output_message = 'No change is required at PE and failed to provision managed ({}) CE which is new bandwidth is equal to or more than 100M and CE port setting is (no negotiation auto)'.format(serialized_data['cpe_type'])

                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':output_message,
                                    'api_output':str(output['output']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = output_message
                                api_response['data'] = 'CE port setting is (no negotiation auto)'
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                        # output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed {} CE which is old bandwidth {} is less than or equal to 100m and new bandwidth {} is equal to or more than 100m'.format(serialized_data['cpe_type'], serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])                        
                        # if cgate_error_message == 'Failed to create RFC in Cgate':
                        #     output_message = 'Successfully provisioned new bandwidth at PE and failed to provision managed {} CE which is old bandwidth {} is less than or equal to 100m and new bandwidth {} is equal to or more than 100m and failed to create RFC in CGate'.format(serialized_data['cpe_type'], serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
                        # elif cgate_error_message == 'RFC application is not required':
                        #     output_message = 'No change is required at PE and failed to provision managed {} CE which is old bandwidth {} is less than or equal to 100m and new bandwidth {} is equal to or more than 100m'.format(serialized_data['cpe_type'], serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])

                        # log_text(filename=log_file, content=output_message)
                        # DB_data = {
                        #     'order_type':'BOD',
                        #     'service_type':'DOME',
                        #     'input_details':serialized_data,
                        #     'message':output_message,
                        #     'api_output':str(output['output']),
                        #     'status':'failed',
                        #     'log_file': log_file,
                        #     'config_file': config_file,
                        #     'user': user
                        # }
                        # api_response['status'] = 'failed'
                        # api_response['message'] = output_message
                        # api_response['data'] = output_message
                        # log_database(data_DB=DB_data)
                        # return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                #                                                  #
                #              GEN CE CONFIGURATIONS               #
                #                                                  #
                ce_config_file = 'CE_DOME_BOD_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ce_wan_ip'],  date_now, time_now, user)		#create config file

                payload = {
                    'interface': serialized_data['ce_interface'],
                    'customer': serialized_data['cust_name'],
                    'leg_name': serialized_data['leg_id'],
                    'service_id': serialized_data['service_id'],
                    'bandwidth': serialized_data['new_bandwidth'],
                    'filename': ce_config_file,
                }

                response = requests.post(url+'v2/dome/generate_config_modify_bw/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while generating CE configurations'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    output_message = output['output']
                    ce_configuration = 'Proof of Config (CE)\n' + ce_config_file + '\n' + output['configuration']
                    log_text(filename=log_file, content=output_message)

                #                                          #
                #              PUSH CISCO CE CONFIG        #
                #                                          #
                payload = {
                    'ce_ip': serialized_data['ce_wan_ip'],
                    'filename': ce_config_file
                }

                # response = requests.post(url+'v1/load_config/', data=payload, verify=False)
                response = requests.post(url+'noproxy/v1/load_config/', data=payload, verify=False)
                output = response.json()

                if output['status'] == 'failed':
                    error_message = 'An error has occured while loading the generated configurations to CE'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file + ',' + ce_config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] != 'Successfully loaded configurations at CE':
                        error_message = 'CE configurations loading process is unsuccessful'
                        if cgate_error_message == 'Failed to create RFC in Cgate':
                            error_message = '{} .{}'.format(error_message, cgate_error_message)
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': config_file + ',' + ce_config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        ce_output_message = 'Successfully provisioned new bandwidth at CE'
                        log_text(filename=log_file, content=ce_output_message, ne_output=output['ne_response'])

                #                                                  #
                #               CHECK CE SOFTWARE                  #
                #                                                  #
                payload = {
                    'ce_ip': serialized_data['ce_wan_ip']
                }
                # response = requests.get(url+'v2/show_version/', params=payload, verify=False)
                response = requests.get(url+'noproxy/v1/show_version', params=payload, verify=False)
                output = response.json()

                # "output": {
                #     "software": "Cisco IOS XE Software",
                #     "model": "ISR4221/K9"
                # },

                if output['status'] == 'failed':
                    error_message = 'An error has occured while checking CE software details'
                    if cgate_error_message == 'Failed to create RFC in Cgate':
                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                    log_text(filename=log_file, content=output_message)
                    DB_data = {
                        'order_type':'BOD',
                        'service_type':'DOME',
                        'input_details':serialized_data,
                        'message':error_message,
                        'api_output':str(output['output']),
                        'status':'failed',
                        'log_file': log_file,
                        'config_file': config_file + ',' + ce_config_file,
                        'user': user
                    }
                    api_response['status'] = 'failed'
                    api_response['message'] = error_message
                    api_response['data'] = output['output']
                    log_database(data_DB=DB_data)
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                else:
                    if output['output'] == 'CE software/model version is not found':
                        error_message = output['output']
                        if cgate_error_message == 'Failed to create RFC in Cgate':
                            error_message = '{} .{}'.format(error_message, cgate_error_message)
                        output_message = error_message + '\n[error message: {}]'.format(output['output'])
                        log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
                        DB_data = {
                            'order_type':'BOD',
                            'service_type':'DOME',
                            'input_details':serialized_data,
                            'message':error_message,
                            'api_output':str(output['output']),
                            'status':'failed',
                            'log_file': log_file,
                            'config_file': config_file + ',' + ce_config_file,
                            'user': user
                        }
                        api_response['status'] = 'failed'
                        api_response['message'] = error_message
                        api_response['data'] = output['output']
                        log_database(data_DB=DB_data)
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    else:
                        output_message = "Successfully retrieved CE software/model version"
                        log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))
                        
                        if re.search('xe', output['output']['software'], re.IGNORECASE) or re.search('xr', output['output']['software'], re.IGNORECASE):
                            #                                                   #
                            #               CHECK CE THROUGHPUT                 #
                            #                                                   #
                            payload = {
                                'ce_ip': serialized_data['ce_wan_ip'],
                            }
                            # response = requests.get(url+'v2/get_throughput/', params=payload, verify=False)
                            response = requests.get(url+'noproxy/v1/get_throughput/', params=payload, verify=False)
                            output = response.json()
                        
                            if output['status'] == 'failed':
                                error_message = 'An error has occured while checking CE throughput'
                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    error_message = '{} .{}'.format(error_message, cgate_error_message)
                                output_message = error_message + '\n[error message: {}]'.format(output['output'])
                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':error_message,
                                    'api_output':str(output['output']),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file + ',' + ce_config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = error_message
                                api_response['data'] = output['output']
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                            else:
                                if output['output'] == 'CE throughput level is unthrottled' or output['output'] == 'No CE throughput level is found':
                                    error_message = 'CE throughput level is not configured'
                                    if cgate_error_message == 'Failed to create RFC in Cgate':
                                        error_message = '{} .{}'.format(error_message, cgate_error_message)
                                    output_message = error_message + '\n[error message: {}]'.format(output['output'])
                                    log_text(filename=log_file, content=output_message)
                                    DB_data = {
                                        'order_type':'BOD',
                                        'service_type':'DOME',
                                        'input_details':serialized_data,
                                        'message':error_message,
                                        'api_output':str(output['output']),
                                        'status':'failed',
                                        'log_file': log_file,
                                        'config_file': config_file + ',' + ce_config_file,
                                        'user': user
                                    }
                                    api_response['status'] = 'failed'
                                    api_response['message'] = error_message
                                    api_response['data'] = output['output']
                                    log_database(data_DB=DB_data)
                                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                                else:
                                    output_message = "Successfully retrieved CE Throughput"
                                    log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))                                    
                                    #                                           #
                                    #              GEN THROUGHPUT               #
                                    #                                           #
                                    if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
                                        serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2
                                    elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
                                        serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2000
                                    elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
                                        serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2000000
                                    else:
                                        output_message = 'New bandwidth ({}) does not contain a valid unit'.format(serialized_data['new_bandwidth'])
                                        if cgate_error_message == 'Failed to create RFC in Cgate':
                                            output_message = '{} .{}'.format(output_message, cgate_error_message)
                                        log_text(filename=log_file, content=output_message)
                                        DB_data = {
                                            'order_type':'BOD',
                                            'service_type':'DOME',
                                            'input_details':serialized_data,
                                            'message':output_message,
                                            'api_output':serialized_data['new_bandwidth'],
                                            'status':'failed',
                                            'log_file': log_file,
                                            'config_file': config_file + ',' + ce_config_file,
                                            'user': user
                                        }
                                        api_response['status'] = 'failed'
                                        api_response['message'] = output_message
                                        api_response['data'] = serialized_data['new_bandwidth']
                                        log_database(data_DB=DB_data)
                                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                                    if serialized_data['ce_throughput'] > int(output['output']['throughput']):
                                        error_message = 'The new bandwidth {} (times 2) is more than allowable CE throughput level {}'.format(serialized_data['new_bandwidth'].lower(), str(output['output']['throughput'])+'k')
                                        if cgate_error_message == 'Failed to create RFC in Cgate':
                                            error_message = '{} .{}'.format(error_message, cgate_error_message)
                                        log_text(filename=log_file, content=error_message)
                                        DB_data = {
                                            'order_type':'BOD',
                                            'service_type':'DOME',
                                            'input_details':serialized_data,
                                            'message':error_message,
                                            'api_output': 'CE througput level is {}'.format(str(output['output']['throughput'])+'k'),
                                            'status':'failed',
                                            'log_file': log_file,
                                            'config_file': config_file + ',' + ce_config_file,
                                            'user': user
                                        }
                                        api_response['status'] = 'failed'
                                        api_response['message'] = error_message
                                        api_response['data'] = 'CE througput level is {}'.format(str(output['output']['throughput'])+'k')
                                        log_database(data_DB=DB_data)
                                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                                    else:
                                        output_message = 'The new bandwidth {} (times 2) is lesser than allowable CE throughput level {}'.format(serialized_data['new_bandwidth'].lower(), str(output['output']['throughput'])+'k')
                                        log_text(filename=log_file, content=output_message)

                                        # If RFC creation is failed 
                                        if cgate_error_message == 'Failed to create RFC in Cgate':
                                            output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
                                            log_text(filename=log_file, content=output_message)
                                            DB_data = {
                                                'order_type':'BOD',
                                                'service_type':'DOME',
                                                'input_details':serialized_data,
                                                'message':output_message,
                                                'api_output':cgate_error_message,
                                                'status':'failed',
                                                'log_file': log_file,
                                                'config_file': config_file,
                                                'user': user
                                            }
                                            api_response['status'] = 'failed'
                                            api_response['message'] = output_message
                                            api_response['data'] = cgate_error_message
                                            log_database(data_DB=DB_data)
                                            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                                        # If no change is required at PE
                                        elif cgate_error_message == 'RFC application is not required':
                                            output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
                                            db_config_file = ce_config_file
                                            data_config = ce_configuration

                                        else:
                                            output_message = 'Successfully provisioned new bandwidth at PE and CE'
                                            db_config_file = config_file + ',' + ce_config_file
                                            data_config = pe_configuration.replace('\n', '<br>') + '\n\n' + ce_configuration.replace('\n', '<br>')
                                        
                                        log_text(filename=log_file, content=output_message)
                                        DB_data = {
                                            'order_type':'BOD',
                                            'service_type':'DOME',
                                            'input_details':serialized_data,
                                            'message': output_message,
                                            # 'api_output':str(output['output']),
                                            'api_output':output_message,
                                            'status':'success',
                                            'log_file': log_file,
                                            'config_file': db_config_file,
                                            'user': user
                                        }
                                        api_response['status'] = 'success'
                                        api_response['message'] = output_message
                                        # api_response['data'] = 'No error'
                                        api_response['data'] = data_config.replace('\n', '<br>')
                                        log_database(data_DB=DB_data)
                                        return Response(api_response,status=status.HTTP_200_OK)

                        # Verify CE Version
                        else:
                            ce_version_bandwidth = [
                                # {'version':'4461','bandwidth':1500},
                                # {'version':'4451','bandwidth':1000},
                                # {'version':'4431','bandwidth':500},
                                # {'version':'4351','bandwidth':200},
                                # {'version':'4331','bandwidth':100},
                                # {'version':'4321','bandwidth':50},
                                # {'version':'3945E','bandwidth':350},
                                # {'version':'3925E','bandwidth':250},
                                # {'version':'3945','bandwidth':150},
                                # {'version':'3925','bandwidth':100},
                                # {'version':'2951','bandwidth':75},
                                # {'version':'2921','bandwidth':50},
                                # {'version':'2911','bandwidth':35},
                                # {'version':'2901','bandwidth':25},
                                # {'version':'1941','bandwidth':25},
                                # {'version':'1921','bandwidth':15},
                                # {'version':'890','bandwidth':15},
                                # {'version':'880','bandwidth':8},
                                # {'version':'860','bandwidth':4},
                                {'version':'3945E','bandwidth':350},
                                {'version':'3925E','bandwidth':250},
                                {'version':'3945','bandwidth':150},
                                {'version':'3925','bandwidth':100},
                                {'version':'2951','bandwidth':75},
                                {'version':'2921','bandwidth':50},
                                {'version':'2911','bandwidth':35},
                                {'version':'2901','bandwidth':25},
                                {'version':'1941','bandwidth':9},
                                {'version':'1921','bandwidth':7},
                                {'version':'890','bandwidth':25},
                                {'version':'887VAG+7-K9','bandwidth':21},
                                {'version':'887VA-K9','bandwidth':15},
                                {'version':'880','bandwidth':15},
                                {'version':'860VAE','bandwidth':8},
                                {'version':'860','bandwidth':4},
                                {'version':'819G+7-K9','bandwidth':8},
                            ]

                            ce_version_bw = str()
                            for x in ce_version_bandwidth:
                                if x['version'] in output['output']['model']:
                                    ce_version_bw = x['bandwidth']			# determine allowed bandwidth
                                    break

                            # if ce_version_bw == '':
                            #     error_message = 'The CE model ({}) version is not found in the saved list'.format(output['output']['model'])
                            #     if cgate_error_message == 'Failed to create RFC in Cgate':
                            #         error_message = '{} .{}'.format(error_message, cgate_error_message)
                            #     log_text(filename=log_file, content=error_message)
                            #     DB_data = {
                            #         'order_type':'BOD',
                            #         'service_type':'DOME',
                            #         'input_details':serialized_data,
                            #         'message':error_message,
                            #         'api_output':str(output['output']),
                            #         'status':'failed',
                            #         'log_file': log_file,
                            #         'config_file': config_file + ',' + ce_config_file,
                            #         'user': user
                            #     }
                            #     api_response['status'] = 'failed'
                            #     api_response['message'] = error_message
                            #     api_response['data'] = output['output']
                            #     log_database(data_DB=DB_data)
                            #     return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                            # else:
                            #     output_message = 'The CE model ({}) version is found in the saved list'.format(output['output']['model'])
                            #     log_text(filename=log_file, content=output_message)

                            if int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 > ce_version_bw: # check if new BW X 2 more than allowed bandwidth
                                error_message = 'The new bandwidth {} (times 2) is more than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    error_message = '{} .{}'.format(error_message, cgate_error_message)
                                log_text(filename=log_file, content=error_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':error_message,
                                    'api_output': 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m'),
                                    'status':'failed',
                                    'log_file': log_file,
                                    'config_file': config_file + ',' + ce_config_file,
                                    'user': user
                                }
                                api_response['status'] = 'failed'
                                api_response['message'] = error_message
                                api_response['data'] = 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m')
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                            else:

                                if ce_version_bw and int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 < ce_version_bw:
                                    output_message = 'The new bandwidth {} (times 2) is lesser than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
                                else:
                                    output_message = 'The CE model ({}) is not exist in CE version list'.format(output['output']['model'])

                                log_text(filename=log_file, content=output_message)

                                if cgate_error_message == 'Failed to create RFC in Cgate':
                                    output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
                                    
                                    log_text(filename=log_file, content=output_message)
                                    DB_data = {
                                        'order_type':'BOD',
                                        'service_type':'DOME',
                                        'input_details':serialized_data,
                                        'message':output_message,
                                        'api_output':cgate_error_message,
                                        'status':'failed',
                                        'log_file': log_file,
                                        'config_file': config_file + ',' + ce_config_file,
                                        'user': user
                                    }
                                    api_response['status'] = 'failed'
                                    api_response['message'] = output_message
                                    api_response['data'] = cgate_error_message
                                    log_database(data_DB=DB_data)
                                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

                                elif cgate_error_message == 'RFC application is not required':
                                    output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
                                    db_config_file = ce_config_file
                                    data_config = ce_configuration

                                else:
                                    output_message = 'Successfully provisioned new bandwidth at PE and CE'
                                    db_config_file = config_file + ',' + ce_config_file
                                    data_config = pe_configuration.replace('\n', '<br>') + '\n\n' + ce_configuration.replace('\n', '<br>')

                                log_text(filename=log_file, content=output_message)
                                DB_data = {
                                    'order_type':'BOD',
                                    'service_type':'DOME',
                                    'input_details':serialized_data,
                                    'message':output_message,
                                    'api_output':output_message,
                                    'status':'success',
                                    'log_file': log_file,
                                    'config_file': db_config_file,
                                    'user': user
                                }
                                api_response['status'] = 'success'
                                api_response['message'] = output_message
                                # api_response['data'] = output_message
                                api_response['data'] = data_config.replace('\n', '<br>')
                                log_database(data_DB=DB_data)
                                return Response(api_response,status=status.HTTP_200_OK)

        # except error
        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + '\n[error message: {}]'.format(error)
            log_text(filename=log_file, content=output_message)
            DB_data = {
                'order_type':'BOD',
                'service_type':'DOME',
                'input_details':request.data, #request.data['RequestPISA']['Request'],
                'message':error_message,
                'api_output':str(error),
                'status':'failed',
                'log_file': log_file,
                'config_file': 'Undefined',
                'user': user
            }
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            # api_response['data'] = str(error)
            api_response['data'] = output_error
            log_database(data_DB=DB_data)
            return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
