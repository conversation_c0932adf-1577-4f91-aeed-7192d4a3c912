from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.serializers import verify5GCsrConnectivitySerializer
import requests, re, sys, os
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from api.libs import log_text, log_database
from json import dumps
from concurrent.futures import ThreadPoolExecutor


class API(APIView):
	
	authentication_classes = ()  # exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		request_body=verify5GCsrConnectivitySerializer,
		responses={201: verify5GCsrConnectivitySerializer()},
		operation_summary="Workflow for 5G Bot to verify CSR connectivity"
	)
	
	def post(self, request):

		try:
            # Get user Id from Token
			token = request.META.get('HTTP_AUTHORIZATION', '').replace('Token ', '')
			user_id = Token.objects.get(key=token).user_id if token else None
			user = User.objects.get(id=user_id).username if user_id else request.data['staff_id']

			#                                           #
			#              VERIFY API INPUT             #
			#                                           #
			input_data = verify5GCsrConnectivitySerializer(data=request.data)
			input_data.is_valid()

			# initiate variable
			api_response = dict()

			if len(input_data.errors) > 0:  # check if passed data contains error and return error
				output_message = 'Incomplete/invalid information fetched by API'
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': request.data,
					'message': output_message,
					'api_output': str(input_data.errors),
					'status': 'failed',
					'log_file': 'N/A',
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = input_data.errors
				api_response['log_file'] = ''
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

			# serialized_data = input_data.data[]   # assign a variable with serialized data
			serialized_data = input_data.validated_data
			date_now = datetime.now().strftime('%d-%m-%Y')
			time_now = datetime.now().strftime('%H:%M:%S')
			log_file = f"5G_verifyCsrConnectivity_{serialized_data['csr_info'].upper()}_{date_now}@{time_now}_by_{user}.txt"		# Create Log File

			# Log created time
			output_message = "File created at {} {} for 5G Existing CSR Verification Bot Log".format(date_now, time_now)
			log_text(filename=log_file, content=output_message)

			output_message = 'Information fetched by API contains no error'
			log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

			# Get filtered CSR
			response = requests.get(
				'http://localhost:8000/workflow/database/v1/csr/', 
				params={ 
					'search': serialized_data['csr_info'],
					'limit': 1000	# limit to display in a single page of API
				}, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = f"An error has occured while retrieving CSR list using searched keyword {serialized_data['csr_info']}"
				output_message = error_message + \
					'\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': error_message,
					'api_output': str(output['output']),
					'status': 'failed',
					'log_file': log_file,
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
			
			else:
				# if there is no matching CSR
				if output['total'] < 1:
					output_message = f"There is no matching CSR info"
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type': 'Verification',
						'service_type': '5G',
						'input_details': serialized_data,
						'message': output_message,
						'api_output': str(output),
						'status': 'failed',
						'log_file': log_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output
					log_database(data_DB=DB_data)
					return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
				
				output_message = 'Successfully retrieved CSR list'		
				log_text(filename=log_file, content=output_message, ne_output=dumps(output, indent=4, separators=(',', ': ')))
					
				# if csr list more than 10, break into smaller dictionaries
				csr_chunks = [output['csr'][i:i + 10] for i in range(0, len(output['csr']), 10)]

				# convert to a list
				# else:
				# 	csr_chunks = [output['csr']]

				# create a thread pool executor with n worker threads
				executor = ThreadPoolExecutor(max_workers=len(csr_chunks))

				# submit the requests to the executor and get the results
				results = list(executor.map(self.verifyCsrConnectivity, csr_chunks))

				# Merge the results if needed
				# Assuming your verifyCsrConnectivity function returns a list of dictionaries for each chunk
				merged_results = [item for sublist in results for item in sublist]

				final_results = {
					'exchange_id': serialized_data['csr_info'],
					'summary': {
						'success_rate': f"{int(output['total'])-len(merged_results)}/{output['total']}",
        				'success_rate_percent': "{:.2f}%".format((int(output['total']) - len(merged_results)) / output['total'] * 100),
						'total_csr_down': len(merged_results),
						'details_csr_down': merged_results
					}
				}
				
				output_message = 'Successfully tested connectivity of all filtered CSRs'

				log_text(filename=log_file, content=output_message, ne_output=dumps(final_results, indent=4, separators=(',', ': ')))
				DB_data = {
					'order_type': 'Verification',
					'service_type': '5G',
					'input_details': serialized_data,
					'message': output_message,
					'api_output': str(final_results)[:4950],
					'status': 'success',
					'log_file': log_file,
					'user': user
				}
				api_response['status'] = 'success'
				api_response['message'] = output_message
				api_response['data'] = final_results
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_200_OK)
			

		except Exception as error:
			output_error = list()
			exc_type, exc_obj, exc_tb = sys.exc_info()
			output_error.append(str(exc_type))
			output_error.append(str(exc_obj))
			output_error.append(str(exc_tb.tb_lineno))
			error_message = 'Unexpected error has occured while executing API'
			output_message = error_message + \
				'\n[error message: {}]'.format(error)
			log_text(filename=log_file, content=output_message)
			DB_data = {
				'order_type': 'Verification',
				'service_type': '5G',
				'input_details': request.data,
				'message': error_message,
				'api_output': str(error),
				'status': 'failed',
				'log_file': log_file,
				'config_file': 'Undefined',
				'user': user
			}
			api_response['status'] = 'failed'
			api_response['message'] = error_message
			# api_response['data'] = str(error)
			api_response['data'] = output_error
			log_database(data_DB=DB_data)
			return Response(api_response, status=status.HTTP_400_BAD_REQUEST)
		

	# Verify CSR Connectivity
	def verifyCsrConnectivity(self, csr_chunk):
		results = []
		for item in csr_chunk:
			response = requests.get('http://localhost:8000/workflow/v1/telnet_ip_port', params={'ip': item['ip'], 'port': 22}, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				item['connectivity_testing'] = output['output']
				results.append(item)

		return results
				
        

