from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
# from api.serializers import BGPUptimeStatusSerializer
import requests, json, time, re, os, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps


class API(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    # @swagger_auto_schema(
    #     request_body=BGPUptimeStatusSerializer,
    #     responses={201: BGPUptimeStatusSerializer()},
    # )

    def post(self, request):
        try:
            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            # initiate variable
            api_response = dict()

            # Initialize variables
            page = 1
            last_page = 1  # Initialize last_page
            total_inserted = 0

            # Process one page at a time
            while page <= last_page:
                # Get circuits for current page
                url_1 = f'http://pisa-self-serve-svc:8000/api/self-serve/get_list_circuits?page={str(page)}'
                headers = {'Authorization': f'Bearer 25|wlARXXwChYkuiL6fOAlHX92hVxx8321PNUEMVTrx'}
                response_1 = requests.get(url_1, headers=headers, verify=False)
                data_1 = response_1.json()

                if data_1['status'] == 'success':
                    last_page = data_1['last_page']  # Update last_page from API response
                    current_page_circuits = data_1['circuits']

                    # Ensure 'PeDeviceIp' key exists in the response data
                    if 'PeDeviceIp' in data_1:
                        ne_ip = data_1['PeDeviceIp']
                    else:
                        return Response({'status': 'failed', 'message': 'PeDeviceIp not found in response data'}, 
                                    status=status.HTTP_400_BAD_REQUEST)

                    # Get BGP information
                    command = 'show bgp summary'
                    url_2 = f'http://pisa-juniper-svc/juniper/pe/v1/show_config/?ne_ip={ne_ip}&command={command}'
                    response_2 = requests.get(url_2, verify=False)
                    data_2 = response_2.json()

                    if 'ne_response' not in data_2:
                        return Response({'status': 'failed', 'message': 'ne_response not found in response data'}, 
                                    status=status.HTTP_400_BAD_REQUEST)

                    ne_response = data_2['ne_response']
                    formatted_circuits = []

                    # Process circuits for current page
                    for circuit in current_page_circuits:
                        ce_wan_ip = circuit.get('CeIp')
                        if ce_wan_ip and isinstance(ce_wan_ip, str):
                            bgp_status = 'Others'
                            bgp_uptime = 'Others'
                            for line in ne_response.split('\n'):
                                if ce_wan_ip in line:
                                    parts = line.split()
                                    if len(parts) >= 7:
                                        bgp_uptime = parts[-3] + ' ' + parts[-2]
                                        bgp_status = parts[-1]
                                        break
                            
                            formatted_circuit = {
                                'CustAbbr': circuit.get('CustAbbr', ''),
                                'ServiceType': circuit.get('ServiceType', ''),
                                'Product': circuit.get('Product', ''),
                                'LegId': circuit.get('RedundancyId', ''),
                                'ServiceId': circuit.get('ServiceId', ''),
                                'PeNode': circuit.get('Pe', ''),
                                'PeDeviceIp': circuit.get('PeDeviceId', ''),
                                'CeWanIp': ce_wan_ip,
                                'BGPStatus': bgp_status,
                                'BGPUptime': bgp_uptime
                            }
                            formatted_circuits.append(formatted_circuit)

                    # Insert data for current page
                    insert_payload = {
                        'pe_node': data_1.get('pe', 'unknown_pe_node'),
                        'circuits': formatted_circuits
                    }

                    # Call API to insert data
                    url_insert = 'http://pisa-self-serve-svc:8000/api/self-serve/insert_bgp_uptime_status'
                    insert_headers = {
                        'Content-Type': 'application/json',
                        'Authorization': f'Bearer 25|wlARXXwChYkuiL6fOAlHX92hVxx8321PNUEMVTrx'
                    }

                    insert_response = requests.post(
                        url_insert, 
                        json=insert_payload,  # Changed from data to json
                        headers=insert_headers, 
                        verify=False
                    )
                    
                    insert_data = insert_response.json()

                    if insert_data.get('status') != 'success':
                        return Response({
                            'status': 'failed', 
                            'message': f'Failed to insert BGP uptime status for page {page}',
                            'detail': insert_data
                        }, status=status.HTTP_400_BAD_REQUEST)

                    total_inserted += len(formatted_circuits)
                    print(f"Successfully processed page {page} of {last_page}")
                    page += 1
                else:
                    return Response({'status': 'failed', 'message': f'Failed to get circuits list for page {page}'}, 
                                status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'status': 'success', 
                'message': 'Successfully processed all pages',
                'total_pages_processed': last_page,
                'total_circuits_inserted': total_inserted
            }, status=status.HTTP_200_OK)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occurred while executing API'
            output_message = error_message + '\n[error message: {}]'.format(error)
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            api_response['data'] = output_error
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)