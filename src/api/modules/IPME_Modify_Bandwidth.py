from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import IpmeModBwSerializer
import requests, json, time, re, os, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps


class API(APIView):

	# authentication_classes = ()     #exclude from global authentication
	# permission_classes = ()

	@swagger_auto_schema(
		request_body=IpmeModBwSerializer,
		responses={201: IpmeModBwSerializer()},
	)

	def post(self, request):

		try:

			# GET USER ID
			token = request.META.get('HTTP_AUTHORIZATION')
			if token:
				user_id = Token.objects.get(key=token.replace('Token ','')).user_id
				user = User.objects.get(id=user_id).username
			else:
				user = 'not_available'

			#                                           #
			#              VERIFY API INPUT             #
			#                                           #
			input_data = IpmeModBwSerializer(data=request.data)
			input_data.is_valid()

			# initiate variable
			api_response = dict()
			proof_config = str()

			if len(input_data.errors) > 0:  # check if passed data contains error and return error
				output_message = 'Incomplete/invalid information fetched by API'
				DB_data = {
					'order_type':'Modify Bandwidth',
					'service_type':'IPVPN',
					'input_details':request.data,
					'message':output_message,
					'api_output':str(input_data.errors),
					'status':'failed',
					'log_file':'N/A',
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = input_data.errors
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			# serialized_data = input_data.data[]   # assign a variable with serialized data
			serialized_data = input_data.validated_data
			date_now = datetime.now().strftime('%d-%m-%Y')
			time_now = datetime.now().strftime('%H:%M:%S')
			log_file = 'IPME_Modify_BW_{}_{}_{}@{}_by_{}.txt'.format(serialized_data['service_id'], serialized_data['node_name'], date_now, time_now, user)		# Create Log File


			# LOG CREATED TIME #
			output_message = "File created at {} {}".format(date_now, time_now) 
			log_text(filename=log_file, content=output_message)

			output_message = 'Information fetched by API contains no error'
			log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

			#                                                #
			#              CHECK PRODUCT NAME                #
			#                                                #
			if not 'IPVPN Service (Metro Ethernet)' in serialized_data['product_name']:
				output_message = 'Invalid product name ({}). Only product IPVPN Service (Metro Ethernet) is allowed'.format(serialized_data['product_name'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'Modify Bandwidth',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':output_message,
					'api_output':"Product name received by PISA is '{}'".format(serialized_data['product_name']),
					'status':'failed',
					'log_file':log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = "Product name received by PISA is '{}'".format(serialized_data['product_name'])
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                      #
			#        GET INTERFACE DETAILS         #
			#                                      #

			# sample API output
			# "output": {
			# 	"name": "xe-7/3/0",
			# 	"unit": [
			# 		{
			# 			"name": 1020,
			# 			"description": "IPVPN - DORADO - SVC NO:PS3000162419",
			# 			"bandwidth": "8m",
			# 			"vlan-id": 1020,
			# 			"family": {
			# 				"inet": {
			# 					"policer": {
			# 						"input": "POLICER_8M_10GPIC"
			# 					},
			# 					"address": [
			# 					{
			# 						"name": "**************/30"
			# 					}
			# 					]
			# 				}
			# 			}
			# 		}
			# 	]
			# }

			url = 'https://pisa-juniper.prdin.kubix.tm/juniper/pe/'	# declare main url variable

			payload = {
				'ne_ip': serialized_data['device_id'],
				'interface': serialized_data['interface']
			}
			response = requests.get(url+'v2/show_interface/', params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while retrieving interface ({}) details'.format(serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'Modify Bandwidth',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				if output['output'] == 'interface is not exist':
					error_message = 'Interface ({}) is not exist in PE ({})'.format(serialized_data['interface'], serialized_data['node_name'])
					log_text(filename=log_file, content=error_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					interface_details = output['output']
					output_message = 'Sucessfully retrieved interface ({}) details'.format(serialized_data['interface'])
					log_text(filename=log_file, content=output_message, ne_output=dumps(interface_details, indent=4, separators=(',', ': ')))

			#                                                #
			#        CHECK PE IP AND INTERFACE MATCH         #
			#                                                #

			pe_wan_ip_subnet = interface_details['unit']['family']['inet']['address']['name']
			pe_wan_ip = pe_wan_ip_subnet.split('/')[0]

			if pe_wan_ip == serialized_data['pe_wan_ip']:
				output_message = 'PE IP Address ({}) match with Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
				log_text(filename=log_file, content=output_message)
			else:
				error_message = 'Mismatched PE IP Address ({}) and Interface ({})'.format(serialized_data['pe_wan_ip'], serialized_data['interface'])
				output_message = error_message + '\n[error message: PE IP Address retrieved from NE is {}]'.format(pe_wan_ip)
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'Modify Bandwidth',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output': 'PE IP Address retrieved from NE is {}'.format(pe_wan_ip),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = 'PE IP Address retrieved from NE is {}'.format(pe_wan_ip)
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                          #
			#        GET INTERFACE COS DETAILS         #
			#                                          #
		
			# sample API output
			# "output": {
			# 	"name": "xe-7/3/0",
			# 	"unit": [{
			# 		"name": 1020,
			# 		"scheduler-map": "SCHED_CONVERGENCE",
			# 		"shaping-rate": {
			# 			"rate": "8m"
			# 		},
			# 		"classifiers": {
			# 			"inet-precedence": {
			# 			"classifier-name": "COS_CONVERGENCE"
			# 			}
			# 		}
			# 	}]
			# }

			payload = {
				'ne_ip': serialized_data['device_id'],
				'interface': serialized_data['interface']
			}
			response = requests.get(url+'v2/show_interface_cos/', params=payload, verify=False)
			output = response.json()

			if output['status'] == 'failed':
				error_message = 'An error has occured while retrieving interface ({}) COS details'.format(serialized_data['interface'])
				output_message = error_message + '\n[error message: {}]'.format(output['output'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'Modify Bandwidth',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':error_message,
					'api_output':str(output['output']),
					'status':'failed',
					'log_file': log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = error_message
				api_response['data'] = output['output']
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
			else:
				if output['output'] == 'interface CoS is not exist':
					error_message = 'Interface ({}) CoS is not exist in PE ({})'.format(serialized_data['interface'], serialized_data['node_name'])
					log_text(filename=log_file, content=error_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':output['output'],
						'status':'failed',
						'log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					interface_cos_details = output['output']
					output_message = 'Successfully retrieved interface ({}) COS details'.format(serialized_data['interface'])
					log_text(filename=log_file, content=output_message, ne_output=dumps(interface_cos_details, indent=4, separators=(',', ': ')))

					# assign bandwidth
					bw = interface_cos_details['unit']['shaping-rate']['rate']

					# display shaping rate in log
					output_message = 'Successfully retrieved interface shaping-rate ({}) from PE'.format(bw)
					log_text(filename=log_file, content=output_message)

					# assign PE QOS package
					if re.search('classic', interface_cos_details['unit']['scheduler-map'], re.IGNORECASE):
						serialized_data['pe_qos'] = 'CLASSIC'
					elif re.search('convergence', interface_cos_details['unit']['scheduler-map'], re.IGNORECASE):
						serialized_data['pe_qos'] = 'CONVERGENCE'
					elif re.search('performance', interface_cos_details['unit']['scheduler-map'], re.IGNORECASE):
						serialized_data['pe_qos'] = 'PERFORMANCE'
					elif re.search('versatile', interface_cos_details['unit']['scheduler-map'], re.IGNORECASE):
						serialized_data['pe_qos'] = 'VERSATILE'
					elif re.search('essential', interface_cos_details['unit']['scheduler-map'], re.IGNORECASE):
						serialized_data['pe_qos'] = 'ESSENTIAL'
					elif re.search('customised', interface_cos_details['unit']['scheduler-map'], re.IGNORECASE):
						serialized_data['pe_qos'] = 'CUSTOMISED'
					else:
						error_message = 'Invalid QOS package retrieved from interface COS scheduler map ({})'.format(interface_cos_details['unit']['scheduler-map'])
						log_text(filename=log_file, content=error_message)
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output': 'Scheduler map returned from interface COS is {}'.format(interface_cos_details['unit']['scheduler-map']),
							'status':'failed',
							'log_file': log_file,
							'config_file': 'N/A',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = 'Scheduler map returned from interface COS is {}'.format(interface_cos_details['unit']['scheduler-map'])
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

					# display QOS package in log
					output_message = 'Successfully retrieved QOS package ({}) from PE'.format(serialized_data['pe_qos'])
					log_text(filename=log_file, content=output_message)

			#                                   #
			#        COMPARE EXISTING BW        #
			#                                   #

			cgate_error_message = str()

			# if existing BW != OLD & NEW BW
			if not re.match(bw, serialized_data['old_bandwidth'], re.IGNORECASE) and not re.match(bw, serialized_data['new_bandwidth'], re.IGNORECASE):
				output_message = 'Existing bandwidth ({}) is not match old bandwidth ({}) and new bandwidth ({})'.format(bw, serialized_data['old_bandwidth'], serialized_data['new_bandwidth'])
				log_text(filename=log_file, content=output_message)
				DB_data = {
					'order_type':'Modify Bandwidth',
					'service_type':'IPVPN',
					'input_details':serialized_data,
					'message':output_message,
					'api_output':'Existing bandwidth ({}) retrieved from PE'.format(bw),
					'status':'failed',
					'log_file':log_file,
					'config_file': 'N/A',
					'user': user
				}
				api_response['status'] = 'failed'
				api_response['message'] = output_message
				api_response['data'] = 'Existing bandwidth ({}) retrieved from PE'.format(bw)
				log_database(data_DB=DB_data)
				return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			# if existing BW == New BW
			elif re.match(bw, serialized_data['new_bandwidth'], re.IGNORECASE):
				output_message = 'Existing bandwidth ({}) match new bandwidth ({}). No change is required at PE'.format(bw, serialized_data['new_bandwidth'])
				log_text(filename=log_file, content=output_message)
				# Initialize variable due to no RFC created
				cgate_error_message = "RFC application is not required"
				config_file = 'N/A'


			# If existing BW == old BW
			elif re.match(bw, serialized_data['old_bandwidth'], re.IGNORECASE):
				#                                                #
				#               CHECK BGP Status                 #
				#                                                #

				payload = {
					'ne_ip': serialized_data['device_id'],
					'vpn': serialized_data['vpn_name'],
					'ce_ip': serialized_data['ce_wan_ip']
				}
				response = requests.get(url+'v2/get_bgp_summary/', params=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while retrieving BGP status from PE'
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed','log_file': log_file,
						'config_file': 'N/A',
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = str(output['output'])
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == 'Invalid BGP status':
						output_message = 'Invalid BGP status for CE IP Address ({}) returned from PE'.format(serialized_data['ce_wan_ip'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':output_message,
							'api_output':'BGP status retrieved from PE is ({})'.format(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': 'N/A',
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = output_message
						api_response['data'] = 'BGP status retrieved from PE is ({})'.format(output['output'])
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if output['output']['state'] != 'Established':
							output_message = 'BGP status for CE IP Address ({}) returned from PE ({}) is not established'.format(serialized_data['ce_wan_ip'], output['output']['state'])
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							DB_data = {
								'order_type':'Modify Bandwidth',
								'service_type':'IPVPN',
								'input_details':serialized_data,
								'message':output_message,
								'api_output':'BGP status retrieved from PE is ({})'.format(output['output']['state']),
								'status':'failed',
								'log_file': log_file,
								'config_file': 'N/A',
								'user': user
							}
							api_response['status'] = 'failed'
							api_response['message'] = output_message
							api_response['data'] = 'BGP status retrieved from PE is ({})'.format(output['output']['state'])
							log_database(data_DB=DB_data)
							return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
						else:
							output_message = 'BGP status for CE IP Address ({}) returned from PE is ({})'.format(serialized_data['ce_wan_ip'], output['output']['state'])
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])



				#                                                            #
				#          GENERATE PE IPME MODIFY BW CONFIGURATION          #
				#                                                            #
				config_file = 'PE_IPME_Modify_BW_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['node_name'],  date_now, time_now, user)		#create config file

				payload = {
					'interface': serialized_data['interface'],
					'vpn': serialized_data['vpn_name'].upper(),
					'service_id': serialized_data['service_id'],
					'bandwidth': serialized_data['new_bandwidth'].lower(),
					'filename': config_file,
				}

				response = requests.post(url+'v3/ipme/generate_config_modify_bw/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while generating PE configurations'
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					proof_config = "Proof of PE Configurations:\n\n{}\n{}\n\n".format(config_file, output['configuration'])
					output_message = output['output']
					log_text(filename=log_file, content=output_message)


				#                                                #
				#                   CHECK CPU                    #
				#                                                #
				match, count, ne_output, master_util_success, master_util_failed = False, int(), str(), dict(), dict()
				while not match:
					# if failed check 5 times
					if count > 4:
						break

					# create 5s delay to re-chek CPU
					if count > 0:
						time.sleep(10)

					payload = {
						'ne_ip': serialized_data['device_id'],
					}

					response = requests.get(url+'v1/check_cpu/', params=payload, verify=False)
					output = response.json()

					if output['status'] == 'failed':
						error_message = 'An error has occured while retrieving CPU utilization info from PE'
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message)
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						output_keys = output['output'].keys()
						for key in output_keys:
							if re.findall('main', key, re.IGNORECASE):
								if int(output['output'][key]['memory_utilization']) < 70 and int(output['output'][key]['idle']) > 10:
									match = True
									master_util_success[key] = {
										'memory_utilization':output['output'][key]['memory_utilization'],
										'idle':output['output'][key]['idle']
									}
								else:
									match = False
									master_util_failed[key] = {
										'memory_utilization':output['output'][key]['memory_utilization'],
										'idle':output['output'][key]['idle']
									}

						# increase counter if does not match threshold
						if not match:
							count += 1
						ne_output += output['ne_response']		# accumulate ne output in a var

				if not match:
					output_message = str()
					for key in master_util_failed:
						if output_message == '':
							output_message = "CPU utilization is outside threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
						else:
							output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(master_util_failed[key]['memory_utilization'])+' percent', str(master_util_failed[key]['idle'])+' percent')
					log_text(filename=log_file, content=output_message, ne_output=ne_output)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = str()
					for key in master_util_success:
						if output_message == '':
							output_message = "CPU utilization is within threshold i.e. {} memory utilization ({}) and idle ({})".format(key, str(master_util_success[key]['memory_utilization']) + ' percent' , str(master_util_success[key]['idle']) + ' percent')
						else:
							output_message += " and {} memory utilization ({}) and idle ({})".format(key, str(master_util_success[key]['memory_utilization'])+' percent', str(master_util_success[key]['idle'])+' percent')
					log_text(filename=log_file, content=output_message, ne_output=ne_output)

				#                                                #
				#                 PUSH PE CONFIG                 #
				#                                                #
				payload = {
					'ne_ip': serialized_data['device_id'],
					'filename': config_file
				}

				response = requests.post(url+'v1/load_config_modify/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while loading the generated configurations to PE'
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] != 'Successfully loaded configurations at PE':
						error_message = 'Failed to load configurations at PE'
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						log_text(filename=log_file, content=output['output'], ne_output=output['ne_response'])

				#                                                #
				#              CREATE RFC in CGATE               #
				#                                                #

				description = 'Service ID: {}, PE loopback IP : {}, PE interface : {}, New bandwidth : {}, Old bandwidth : {}, VPN Name: {}'.format(serialized_data['service_id'], serialized_data['device_id'], serialized_data['interface'], serialized_data['new_bandwidth'], serialized_data['old_bandwidth'], serialized_data['vpn_name'])
				file_config = open('/root/home/<USER>/{}.txt'.format(config_file), 'r')
				workplan = file_config.read()
				datetime_provi = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

				payload = {
					'rfc_status': 'close',
					'title': 'IPME MODIFY BANDWIDTH - {} ({})'.format(serialized_data['service_id'], serialized_data['vpn_name']),
					'description': description,
					'impact': 'Low',
					'work_plan': workplan,
					'fall_back': 'Rollback',
					'reason': 'Provisioning',
					'start': datetime_provi,
					'end': datetime_provi,
					'hostname': serialized_data['node_name'],
					'region': serialized_data['pe_region'],
					'actual_start': datetime_provi,
					'actual_end': datetime_provi,
					'task_completed': 'Successful',
					'inventory_update': 'No'
				}

				try:
					response = requests.get('https://cgate.tm.com.my/cms/api/pisa/createRFC.php',params=payload, verify=False, timeout=40)
					cgate_output = response.json()

					if cgate_output[0]['HEADER']['CREATE_STATUS']=='FAIL':
						cgate_error_message = 'Failed to create RFC in Cgate'
						output_message = cgate_error_message + '\n[error message: {}]'.format(cgate_output[0]['HEADER']['ERROR_MSG'])
						log_text(filename=log_file, content=output_message)
					else:
						cgate_success_message = 'Successfully created RFC in Cgate'
						log_text(filename=log_file, content=cgate_success_message)

				except Exception as error:
					cgate_error_message = 'Failed to create RFC in Cgate'
					output_message = cgate_error_message + '\n[error message: {}]'.format(str(error))
					log_text(filename=log_file, content=output_message)


			#                                                     #
			#                 CHECK L3 SERVICE CE                 #
			#                                                     #
			if not re.findall('premium', serialized_data['l3_service_ce'], re.IGNORECASE):
				if cgate_error_message == 'Failed to create RFC in Cgate':
					output_message = 'Successfully provisioned new bandwidth at PE only (unmanaged CE) but failed to create RFC in CGate'
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = cgate_error_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Successfully provisioned new bandwidth at PE only (unmanaged CE)'
					if cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and unmanaged CE'
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'success',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'success'
					api_response['message'] = output_message
					# api_response['data'] = 'No error'
					if proof_config:
						api_response['data'] = proof_config.replace('\n', '<br>')
					else:
						api_response['data'] = 'Proof of configurations is unavailable'
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_200_OK)

			#                                             #
			#                CHECK CE TYPE                #
			#                                             #
			if not re.findall('cisco', serialized_data['cpe_type'], re.IGNORECASE) and not re.findall('juniper', serialized_data['cpe_type'], re.IGNORECASE):
				if cgate_error_message == 'Failed to create RFC in Cgate':
					output_message = 'Successfully provisioned new bandwidth at PE only but not managed {} CE which is not supported and failed to create RFC in CGate'.format(serialized_data['cpe_type'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = cgate_error_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Successfully provisioned new bandwidth at PE only but not managed {} CE which is not supported'.format(serialized_data['cpe_type'])
					if cgate_error_message == 'RFC application is not required':
						output_message = 'No change is required at PE and failed to provision managed {} CE which is not supported'.format(serialized_data['cpe_type'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

			#                                                #
			#                   JUNIPER CE                   #
			#                                                #
			if re.findall('juniper', serialized_data['cpe_type'], re.IGNORECASE):
				url = 'https://pisa-juniper.prdin.kubix.tm/juniper/ce/'

				#                                                        #
				#                GET JUNIPER CE INTERFACE                #
				#                                                        #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'v2/get_interface/', params=payload, verify=False)
				output = response.json()

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving interface from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					# if output['output'] == "No CE interface is found":
					# 	error_message = 'Failed to retrieve interface from CE'
					# 	if cgate_error_message == 'Failed to create RFC in Cgate':
					# 		error_message = '{} .{}'.format(error_message, cgate_error_message)
					# 	output_message = error_message + '\n[error message: {}]'.format(output['output'])
					# 	log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					# 	DB_data = {
					# 		'order_type':'Modify Bandwidth',
					# 		'service_type':'IPVPN',
					# 		'input_details':serialized_data,
					# 		'message':error_message,
					# 		'api_output':str(output['output']),
					# 		'status':'failed',
					# 		'log_file': log_file,
					# 		'config_file': config_file,
					# 		'user': user
					# 	}
					# 	api_response['status'] = 'failed'
					# 	api_response['message'] = error_message
					# 	api_response['data'] = output['output']
					# 	log_database(data_DB=DB_data)
					# 	return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					# else:
					serialized_data['ce_interface'] = '{}.{}'.format(output['output']['name'], output['output']['unit'][0]['name'])
					output_message = 'Successfully retrieved CE Interface ({})'.format(serialized_data['ce_interface'])
					log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

				#                                               #
				#               GET JUNIPER CE QOS              #
				#                                               #

				payload = {
					'ce_ip': serialized_data['ce_wan_ip'], 
					'interface': serialized_data['ce_interface'],
				}
				response = requests.get(url+'v2/show_interface_cos/', params=payload, verify=False)
				output = response.json()

				# expected output
				# "output": {
				# 	"name": "ge-0/0/0",
				# 	"unit": [
				# 	{
				# 		"scheduler-map": "SCHED_CONVERGENCE",
				# 		"shaping-rate": "10m",
				# 		"classifiers": {
				# 			"inet-precedence": "COS_CONVERGENCE"
				# 		},
				# 		"name": "0"
				# 	}
				# 	]
				# }

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving QOS from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == 'interface CoS is not exist':
						serialized_data['ce_qos'] = 'CLASSIC'

						output_message = 'Successfully retrieved CE QOS ({})'.format(serialized_data['ce_qos'])
						log_text(filename=log_file, content=output_message)
					else:
						# assign CE QOS package
						if re.search('convergence', output['output']['unit'][0]['scheduler-map'], re.IGNORECASE):
							serialized_data['ce_qos'] = 'CONVERGENCE'
						elif re.search('performance', output['output']['unit'][0]['scheduler-map'], re.IGNORECASE):
							serialized_data['ce_qos'] = 'PERFORMANCE'
						elif re.search('versatile', output['output']['unit'][0]['scheduler-map'], re.IGNORECASE):
							serialized_data['ce_qos'] = 'VERSATILE'
						elif re.search('essential', output['output']['unit'][0]['scheduler-map'], re.IGNORECASE):
							serialized_data['ce_qos'] = 'ESSENTIAL'
						elif re.search('classic', output['output']['unit'][0]['scheduler-map'], re.IGNORECASE):
							serialized_data['ce_qos'] = 'CLASSIC'
						elif re.search('customised', output['output']['unit'][0]['scheduler-map'], re.IGNORECASE):
							serialized_data['ce_qos'] = 'CUSTOMISED'
						else:
							serialized_data['ce_qos'] = 'Not Available'

						output_message = 'Successfully retrieved CE QOS ({})'.format(serialized_data['ce_qos'])
						log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

				#                                                        #
				#              COMPARE PE & JUNIPER CE QOS               #
				#                                                        #
				if serialized_data['ce_qos'] != serialized_data['pe_qos']:
					output_message = 'Mismatch QOS Package retrieved from PE ({}) and CE ({})'.format(serialized_data['pe_qos'], serialized_data['ce_qos'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = '{} .{}'.format(output_message, cgate_error_message)
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output': 'CE QOS retrieved from CE is {}'.format(serialized_data['ce_qos']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = 'CE QOS retrieved from CE is {}'.format(serialized_data['ce_qos'])
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Both PE and CE QOS Package ({}) is match'.format(serialized_data['pe_qos'])
					log_text(filename=log_file, content=output_message)

				#                                                          #
				#              GEN JUNIPER CE CONFIGURATIONS               #
				#                                                          #
				ce_config_file = 'CE_IPME_Modify_BW_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ce_wan_ip'],  date_now, time_now, user)		#create config file
				# parameters = [ce_port, ce_vlan, serialized_data['new_bandwidth']]
				
				payload = {
					'filename': ce_config_file,
					'interface': serialized_data['ce_interface'],
					'bandwidth': serialized_data['new_bandwidth']
				}

				if serialized_data['ce_qos']=='CLASSIC':
					payload['interface_cos'] = False
				else:
					payload['interface_cos'] = True

				response = requests.post(url+'v2/ipme/generate_config_modify_bw/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while generating CE configurations'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					proof_config = proof_config + "Proof of CE Configurations:\n\n{}\n{}\n\n".format(ce_config_file, output['configuration'])
					output_message = output['output']
					log_text(filename=log_file, content=output_message)

				#                                                  #
				#              PUSH JUNIPER CE CONFIG              #
				#                                                  #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
					'filename': ce_config_file
				}

				response = requests.post(url+'v1/load_config/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while loading the generated configurations to CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file + ',' + ce_config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] != 'Successfully loaded configurations at CE':
						error_message = 'Failed to load configurations at CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if cgate_error_message == 'Failed to create RFC in Cgate':
							output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in CGate'
							log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
							DB_data = {
								'order_type':'Modify Bandwidth',
								'service_type':'IPVPN',
								'input_details':serialized_data,
								'message':output_message,
								'api_output':str(output['output']),
								'status':'failed',
								'log_file': log_file,
								'config_file': config_file + ',' + ce_config_file,
								'user': user
							}
							api_response['status'] = 'failed'
							api_response['message'] = output_message
							api_response['data'] = cgate_error_message
							log_database(data_DB=DB_data)
							return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							
						elif cgate_error_message == 'RFC application is not required':
							output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
							db_config_file = ce_config_file

						else:
							output_message = 'Successfully provisioned new bandwidth at PE and CE'
							db_config_file = config_file + ',' + ce_config_file
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message': output_message,
							'api_output':str(output['output']),
							'status':'success',
							'log_file': log_file,
							'config_file': db_config_file,
							'user': user
						}
						api_response['status'] = 'success'
						api_response['message'] = output_message
						# api_response['data'] = 'No error'
						if proof_config:
							api_response['data'] = proof_config.replace('\n', '<br>')
						else:
							api_response['data'] = 'Proof of configurations is unavailable'
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_200_OK)


			#                                                #
			#                    CISCO CE                    #
			#                                                #
			if re.findall('cisco', serialized_data['cpe_type'], re.IGNORECASE):

				url = 'https://pisa-cisco.prdin.kubix.tm/cisco/ce/'
				
				#                                                #
				#                GET CE INTERFACE                #
				#                                                #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'v2/get_interface/', params=payload, verify=False)
				output = response.json()

				# expected output
				# {
				# 	"name": "GigabitEthernet0/1",
				# 	"ip-address": "**************",
				# 	"status": "up",
				# 	"protocol": "up"
				# }

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving interface from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					# if output['output'] == "No interface match IP Address":
					# 	error_message = 'Failed to retrieve interface from CE'
					# 	if cgate_error_message == 'Failed to create RFC in Cgate':
					# 		error_message = '{} .{}'.format(error_message, cgate_error_message)
					# 	output_message = error_message + '\n[error message: {}]'.format(output['output'])
					# 	log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
					# 	DB_data = {
					# 		'order_type':'Modify Bandwidth',
					# 		'service_type':'IPVPN',
					# 		'input_details':serialized_data,
					# 		'message':error_message,
					# 		'api_output':str(output['output']),
					# 		'status':'failed',
					# 		'log_file': log_file,
					# 		'config_file': config_file,
					# 		'user': user
					# 	}
					# 	api_response['status'] = 'failed'
					# 	api_response['message'] = error_message
					# 	api_response['data'] = output['output']
					# 	log_database(data_DB=DB_data)
					# 	return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					# else:
					serialized_data['ce_interface'] = output['output']['name']
					output_message = 'Successfully retrieved CE Interface ({})'.format(serialized_data['ce_interface'])
					log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

				#                                                #
				#               GET CE QOS & Policy              #
				#                                                #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'], 
					'ce_interface': serialized_data['ce_interface'],
				}
				response = requests.get(url+'v2/show_interface/', params=payload, verify=False)
				output = response.json()

				# expected output
				# {
				# 	"bandwidth": "10000",
				# 	"ip-address": "**************",
				# 	"service-policy": "out_DORADO_CONVERGENCE",
				# 	"name": "GigabitEthernet0/1"
				# }

				if output['status']=='failed':
					error_message = 'An error has occured while retrieving QOS/service policy from CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == 'interface is not exist':
						error_message = 'Failed to retrieve QOS/service policy from CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						if 'service-policy' in output['output'].keys():
							# assign PE QOS package
							if re.search('convergence', output['output']['service-policy'], re.IGNORECASE):
								serialized_data['ce_qos'] = 'CONVERGENCE'
							elif re.search('performance', output['output']['service-policy'], re.IGNORECASE):
								serialized_data['ce_qos'] = 'PERFORMANCE'
							elif re.search('versatile', output['output']['service-policy'], re.IGNORECASE):
								serialized_data['ce_qos'] = 'VERSATILE'
							elif re.search('essential', output['output']['service-policy'], re.IGNORECASE):
								serialized_data['ce_qos'] = 'ESSENTIAL'
							elif re.search('customised', output['output']['service-policy'], re.IGNORECASE):
								serialized_data['ce_qos'] = 'CUSTOMISED'
							else:
								serialized_data['ce_qos'] = 'Not Available'

							serialized_data['service_policy'] = output['output']['service-policy']
							output_message = 'Successfully retrieved CE QOS ({}) and service policy ({})'.format(serialized_data['ce_qos'], output['output']['service-policy'])
						else:
							output_message = 'Successfullly retrieved CE QOS (CLASSIC)'
							serialized_data['ce_qos'] = 'CLASSIC'
						log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))

				#                                                #
				#              COMPARE PE & CE QOS               #
				#                                                #
				if not re.search(serialized_data['pe_qos'], serialized_data['ce_qos'], re.IGNORECASE):
					output_message = 'Mismatch QOS Package retrieved from PE ({}) and CE ({})'.format(serialized_data['pe_qos'], serialized_data['ce_qos'])
					if cgate_error_message == 'Failed to create RFC in Cgate':
						output_message = '{} .{}'.format(output_message, cgate_error_message)
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':output_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = output_message
					api_response['data'] = output_message
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					output_message = 'Both PE and CE QOS Package ({}) is match'.format(serialized_data['pe_qos'])
					log_text(filename=log_file, content=output_message)
		

				#                                                  #
				#              GEN CE CONFIGURATIONS               #
				#                                                  #
				ce_config_file = 'CE_IPME_Modify_BW_{}_{}_{}@{}_by_{}'.format(serialized_data['service_id'], serialized_data['ce_wan_ip'],  date_now, time_now, user)		#create config file
				
				payload = {
					'interface': serialized_data['ce_interface'],
					'bandwidth': serialized_data['new_bandwidth'],
					'filename': ce_config_file,
				}

				if serialized_data['ce_qos']=='CLASSIC':
					payload['service_policy'] = ''
					payload['interface_cos'] = False
				else:
					payload['service_policy'] = serialized_data['service_policy']
					payload['interface_cos'] = True
				
				response = requests.post(url+'v2/ipme/generate_config_modify_bw/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while generating CE configurations'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					proof_config = proof_config + "Proof of CE Configurations:\n\n{}\n{}\n\n".format(ce_config_file, output['configuration'])
					output_message = output['output']
					log_text(filename=log_file, content=output_message)


				#                                          #
				#              PUSH CE CONFIG              #
				#                                          #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
					'filename': ce_config_file
				}

				response = requests.post(url+'v1/load_config/', data=payload, verify=False)
				output = response.json()

				if output['status'] == 'failed':
					error_message = 'An error has occured while loading the generated configurations to CE'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file + ',' + ce_config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] != 'Successfully loaded configurations at CE':
						error_message = 'Failed to load configurations at CE'
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						ce_output_message = 'Successfully provisioned new bandwidth at CE'
						log_text(filename=log_file, content=ce_output_message, ne_output=output['ne_response'])


				#                                                  #
				#               CHECK CE SOFTWARE                  #
				#                                                  #
				payload = {
					'ce_ip': serialized_data['ce_wan_ip'],
				}
				response = requests.get(url+'v2/show_version/', params=payload, verify=False)
				output = response.json()

				# expected output
				# {
				# 	"software": "Cisco IOS Software",
				# 	"model": "CISCO2911/K9"
				# }

				if output['status'] == 'failed':
					error_message = 'An error has occured while checking CE software details'
					if cgate_error_message == 'Failed to create RFC in Cgate':
						error_message = '{} .{}'.format(error_message, cgate_error_message)
					output_message = error_message + '\n[error message: {}]'.format(output['output'])
					log_text(filename=log_file, content=output_message)
					DB_data = {
						'order_type':'Modify Bandwidth',
						'service_type':'IPVPN',
						'input_details':serialized_data,
						'message':error_message,
						'api_output':str(output['output']),
						'status':'failed',
						'log_file': log_file,
						'config_file': config_file + ',' + ce_config_file,
						'user': user
					}
					api_response['status'] = 'failed'
					api_response['message'] = error_message
					api_response['data'] = output['output']
					log_database(data_DB=DB_data)
					return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
				else:
					if output['output'] == 'CE software/model version is not found':
						error_message = output['output']
						if cgate_error_message == 'Failed to create RFC in Cgate':
							error_message = '{} .{}'.format(error_message, cgate_error_message)
						output_message = error_message + '\n[error message: {}]'.format(output['output'])
						log_text(filename=log_file, content=output_message, ne_output=output['ne_response'])
						DB_data = {
							'order_type':'Modify Bandwidth',
							'service_type':'IPVPN',
							'input_details':serialized_data,
							'message':error_message,
							'api_output':str(output['output']),
							'status':'failed',
							'log_file': log_file,
							'config_file': config_file + ',' + ce_config_file,
							'user': user
						}
						api_response['status'] = 'failed'
						api_response['message'] = error_message
						api_response['data'] = output['output']
						log_database(data_DB=DB_data)
						return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
					else:
						output_message = "Successfully retrieved CE software/model version"
						log_text(filename=log_file, content=output_message, ne_output=dumps(output['output'], indent=4, separators=(',', ': ')))
						
						if re.search('xe', output['output']['software'], re.IGNORECASE) or re.search('xr', output['output']['software'], re.IGNORECASE):
							#                                                   #
							#               CHECK CE THROUGHPUT                 #
							#                                                   #
							payload = {
								'ce_ip': serialized_data['ce_wan_ip'],
							}
							response = requests.get(url+'v2/get_throughput/', params=payload, verify=False)
							output = response.json()
							if output['status'] == 'failed':
								error_message = 'An error has occured while checking CE throughput'
								if cgate_error_message == 'Failed to create RFC in Cgate':
									error_message = '{} .{}'.format(error_message, cgate_error_message)
								output_message = error_message + '\n[error message: {}]'.format(output['output'])
								log_text(filename=log_file, content=output_message)
								DB_data = {
									'order_type':'Modify Bandwidth',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':error_message,
									'api_output':str(output['output']),
									'status':'failed',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'failed'
								api_response['message'] = error_message
								api_response['data'] = output['output']
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							else:
								if output['output'] == 'CE throughput level is unthrottled' or output['output'] == 'No CE throughput level is found':
									error_message = 'CE throughput level is not configured'
									if cgate_error_message == 'Failed to create RFC in Cgate':
										error_message = '{} .{}'.format(error_message, cgate_error_message)
									output_message = error_message + '\n[error message: {}]'.format(output['output'])
									log_text(filename=log_file, content=output_message)
									DB_data = {
										'order_type':'Modify Bandwidth',
										'service_type':'IPVPN',
										'input_details':serialized_data,
										'message':error_message,
										'api_output':str(output['output']),
										'status':'failed',
										'log_file': log_file,
										'config_file': config_file + ',' + ce_config_file,
										'user': user
									}
									api_response['status'] = 'failed'
									api_response['message'] = error_message
									api_response['data'] = output['output']
									log_database(data_DB=DB_data)
									return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
								else:
									#                                           #
									#              GEN THROUGHPUT               #
									#                                           #
									if re.findall('k', serialized_data['new_bandwidth'], re.IGNORECASE):
										serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2
									elif re.findall('m', serialized_data['new_bandwidth'], re.IGNORECASE):
										serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2000
									elif re.findall('g', serialized_data['new_bandwidth'], re.IGNORECASE):
										serialized_data['ce_throughput'] = int(re.sub(r'\D', '', serialized_data['new_bandwidth'])) * 2000000
									else:
										output_message = 'New bandwidth ({}) does not contain a valid unit'.format(serialized_data['new_bandwidth'])
										if cgate_error_message == 'Failed to create RFC in Cgate':
											output_message = '{} .{}'.format(output_message, cgate_error_message)
										log_text(filename=log_file, content=output_message)
										DB_data = {
											'order_type':'Modify Bandwidth',
											'service_type':'IPVPN',
											'input_details':serialized_data,
											'message':output_message,
											'api_output':serialized_data['new_bandwidth'],
											'status':'failed',
											'log_file': log_file,
											'config_file': config_file + ',' + ce_config_file,
											'user': user}
										api_response['status'] = 'failed'
										api_response['message'] = output_message
										api_response['data'] = serialized_data['new_bandwidth']
										log_database(data_DB=DB_data)
										return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

									if serialized_data['ce_throughput'] > int(output['output']['throughput']):
										error_message = 'The new bandwidth {} (times 2) is more than allowable CE throughput level {}'.format(serialized_data['new_bandwidth'].lower(), str(output['output']['throughput'])+'k')
										if cgate_error_message == 'Failed to create RFC in Cgate':
											error_message = '{} .{}'.format(error_message, cgate_error_message)
										log_text(filename=log_file, content=error_message)
										DB_data = {
											'order_type':'Modify Bandwidth',
											'service_type':'IPVPN',
											'input_details':serialized_data,
											'message':error_message,
											'api_output': 'CE througput level is {}'.format(str(output['output']['throughput'])+'k'),
											'status':'failed',
											'log_file': log_file,
											'config_file': config_file + ',' + ce_config_file,
											'user': user
										}
										api_response['status'] = 'failed'
										api_response['message'] = error_message
										api_response['data'] = 'CE througput level is {}'.format(str(output['output']['throughput'])+'k')
										log_database(data_DB=DB_data)
										return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
									else:
										output_message = 'The new bandwidth {} (times 2) is lesser than allowable CE throughput level {}'.format(serialized_data['new_bandwidth'].lower(), str(output['output']['throughput'])+'k')
										log_text(filename=log_file, content=output_message)

										# If RFC creation is failed 
										if cgate_error_message == 'Failed to create RFC in Cgate':
											output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
											log_text(filename=log_file, content=output_message)
											DB_data = {
												'order_type':'Modify Bandwidth',
												'service_type':'IPVPN',
												'input_details':serialized_data,
												'message':output_message,
												'api_output':cgate_error_message,
												'status':'failed',
												'log_file': log_file,
												'config_file': config_file,
												'user': user
											}
											api_response['status'] = 'failed'
											api_response['message'] = output_message
											api_response['data'] = cgate_error_message
											log_database(data_DB=DB_data)
											return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

										# If no change is required at PE
										elif cgate_error_message == 'RFC application is not required':
											output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
											db_config_file = ce_config_file

										else:
											output_message = 'Successfully provisioned new bandwidth at PE and CE'
											db_config_file = config_file + ',' + ce_config_file
										
										log_text(filename=log_file, content=output_message)
										DB_data = {
											'order_type':'Modify Bandwidth',
											'service_type':'IPVPN',
											'input_details':serialized_data,
											'message': output_message,
											# 'api_output':str(output['output']),
											'api_output':output_message,
											'status':'success',
											'log_file': log_file,
											'config_file': db_config_file,
											'user': user
										}
										api_response['status'] = 'success'
										api_response['message'] = output_message
										# api_response['data'] = 'No error'
										if proof_config:
											api_response['data'] = proof_config.replace('\n', '<br>')
										else:
											api_response['data'] = 'Proof of configurations is unavailable'
										log_database(data_DB=DB_data)
										return Response(api_response,status=status.HTTP_200_OK)

						else:
							ce_version_bandwidth = [
								# {'version':'3945E','bandwidth':350},
								# {'version':'3925E','bandwidth':250},
								# {'version':'3945','bandwidth':150},
								# {'version':'3925','bandwidth':100},
								# {'version':'2951','bandwidth':75},
								# {'version':'2921','bandwidth':50},
								# {'version':'2911','bandwidth':35},
								# {'version':'2901','bandwidth':25},
								# {'version':'1941','bandwidth':25},
								# {'version':'1921','bandwidth':15},
								# {'version':'890','bandwidth':15},
								# {'version':'880','bandwidth':8},
								# {'version':'860','bandwidth':4},
								{'version':'3945E','bandwidth':350},
								{'version':'3925E','bandwidth':250},
								{'version':'3945','bandwidth':150},
								{'version':'3925','bandwidth':100},
								{'version':'2951','bandwidth':75},
								{'version':'2921','bandwidth':50},
								{'version':'2911','bandwidth':35},
								{'version':'2901','bandwidth':25},
								{'version':'1941','bandwidth':9},
								{'version':'1921','bandwidth':7},
								{'version':'890','bandwidth':25},
								{'version':'887VAG+7-K9','bandwidth':21},
								{'version':'887VA-K9','bandwidth':15},
								{'version':'880','bandwidth':15},
								{'version':'860VAE','bandwidth':8},
								{'version':'860','bandwidth':4},
								{'version':'819G+7-K9','bandwidth':8},
							]

							ce_version_bw = str()
							for x in ce_version_bandwidth:
								if x['version'] in output['output']['model']:
									ce_version_bw = x['bandwidth']			# determine allowed bandwidth
									break

							if ce_version_bw and int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 > ce_version_bw:		# check if new BW X 2 more than allowed bandwidth
								error_message = 'The new bandwidth {} (times 2) is more than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
								if cgate_error_message == 'Failed to create RFC in Cgate':
									error_message = '{} .{}'.format(error_message, cgate_error_message)
								log_text(filename=log_file, content=error_message)
								DB_data = {
									'order_type':'Modify Bandwidth',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':error_message,
									'api_output': 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m'),
									'status':'failed',
									'log_file': log_file,
									'config_file': config_file + ',' + ce_config_file,
									'user': user
								}
								api_response['status'] = 'failed'
								api_response['message'] = error_message
								api_response['data'] = 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m')
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							else:
								if ce_version_bw and int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 < ce_version_bw:
									output_message = 'The new bandwidth {} (times 2) is lesser than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
								else:
									output_message = 'The CE model ({}) is not exist in CE version list'.format(output['output']['model'])
								log_text(filename=log_file, content=output_message)
								if cgate_error_message == 'Failed to create RFC in Cgate':
									output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
									
									log_text(filename=log_file, content=output_message)
									DB_data = {
										'order_type':'Modify Bandwidth',
										'service_type':'IPVPN',
										'input_details':serialized_data,
										'message':output_message,
										'api_output':cgate_error_message,
										'status':'failed',
										'log_file': log_file,
										'config_file': config_file + ',' + ce_config_file,
										'user': user
									}
									api_response['status'] = 'failed'
									api_response['message'] = output_message
									api_response['data'] = cgate_error_message
									log_database(data_DB=DB_data)
									return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

								elif cgate_error_message == 'RFC application is not required':
									output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
									db_config_file = ce_config_file

								else:
									output_message = 'Successfully provisioned new bandwidth at PE and CE'
									db_config_file = config_file + ',' + ce_config_file

								log_text(filename=log_file, content=output_message)
								DB_data = {
									'order_type':'Modify Bandwidth',
									'service_type':'IPVPN',
									'input_details':serialized_data,
									'message':output_message,
									'api_output':output_message,
									'status':'success',
									'log_file': log_file,
									'config_file': db_config_file,
									'user': user
								}
								api_response['status'] = 'success'
								api_response['message'] = output_message
								# api_response['data'] = output_message
								if proof_config:
									api_response['data'] = proof_config.replace('\n', '<br>')
								else:
									api_response['data'] = 'Proof of configurations is unavailable'
								log_database(data_DB=DB_data)
								return Response(api_response,status=status.HTTP_200_OK)

							# if ce_version_bw == '':
							# 	error_message = 'The CE model ({}) version is not found in the saved list'.format(output['output']['model'])
							# 	if cgate_error_message == 'Failed to create RFC in Cgate':
							# 		error_message = '{} .{}'.format(error_message, cgate_error_message)
							# 	log_text(filename=log_file, content=error_message)
							# 	DB_data = {
							# 		'order_type':'Modify Bandwidth',
							# 		'service_type':'IPVPN',
							# 		'input_details':serialized_data,
							# 		'message':error_message,
							# 		'api_output':str(output['output']),
							# 		'status':'failed',
							# 		'log_file': log_file,
							# 		'config_file': config_file + ',' + ce_config_file,
							# 		'user': user
							# 	}
							# 	api_response['status'] = 'failed'
							# 	api_response['message'] = error_message
							# 	api_response['data'] = output['output']
							# 	log_database(data_DB=DB_data)
							# 	return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							# else:
							# 	if int(re.sub(r'\D', '', serialized_data['new_bandwidth']))*2 > ce_version_bw:		# check if new BW X 2 more than allowed bandwidth
							# 		error_message = 'The new bandwidth {} (times 2) is more than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
							# 		if cgate_error_message == 'Failed to create RFC in Cgate':
							# 			error_message = '{} .{}'.format(error_message, cgate_error_message)
							# 		log_text(filename=log_file, content=error_message)
							# 		DB_data = {
							# 			'order_type':'Modify Bandwidth',
							# 			'service_type':'IPVPN',
							# 			'input_details':serialized_data,
							# 			'message':error_message,
							# 			'api_output': 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m'),
							# 			'status':'failed',
							# 			'log_file': log_file,
							# 			'config_file': config_file + ',' + ce_config_file,
							# 			'user': user
							# 		}
							# 		api_response['status'] = 'failed'
							# 		api_response['message'] = error_message
							# 		api_response['data'] = 'WAN circuit speed is {}'.format(str(ce_version_bw)+'m')
							# 		log_database(data_DB=DB_data)
							# 		return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
							# 	else:
							# 		output_message = 'The new bandwidth {} (times 2) is lesser than allowable WAN circuit speed {}'.format(serialized_data['new_bandwidth'].lower(), str(ce_version_bw)+'m')
							# 		log_text(filename=log_file, content=output_message)

							# 		if cgate_error_message == 'Failed to create RFC in Cgate':
							# 			output_message = 'Successfully provisioned new bandwidth at PE and CE but failed to create RFC in Cgate'
										
							# 			log_text(filename=log_file, content=output_message)
							# 			DB_data = {
							# 				'order_type':'Modify Bandwidth',
							# 				'service_type':'IPVPN',
							# 				'input_details':serialized_data,
							# 				'message':output_message,
							# 				'api_output':cgate_error_message,
							# 				'status':'failed',
							# 				'log_file': log_file,
							# 				'config_file': config_file + ',' + ce_config_file,
							# 				'user': user
							# 			}
							# 			api_response['status'] = 'failed'
							# 			api_response['message'] = output_message
							# 			api_response['data'] = cgate_error_message
							# 			log_database(data_DB=DB_data)
							# 			return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

							# 		elif cgate_error_message == 'RFC application is not required':
							# 			output_message = 'No change is required at PE and successfully provisioned new bandwidth at CE'
							# 			db_config_file = ce_config_file

							# 		else:
							# 			output_message = 'Successfully provisioned new bandwidth at PE and CE'
							# 			db_config_file = config_file + ',' + ce_config_file

							# 		log_text(filename=log_file, content=output_message)
							# 		DB_data = {
							# 			'order_type':'Modify Bandwidth',
							# 			'service_type':'IPVPN',
							# 			'input_details':serialized_data,
							# 			'message':output_message,
							# 			'api_output':output_message,
							# 			'status':'success',
							# 			'log_file': log_file,
							# 			'config_file': db_config_file,
							# 			'user': user
							# 		}
							# 		api_response['status'] = 'success'
							# 		api_response['message'] = output_message
							# 		api_response['data'] = output_message
							# 		log_database(data_DB=DB_data)
							# 		return Response(api_response,status=status.HTTP_200_OK)


		except Exception as error:
			output_error = list()
			exc_type, exc_obj, exc_tb = sys.exc_info()
			output_error.append(str(exc_type))
			output_error.append(str(exc_obj))
			output_error.append(str(exc_tb.tb_lineno))
			error_message = 'Unexpected error has occured while executing API'
			output_message = error_message + '\n[error message: {}]'.format(error)
			log_text(filename=log_file, content=output_message)
			DB_data = {
				'order_type':'Modify Bandwidth',
				'service_type':'IPVPN',
				'input_details':serialized_data,
				'message':error_message,
				'api_output':str(error),
				'status':'failed',
				'log_file': log_file,
				'config_file': 'Undefined',
				'user': user
			}
			api_response['status'] = 'failed'
			api_response['message'] = error_message
			api_response['data'] = str(error)
			# api_response['data'] = output_error
			log_database(data_DB=DB_data)
			return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
