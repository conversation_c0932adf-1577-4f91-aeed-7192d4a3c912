from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers import L2L3TbuSerializer
import requests, json, time, re, os, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps
from concurrent.futures import ThreadPoolExecutor


class API(APIView):

    # authentication_classes = ()     #exclude from global authentication
    # permission_classes = ()

    @swagger_auto_schema(
        request_body=L2L3TbuSerializer,
        responses={201: L2L3TbuSerializer()},
        operation_summary="Workflow for TBU for both L2 & L3 NE (without CE) for IPME"
    )

    def post(self, request):

        try:

            # GET USER ID
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                user_id = Token.objects.get(key=token.replace('Token ','')).user_id
                user = User.objects.get(id=user_id).username
            else:
                user = 'not_available'

            #                                           #
            #              VERIFY API INPUT             #
            #                                           #
            input_data = L2L3TbuSerializer(data=request.data)
            input_data.is_valid()

            # initiate variable
            api_response = dict()

            if len(input_data.errors) > 0:  # check if passed data contains error and return error
                output_message = 'Incomplete/invalid information fetched by API'
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = input_data.errors
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
            
             # serialized_data = input_data.data[]   # assign a variable with serialized data
            serialized_data = input_data.validated_data
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = f"L2L3_IPME_TBU_{serialized_data['service_id']}_{date_now}@{time_now}_by_{user}.txt"     # Create Log File

             # LOG CREATED TIME #
            output_message = "File created at {} {}".format(date_now, time_now)
            log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(
                request.data, indent=4, separators=(',', ': ')))
            
            #                                                   #
            #              Get info from Granite                #
            #                                                   #
            payload = {
                "graniteid": serialized_data['service_id'],
                "queryscenario": "POSITIVE"
            }

            headers = {
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.TklHX1RPS0VOX0NQ.tyz7S9IumAJe09eF8jpkd29dIFfVwZ3wWcAYgQO1JtM',
                'Content-Type': 'application/json'
            }

            response = requests.post(
                "https://nig.tm.com.my/spice/api/GetLayer3", 
                headers=headers, 
                json=payload
            )

            output = response.json()
            payload_pe, payload_npe, payload_epe = dict(), dict(), dict()

            if 'start_date' in serialized_data and 'end_date' in serialized_data:       # for bw upgrade
                payload_pe = {
                    "start_date": str(serialized_data["start_date"]),
                    "end_date": str(serialized_data["end_date"]),
                    "service_id": serialized_data["service_id"],
                    "new_bandwidth": serialized_data["new_bandwidth"],
                    "old_bandwidth": serialized_data["old_bandwidth"],
                }
            else:                                                                       # for bw normalization
                payload_pe = {
                    "service_id": serialized_data["service_id"],
                    "new_bandwidth": serialized_data["new_bandwidth"],
                    "old_bandwidth": serialized_data["old_bandwidth"],
                }

            payload_npe = {
                "service_id": serialized_data["service_id"],
                "new_bandwidth": serialized_data["new_bandwidth"],
                "old_bandwidth": serialized_data["old_bandwidth"],
            }

            payload_epe = {
                "service_id": serialized_data["service_id"],
                "new_bandwidth": serialized_data["new_bandwidth"],
                "old_bandwidth": serialized_data["old_bandwidth"],
            }

            # check if passed data contains error and return error
            if output['NIGResponse']['ErrorMesage']=='':
                    
                # check if Layer 3 and Layer 2 exists in output['NIGResponse']
                if 'layer3' in output['NIGResponse'] and 'layer2' in output['NIGResponse']:

                    # get info for Layer 3
                    for item in output['NIGResponse']['layer3']:
                        if item["name"] == "VPN Name":
                            payload_pe['vpn_name'] = item["value"]
                            payload_npe['vpn_name'] = item["value"]
                            payload_epe['vpn_name'] = item["value"]
                            if 'payload_epe_2' in locals() and not 'vpn_name' in payload_epe_2:	
                                payload_epe_2['vpn_name'] = item["value"]
                            continue
                        elif item["name"] == "Device Name":
                            payload_pe['node_name'] = item["value"].replace('M0.', '').replace('M1.', '')
                            payload_npe['sys_name'] = item["value"].replace('M0.', '').replace('M1.', '')
                            continue
                        elif item["name"] == "Device Loopback IP":
                            payload_pe['device_id'] = re.sub(r"[^0-9.]", "", item["value"])
                            continue
                        elif item["name"] == "PE Interface":
                            payload_pe['interface'] = item["value"]
                            continue
                        elif item["name"] == "PE Region":
                            payload_pe['pe_region'] = item["value"]
                            payload_npe['region'] = item["value"]
                            payload_epe['region'] = item["value"]
                            if 'payload_epe_2' in locals() and not 'region' in payload_epe_2:
                                payload_epe_2['region'] = item["value"]
                            continue
                        elif item["name"] == "PE_WAN_IP":
                            payload_pe['pe_wan_ip'] = re.sub(r"[^0-9.]", "", item["value"])
                            continue
                        elif item["name"] == "CE_WAN_IP":
                            payload_pe['ce_wan_ip'] = re.sub(r"[^0-9.]", "", item["value"])
                            continue
                        # request the value from user instead of taking from Granite
                        # elif item["name"] == "Bandwidth":
                        #     payload_pe['old_bandwidth'] = item["value"]
                        #     payload_npe['old_bandwidth'] = item["value"]
                        #     payload_epe['old_bandwidth'] = item["value"]
                        #     continue

                    for item in output['NIGResponse']['layer2']:

                        # get info for Layer 2 (CPE)
                        if re.search(r"^(?=.*\bcpe\b)(?=.*\bcontainer\b).*", item["name"], re.IGNORECASE):
                            # assign CPE vendor
                            if re.search('juniper', item["name"], re.IGNORECASE):
                                payload_pe['cpe_type'] = 'juniper'
                                payload_pe['l3_service_ce'] = 'managed'
                            elif re.search('cisco', item["name"], re.IGNORECASE):
                                payload_pe['cpe_type'] = 'cisco'
                                payload_pe['l3_service_ce'] = 'managed'
                            elif re.search('other', item["name"], re.IGNORECASE):
                                payload_pe['cpe_type'] = 'others'
                                payload_pe['l3_service_ce'] = 'unmanaged'
                            continue

                        # get info for Layer 2 (NPE & EPE)
                        elif re.search(r"qos", item["name"], re.IGNORECASE):
                            payload_npe['qos_package'] = item["value"]
                            payload_epe['qos_package'] = item["value"]
                            if 'payload_epe_2' in locals() and not 'qos_package' in payload_epe_2:
                                payload_epe_2['qos_package'] = item["value"]
                            continue

                        # get info for Layer 2 (NPE)
                        elif re.search(r"(?=.*\bnpe\b)(?=.*\buplink\b)(?=.*\bcontainer\sid\b).*", item["name"], re.IGNORECASE):
                            payload_npe['node_name'] = item["value"]
                            continue
                        elif re.search(r"(?=.*\bnpe\b)(?=.*\buplink\b)(?=.*\binterface\sname\b).*", item["name"], re.IGNORECASE):
                            payload_npe['interface'] = re.sub('ten', '', item["value"], flags=re.IGNORECASE)
                            continue
                        elif re.search(r"(?=.*\bnpe\b)(?=.*\buplink\b)(?=.*\bdevice\sid\b).*", item["name"], re.IGNORECASE):
                            payload_npe['loopback_ip'] = re.sub(r"[^0-9.]", "", item["value"])
                            continue

                        # get info for Layer 2 (EPE)
                        # elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink\b)(?=.*\bcontainer\sid\b).*", item["name"], re.IGNORECASE):
                        elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink_1\b)(?=.*\bcontainer\sid\b).*", item["name"], re.IGNORECASE):
                            payload_epe['node_name'] = item["value"]
                            continue
                        # elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink\b)(?=.*\binterface\sname\b).*", item["name"], re.IGNORECASE):
                        elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink_1\b)(?=.*\binterface\sname\b).*", item["name"], re.IGNORECASE):
                            payload_epe['interface'] = item["value"]
                            continue
                        elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink_2\b)(?=.*\bcontainer\sid\b).*", item["name"], re.IGNORECASE):
                            if 'payload_epe_2' in locals():
                                payload_epe_2['node_name'] = item["value"]
                            else:
                                payload_epe_2 = payload_epe.copy()
                                payload_epe_2['node_name'] = item["value"]
                            continue
                        elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink_2\b)(?=.*\binterface\sname\b).*", item["name"], re.IGNORECASE):
                            if 'payload_epe_2' in locals():
                                payload_epe_2['interface'] = item["value"]
                            else:
                                payload_epe_2 = payload_epe.copy()
                                payload_epe_2['interface'] = item["value"]
                            continue
                        elif re.search(r"(?=.*\bepe\b)(?=.*\bdownlink\b)(?=.*\bdevice\sid\b).*", item["name"], re.IGNORECASE):
                            payload_epe['loopback_ip'] = re.sub(r"[^0-9.]", "", item["value"])
                            continue

                        # get info for Layer 2 (UPE)
                        elif re.search(r"(?=.*\bupe\b)(?=.*\bdownlink\b)(?=.*\bcontainer\sid\b).*", item["name"], re.IGNORECASE):
                        # elif item["name"] == "UPE ID":
                            payload_epe['sys_name'] = item["value"]
                            if 'payload_epe_2' in locals() and not 'sys_name' in payload_epe_2:
                                payload_epe_2['sys_name'] = item["value"]
                            continue

                        elif item["name"] == "UPE ID":
                            payload_epe['sys_name'] = item["value"]
                            if 'payload_epe_2' in locals() and not 'sys_name' in payload_epe_2:
                                payload_epe_2['sys_name'] = item["value"]
                            continue

                    # if l3_service_ce is not set, set it to value 'unmanaged'
                    if not 'l3_service_ce' in payload_pe:
                        payload_pe['l3_service_ce'] = 'unmanaged'
                        payload_pe['cpe_type'] = 'others'

                    # For Testing
                    # test_output = list()
                    # test_output.append(payload_pe)
                    # test_output.append(payload_npe)
                    # test_output.append(payload_epe)

                    # if 'payload_epe_2' in locals():
                    #     test_output.append(payload_epe_2)

                    # return Response(test_output)

                    # for Type 2 and 4 Huawei, Type 4 Nokia and Type 4 ZTE
                    if ('payload_epe_2' in locals() and re.search(r'hw', payload_epe_2['node_name'], re.IGNORECASE)) or ('payload_epe_2' in locals() and re.search(r'zt|al', payload_epe_2['node_name'], re.IGNORECASE) and payload_epe_2['node_name'] != payload_epe['node_name']):
                        api_details = [
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu_with_no_ce/layer3',
                                'payload': payload_pe,
                                'token': token
                            },
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu/layer2',
                                'payload': payload_npe,
                                'token': token
                            },
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu/layer2',
                                'payload': payload_epe,
                                'token': token
                            },
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu/layer2',
                                'payload': payload_epe_2,
                                'token': token
                            }
                        ]

                        # create a thread pool executor with 5 worker threads
                        executor = ThreadPoolExecutor(max_workers=4)

                        # submit the requests to the executor and get the results
                        results = list(executor.map(self.invoke_api, api_details))

                        # 1. Success, Success, Success, Success
                        if results[0]['status'] == 'success' and results[1]['status'] == 'success' and results[2]['status'] == 'success' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at PE, NPE, EPE (interface 1) and EPE (interface 2)'
                            api_response['status'] = 'success'
                            api_response['message'] = output_message
                            api_response['data'] = {
                                'pe_output': results[0]['message'],
                                'npe_output': results[1]['message'],
                                'epe_output_interface_1': results[2]['message'],
                                'epe_output_interface_2': results[3]['message'],
                            }
                            return Response(api_response, status=status.HTTP_200_OK)

                        # 2. Success, Success, Success, Failed
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'success' and results[2]['status'] == 'success' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at PE, NPE, EPE (interface 1) but failed at EPE (interface 2)'

                        # 3. Success, Success, Failed, Success
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'success' and results[2]['status'] == 'failed' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at PE, NPE, EPE (interface 2) but failed at EPE (interface 1)'

                        # 4. Success, Success, Failed, Failed
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'success' and results[2]['status'] == 'failed' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at PE, NPE but failed at EPE (interface 1 and interface 2)'

                        # 5. Success, Failed, Success, Success
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'failed' and results[2]['status'] == 'success' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at PE and EPE (interface 1 and interface 2) but failed at NPE'

                        # 6. Success, Failed, Success, Failed
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'failed' and results[2]['status'] == 'success' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at PE and EPE (interface 1) but failed at NPE and EPE (interface 2)'

                        # 7. Success, Failed, Failed, Success
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'failed' and results[2]['status'] == 'failed' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at PE and EPE (interface 2) but failed at NPE and EPE (interface 1)'

                        # 8. Success, Failed, Failed, Failed
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'failed' and results[2]['status'] == 'failed' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at PE but failed at NPE and EPE (interface 1 and interface 2)'

                        # 9. Failed, Success, Success, Success
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'success' and results[2]['status'] == 'success' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at NPE, EPE (interface 1), and EPE (interface 2) but failed at PE'

                        # 10. Failed, Success, Success, Failed
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'success' and results[2]['status'] == 'success' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at NPE, EPE (interface 1) but failed at PE and EPE (interface 2)'

                        # 11. Failed, Success, Failed, Success
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'success' and results[2]['status'] == 'failed' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at NPE and EPE (interface 2) but failed at PE and EPE (interface 1)'

                        # 12. Failed, Success, Failed, Failed
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'success' and results[2]['status'] == 'failed' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at NPE but failed at PE and EPE (interface 1 and interface 2)'

                        # 13. Failed, Failed, Success, Success
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'failed' and results[2]['status'] == 'success' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at EPE (interface 1) and EPE (interface 2) but failed at PE and NPE'

                        # 14. Failed, Failed, Success, Failed
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'failed' and results[2]['status'] == 'success' and results[3]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at EPE (interface 1) but failed at PE, NPE, and EPE (interface 2)'

                        # 15. Failed, Failed, Failed, Success
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'failed' and results[2]['status'] == 'failed' and results[3]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at EPE (interface 2) but failed at PE, NPE, and EPE (interface 1)'

                        # 16. Failed, Failed, Failed, Failed
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'failed' and results[2]['status'] == 'failed' and results[3]['status'] == 'failed':
                            output_message = 'Failed to provision new bandwidth at all PE, NPE, and EPE (interface 1 and interface 2)'

                        api_response['status'] = 'failed'
                        api_response['message'] = output_message
                        api_response['data'] = {
                            'pe_output': results[0]['message'],
                            'npe_output': results[1]['message'],
                            'epe_output_interface_1': results[2]['message'],
                            'epe_output_interface_2': results[3]['message'],
                        }
                        return Response(api_response, status=status.HTTP_400_BAD_REQUEST)

                    else:       # other TYPE
                        api_details = [
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu_with_no_ce/layer3',
                                'payload': payload_pe,
                                'token': token
                            },
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu/layer2',
                                'payload': payload_npe,
                                'token': token
                            },
                            {
                                'url': 'http://localhost:8000/workflow/v1/ipvpn/tbu/layer2',
                                'payload': payload_epe,
                                'token': token
                            }
                        ]

                        # create a thread pool executor with 5 worker threads
                        executor = ThreadPoolExecutor(max_workers=3)

                        # submit the requests to the executor and get the results
                        results = list(executor.map(self.invoke_api, api_details))

                        # Success, Success, Success
                        if results[0]['status'] == 'success' and results[1]['status'] == 'success' and results[2]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at PE, NPE and EPE'
                            api_response['status'] = 'success'
                            api_response['message'] = output_message
                            api_response['data'] = {
                                'pe_output': results[0]['message'],
                                'npe_output': results[1]['message'],
                                'epe_output': results[2]['message'],
                            }
                            return Response(api_response,status=status.HTTP_200_OK)

                        # Success, Success, Failure
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'success' and results[2]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at PE, NPE but failed at EPE'

                        # Success, Failure, Success
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'failed' and results[2]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at PE, EPE but failed at NPE'

                        # Success, Failure, Failure
                        elif results[0]['status'] == 'success' and results[1]['status'] == 'failed' and results[2]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at PE but failed at NPE and EPE'

                        # Failure, Success, Success
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'success' and results[2]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at NPE and EPE but failed at PE'

                        # Failure, Success, Failure
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'success' and results[2]['status'] == 'failed':
                            output_message = 'Successfully provisioned new bandwidth at NPE but failed at PE and EPE'

                        # Failure, Failure, Success
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'failed' and results[2]['status'] == 'success':
                            output_message = 'Successfully provisioned new bandwidth at EPE but failed at PE and NPE'

                        # Failure, Failure, Failure
                        elif results[0]['status'] == 'failed' and results[1]['status'] == 'failed' and results[2]['status'] == 'failed':
                            output_message = 'Failed to provision new bandwidth at PE, NPE and EPE'

                        api_response['status'] = 'failed'
                        api_response['message'] = output_message
                        api_response['data'] = {
                            'pe_output': results[0]['message'],
                            'npe_output': results[1]['message'],
                            'epe_output': results[2]['message'],
                        }
                        return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                
                else:
                    # if info returned is incomplete
                    output_message = 'Incomplete information returned from Granite API'
                    api_response['status'] = 'failed'
                    api_response['message'] = output_message
                    api_response['data'] = output['NIGResponse']
                    return Response(api_response,status=status.HTTP_400_BAD_REQUEST)
                    
            else:
                # if service is not exist
                output_message = output['NIGResponse']['ErrorMesage']
                api_response['status'] = 'failed'
                api_response['message'] = output_message
                api_response['data'] = output['NIGResponse']
                return Response(api_response,status=status.HTTP_400_BAD_REQUEST)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            error_message = 'Unexpected error has occured while executing API'
            output_message = error_message + \
                '\n[error message: {}]'.format(error)
            log_text(filename=log_file, content=output_message)
            api_response['status'] = 'failed'
            api_response['message'] = error_message
            api_response['data'] = output_error
            return Response(api_response, status=status.HTTP_400_BAD_REQUEST)


    # define a function to invoke API
    def invoke_api(self, info):
        response = requests.post(
            info['url'], 
            data=info['payload'], 
            verify=False, 
            headers={ 
                'Authorization': info['token'],
            }
        )
        return response.json()