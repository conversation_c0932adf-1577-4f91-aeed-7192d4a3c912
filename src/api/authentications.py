from rest_framework import generics
from .serializers import UserSerializer
from rest_framework.views import APIView
from django.contrib.auth import authenticate
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from django.contrib.auth.models import User

class RegisterUser(generics.ListCreateAPIView):
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()         #exclude from global authentication
    queryset = User.objects.all()
    serializer_class = UserSerializer

class GetToken(APIView):
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        request_body=UserSerializer,
        responses={201: UserSerializer()},
    ) 

    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")
        user = authenticate(username=username, password=password)
        if user:
            return Response({"token": user.auth_token.key})
        else:
            return Response({"error": "Wrong Credentials"}, status=status.HTTP_400_BAD_REQUEST)