from rest_framework.views import APIView  # activate REST //post,get,put,delete
# return Response JSON associative array
from rest_framework.response import Response
from rest_framework import status, generics  # status code and DRF view
from api.models import AuditTrail, NetworkElement, User5GBot, CsrInfo
from api.serializers import AuditTrailSerializer, GetLogSerializer, NetworkElementSerializer, User5GBotSerializer, \
                            OggQueryActivitiesSerializer, OggQueryLineAttributeSerializer, OggQueryLineItemSerializer, \
                            OggQueryPerActivityIdSerializer, CsrInfoSerializer, OggQueryUsingServiceNumSerializer
from drf_yasg.utils import swagger_auto_schema
import math, sys
from datetime import datetime
from django.db import connections
from django.conf import settings
import cx_Oracle



class GetLogData(APIView):
    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    def get(self, request, format=None):
        data = AuditTrail.objects.all()
        serializer = AuditTrailSerializer(data, many=True)
        return Response(serializer.data)


class GetLogDataLatest(APIView):
    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=GetLogSerializer,
        operation_summary="Get log details based on selected year"
    )
    def get(self, request):

        # verify passed data
        input_data = GetLogSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:
                # Initialize variables using the passed data
                serialized_data = input_data.data
                if serialized_data['year'] == 'All':
                    data = AuditTrail.objects.all()
                else:
                    data = AuditTrail.objects.filter(
                        updated__year=serialized_data['year']).order_by('-updated')[:5000]
                serializer = AuditTrailSerializer(data, many=True)
                return Response(serializer.data)
            except Exception as error:
                return Response(str(error))


class NetworkElementApi(generics.GenericAPIView):
    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    serializer_class = NetworkElementSerializer
    queryset = NetworkElement.objects.all()

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Retrieve list of network element"
    )

    def get(self, request):
        page_num = int(request.GET.get("page", 1))
        limit_num = int(request.GET.get("limit", 10))
        start_num = (page_num - 1) * limit_num
        end_num = limit_num * page_num
        search_param = request.GET.get("search")
        ne = NetworkElement.objects.all()
        total_ne = ne.count()
        if search_param:
            ne = ne.filter(hostname__icontains=search_param)
        serializer = self.serializer_class(ne[start_num:end_num], many=True)
        return Response({
            "status": "success",
            "total": total_ne,
            "page": page_num,
            "last_page": math.ceil(total_ne / limit_num),
            "network_element": serializer.data
        })

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Create a new network element"
    )

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({"status": "success", "network_element": serializer.data}, status=status.HTTP_201_CREATED)
        else:
            return Response({"status": "fail", "message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

class NetworkElementBulkDetailApi(generics.CreateAPIView):

    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    queryset = NetworkElement.objects.all()  
    serializer_class = NetworkElementSerializer  

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Register bulk of new network element"
    )
    
    def create(self, request, *args, **kwargs):  
        serializer = self.serializer_class(data=request.data, many=True)  
        serializer.is_valid(raise_exception=True)  
    
        try:  
            self.perform_create(serializer)  
            return Response({"status": "success", "network_element": serializer.data}, status=status.HTTP_201_CREATED)
        except:  
            return Response({"status": "fail", "message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class NetworkElementDetailApi(generics.GenericAPIView):

    # authentication_classes = ()  # exclude from global authentication
    # permission_classes = ()

    serializer_class = NetworkElementSerializer
    queryset = NetworkElement.objects.all()

    def get_network_element(self, pk):
        try:
            return NetworkElement.objects.get(pk=pk)
        except:
            return None

    # @swagger_auto_schema(
    #     # query_serializer=NetworkElementSerializer,
    #     operation_summary="Get network element details"
    # )

    def get(self, request, pk):
        network_element = self.get_network_element(pk=pk)
        if network_element == None:
            return Response({"status": "fail", "message": f"Network Element with Id: {pk} not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(network_element)
        return Response({"status": "success", "network_element": serializer.data})

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Modify network element details"
    )

    def patch(self, request, pk):
        network_element = self.get_network_element(pk)
        if network_element == None:
            return Response({"status": "fail", "message": f"Network Element with Id: {pk} not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(
            network_element, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.validated_data['updatedAt'] = datetime.now()
            serializer.save()
            return Response({"status": "success", "network_element": serializer.data})
        return Response({"status": "fail", "message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Delete network element"
    )

    def delete(self, request, pk):
        network_element = self.get_network_element(pk)
        if network_element == None:
            return Response({"status": "fail", "message": f"Network Element with Id: {pk} not found"}, status=status.HTTP_404_NOT_FOUND)

        network_element.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
# User 5G Bot
class User5GBotListCreateAPIView(generics.ListCreateAPIView):

    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    queryset = User5GBot.objects.all()
    serializer_class = User5GBotSerializer


class User5GBotRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):

    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    queryset = User5GBot.objects.all()
    serializer_class = User5GBotSerializer
    lookup_field = 'chat_id' # Use chat_id as lookup field instead of the default 'pk'


# get log data based on page
class LogList(generics.ListAPIView):
    serializer_class = AuditTrailSerializer
    queryset = AuditTrail.objects.all().order_by('-id')

    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="List PISA log with pages and search features"
    )

    def get_queryset(self):
        # Get query parameters
        page_number = int(self.request.query_params.get('page', 1))
        page_size = int(self.request.query_params.get('page_size', 10))
        search_query = self.request.query_params.get('search', None)

        # Apply pagination
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        queryset = AuditTrail.objects.all().order_by('-id')[start_index:end_index]

        # Apply search filter
        if search_query is not None:
            queryset = queryset.filter(name__icontains=search_query)

        return queryset

#  OGG Database
class OggQueryActivitiesDetails(APIView):
    
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryActivitiesSerializer,
        operation_summary="Get Activities Details"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryActivitiesSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Query Activity Details with status In Progress
                if serialized_data['soc_group'] == 'IP TRANSIT':
                    query = "select act.activity_uid \"ActivityId\", so.order_num \"OrderNum\", sot.name \"OrderType\", prod.name \"Product\", so.STATUS_CD \"OrderStatus\", act.todo_cd \"Type\", act.evt_stat_cd \"ActivityStatus\", soi.service_num \"ServiceNum\", soe.name \"Account\", act.act_created_dt+1/3 \"Created\", ownercontact.fst_name || ' ' || ownercontact.last_name \"Owner\", ownerpostn.name \"OwnersPosition\" from assurance_nova.s_evt_act act left join assurance_nova.s_contact ownercontact on (act.owner_per_id=ownercontact.row_id) left join assurance_nova.s_postn ownerpostn on (ownercontact.pr_held_postn_id=ownerpostn.row_id) left join assurance_nova.s_org_ext soe on (act.target_ou_id=soe.row_id) left join assurance_nova.s_order so on (act.order_id=so.row_id) left join assurance_nova.s_order_type sot on (so.order_type_id=sot.row_id) left join assurance_nova.s_order_item soi on (act.ORDER_ITEM_ID=soi.row_id) LEFT JOIN assurance_nova.s_prod_int prod on (soi.prod_id=prod.row_id) where act.todo_cd IN ('Activate VAS','IPT Activate VAS AntiDDOS', 'IPT Deactivate VAS AntiDDOS') AND act.evt_stat_cd not in ('Done','Cancelled','Not Attended','Returned', 'Scheduled', 'Unscheduled') AND prod.name IN ('IP Transit Clean Pipe', 'IP Transit Clean Pipe Customized') and act.act_created_dt is not null order by act.row_id"
                if serialized_data['soc_group'] == 'WIA':
                    query = "select act.activity_uid \"ActivityId\", so.order_num \"OrderNum\", sot.name \"OrderType\", prod.name \"Product\", so.STATUS_CD \"OrderStatus\", act.todo_cd \"Type\", act.evt_stat_cd \"ActivityStatus\", soi.service_num \"ServiceNum\", soe.name \"Account\", act.act_created_dt+1/3 \"Created\", ownercontact.fst_name || ' ' || ownercontact.last_name \"Owner\", ownerpostn.name \"OwnersPosition\" from assurance_nova.s_evt_act act left join assurance_nova.s_contact ownercontact on (act.owner_per_id=ownercontact.row_id) left join assurance_nova.s_postn ownerpostn on (ownercontact.pr_held_postn_id=ownerpostn.row_id) left join assurance_nova.s_org_ext soe on (act.target_ou_id=soe.row_id) left join assurance_nova.s_order so on (act.order_id=so.row_id) left join assurance_nova.s_order_type sot on (so.order_type_id=sot.row_id) left join assurance_nova.s_order_item soi on (act.ORDER_ITEM_ID=soi.row_id) LEFT JOIN assurance_nova.s_prod_int prod on (soi.prod_id=prod.row_id) where act.todo_cd IN ('WIA-Activate VAS','WIA-Deactivate VAS') AND act.evt_stat_cd not in ('Done','Cancelled','Not Attended','Returned', 'Scheduled', 'Unscheduled') AND prod.name IN ('WIA AntiDDOS', 'WIA AntiDDOS Customized') and act.act_created_dt is not null order by act.row_id"
                if serialized_data['soc_group'] == 'DIRECT':
                    query = "select act.activity_uid \"ActivityId\", so.order_num \"OrderNum\", sot.name \"OrderType\", prod.name \"Product\", so.STATUS_CD \"OrderStatus\", act.todo_cd \"Type\", act.evt_stat_cd \"ActivityStatus\", soi.service_num \"ServiceNum\", soe.name \"Account\", act.act_created_dt+1/3 \"Created\", ownercontact.fst_name || ' ' || ownercontact.last_name \"Owner\", ownerpostn.name \"OwnersPosition\" from assurance_nova.s_evt_act act left join assurance_nova.s_contact ownercontact on (act.owner_per_id=ownercontact.row_id) left join assurance_nova.s_postn ownerpostn on (ownercontact.pr_held_postn_id=ownerpostn.row_id) left join assurance_nova.s_org_ext soe on (act.target_ou_id=soe.row_id) left join assurance_nova.s_order so on (act.order_id=so.row_id) left join assurance_nova.s_order_type sot on (so.order_type_id=sot.row_id) left join assurance_nova.s_order_item soi on (act.ORDER_ITEM_ID=soi.row_id) LEFT JOIN assurance_nova.s_prod_int prod on (soi.prod_id=prod.row_id) where act.todo_cd IN ('TMD-Confirm VAS Acceptance') AND act.evt_stat_cd not in ('Done', 'Cancelled', 'Not Attended', 'Returned', 'Scheduled', 'Unscheduled') AND prod.name IN ('Standard Direct Package - DOME','Platinum Direct Package - DOME','Silver Direct Package - DOME','Gold Direct Package - DOME','Customized Direct Package','Direct Standard Plus Package') order by act.row_id"
                    # query = "select act.activity_uid \"ActivityId\", so.order_num \"OrderNum\", sot.name \"OrderType\", prod.name \"Product\", so.STATUS_CD \"OrderStatus\", act.todo_cd \"Type\", act.evt_stat_cd \"ActivityStatus\", soi.service_num \"ServiceNum\", soe.name \"Account\", act.act_created_dt+1/3 \"Created\", ownercontact.fst_name || ' ' || ownercontact.last_name \"Owner\", ownerpostn.name \"OwnersPosition\" from assurance_nova.s_evt_act act left join assurance_nova.s_contact ownercontact on (act.owner_per_id=ownercontact.row_id) left join assurance_nova.s_postn ownerpostn on (ownercontact.pr_held_postn_id=ownerpostn.row_id) left join assurance_nova.s_org_ext soe on (act.target_ou_id=soe.row_id) left join assurance_nova.s_order so on (act.order_id=so.row_id) left join assurance_nova.s_order_type sot on (so.order_type_id=sot.row_id) left join assurance_nova.s_order_item soi on (act.ORDER_ITEM_ID=soi.row_id) LEFT JOIN assurance_nova.s_prod_int prod on (soi.prod_id=prod.row_id) where act.todo_cd IN ('Slot','TMD-Configure VAS') AND act.evt_stat_cd not in ('Done', 'Cancelled', 'Not Attended', 'Returned', 'Scheduled', 'Unscheduled') AND prod.name IN ('DDoS Defence','Standard Direct Package - DOME') order by act.row_id"

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = dict()
                data_list = list()

                for row in results:
                    # return Response({"status": "success", "output": row[0]})
                    data_dict = dict()
                    data_dict['ActivityId'] = row[0]
                    data_dict['OrderNum'] = row[1]
                    data_dict['OrderType'] = row[2]
                    data_dict['Product'] = row[3]
                    data_dict['OrderStatus'] = row[4]
                    data_dict['Type'] = row[5]
                    data_dict['ActivityStatus'] = row[6]
                    data_dict['ServiceNum'] = row[7]
                    data_dict['Account'] = row[8]
                    data_dict['Created'] = row[9]
                    data_dict['Owner'] = row[10]
                    data_dict['OwnersPosition'] = row[11]
                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({"status": "success", "output": data_list})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OggQueryCustomerDetails(APIView):
    
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryLineItemSerializer,
        operation_summary="Get Customer Details"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryLineItemSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Customer information Details
                query = f"select ord.order_num \"Order #\", accnt.name \"Account\", contact.last_name \"Contact Name\", contact.cell_ph_num \"Contact Phone #\", contact.email_addr \"Contact Email Address\", csm.fst_name || ' ' || csm.last_name \"CSM / iDFM Representative Name\", csm.EMAIL_ADDR \"CSM Email Address\" from assurance_nova.s_order ord inner join assurance_nova.s_order_x ordx on (ord.row_id=ordx.par_row_id) inner join assurance_nova.s_org_ext accnt on (ord.accnt_id=accnt.row_id) left join assurance_nova.s_contact contact on (ord.contact_id=contact.row_id) left join assurance_nova.s_contact csm on (ordx.attrib_43=csm.row_id) where ord.order_num='{serialized_data['order_no']}'"

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = dict()
                data_list = list()

                for row in results:
                    data_dict = dict()
                    data_dict['Order'] = row[0]
                    data_dict['Account'] = row[1]
                    data_dict['ContactName'] = row[2]
                    data_dict['ContactPhone'] = row[3]
                    data_dict['ContactEmailAddress'] = row[4]
                    data_dict['CSM_iDFM_RepresentativeName'] = row[5]
                    data_dict['CSM_iDFM_EmailAddress'] = row[6]
                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({"status": "success", "output": data_list})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OggQueryLineAttributeDetails(APIView):
    
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryLineAttributeSerializer,
        operation_summary="Get Line Attribute Details"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryLineAttributeSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Line Attribute
                query = f"select att.attr_name \"Name\", att.char_val \"Value\", att.action_cd \"Action\" from assurance_nova.s_order_item_xa att where order_item_id='{serialized_data['order_item_id']}'"

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = dict()
                data_list = list()

                for row in results:
                    data_dict = dict()
                    data_dict['Name'] = row[0]
                    data_dict['Value'] = row[1]
                    data_dict['Action'] = row[2]
                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({"status": "success", "output": data_list})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OggQueryLineItemsDetails(APIView):
    
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryLineItemSerializer,
        operation_summary="Get Line Items Details"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryLineItemSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Line Items
                query = f"select ord.order_num \"Order #\",ordi.row_id \"order_item_row_id\",prod.name \"Product\",ordi.status_cd \"Status\",ordi.qty_req \"Qty\",ordi.SERVICE_NUM \"ServiceNumber\" from assurance_nova.s_order ord inner join assurance_nova.s_order_item ordi on (ord.row_id=ordi.order_id) inner join assurance_nova.s_prod_int prod on (ordi.prod_id=prod.row_id) where ord.order_num='{serialized_data['order_no']}'"

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = dict()
                data_list = list()

                for row in results:
                    data_dict = dict()
                    data_dict['order_number'] = row[0]
                    data_dict['order_item_row_id'] = row[1]
                    data_dict['product'] = row[2]
                    data_dict['status'] = row[3]
                    data_dict['quantity'] = row[4]
                    data_dict['servicenumber'] = row[5]
                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({"status": "success", "output": data_list})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OggQueryPerActivityId(APIView):
    
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryPerActivityIdSerializer,
        operation_summary="Get Per Activity Id"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryPerActivityIdSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Query Activity Details with status In Progress
                query = f"select act.activity_uid \"ActivityId\", so.order_num \"OrderNum\", sot.name \"OrderType\", prod.name \"Product\", so.STATUS_CD \"OrderStatus\", act.todo_cd \"Type\", act.evt_stat_cd \"ActivityStatus\", soi.service_num \"ServiceNum\", soe.name \"Account\", act.act_created_dt+1/3 \"Created\", ownercontact.fst_name || ' ' || ownercontact.last_name \"Owner\", ownerpostn.name \"OwnersPosition\" from assurance_nova.s_evt_act act left join assurance_nova.s_contact ownercontact on (act.owner_per_id=ownercontact.row_id) left join assurance_nova.s_postn ownerpostn on (ownercontact.pr_held_postn_id=ownerpostn.row_id) left join assurance_nova.s_org_ext soe on (act.target_ou_id=soe.row_id) left join assurance_nova.s_order so on (act.order_id=so.row_id) left join assurance_nova.s_order_type sot on (so.order_type_id=sot.row_id) left join assurance_nova.s_order_item soi on (act.ORDER_ITEM_ID=soi.row_id) LEFT JOIN assurance_nova.s_prod_int prod on (soi.prod_id=prod.row_id) where act.activity_uid = '{serialized_data['activity_id']}' ORDER BY act.row_id"

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = dict()
                data_list = list()

                for row in results:
                    # return Response({"status": "success", "output": row[0]})
                    data_dict = dict()
                    data_dict['ActivityId'] = row[0]
                    data_dict['OrderNum'] = row[1]
                    data_dict['OrderType'] = row[2]
                    data_dict['Product'] = row[3]
                    data_dict['OrderStatus'] = row[4]
                    data_dict['Type'] = row[5]
                    data_dict['ActivityStatus'] = row[6]
                    data_dict['ServiceNum'] = row[7]
                    data_dict['Account'] = row[8]
                    data_dict['Created'] = row[9]
                    data_dict['Owner'] = row[10]
                    data_dict['OwnersPosition'] = row[11]
                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({"status": "success", "output": data_list})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OggQueryAllActivities(APIView):
    
    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryLineItemSerializer,
        operation_summary="Get All Activities"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryLineItemSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:
            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Query Activity Details with status In Progress
                query = f"select act.activity_uid \"ActivityId\", so.order_num \"OrderNum\", sot.name \"OrderType\", prod.name \"Product\", so.STATUS_CD \"OrderStatus\", act.todo_cd \"Type\", act.evt_stat_cd \"ActivityStatus\", soi.service_num \"ServiceNum\", soe.name \"Account\", act.act_created_dt+1/3 \"Created\", ownercontact.fst_name || ' ' || ownercontact.last_name \"Owner\", ownerpostn.name \"OwnersPosition\" from assurance_nova.s_evt_act act left join assurance_nova.s_contact ownercontact on (act.owner_per_id=ownercontact.row_id) left join assurance_nova.s_postn ownerpostn on (ownercontact.pr_held_postn_id=ownerpostn.row_id) left join assurance_nova.s_org_ext soe on (act.target_ou_id=soe.row_id) left join assurance_nova.s_order so on (act.order_id=so.row_id) left join assurance_nova.s_order_type sot on (so.order_type_id=sot.row_id) left join assurance_nova.s_order_item soi on (act.ORDER_ITEM_ID=soi.row_id) LEFT JOIN assurance_nova.s_prod_int prod on (soi.prod_id=prod.row_id) where so.order_num = '{serialized_data['order_no']}' ORDER BY act.row_id"

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = dict()
                data_list = list()

                for row in results:
                    # return Response({"status": "success", "output": row[0]})
                    data_dict = dict()
                    data_dict['ActivityId'] = row[0]
                    data_dict['OrderNum'] = row[1]
                    data_dict['OrderType'] = row[2]
                    data_dict['Product'] = row[3]
                    data_dict['OrderStatus'] = row[4]
                    data_dict['Type'] = row[5]
                    data_dict['ActivityStatus'] = row[6]
                    data_dict['ServiceNum'] = row[7]
                    data_dict['Account'] = row[8]
                    data_dict['Created'] = row[9]
                    data_dict['Owner'] = row[10]
                    data_dict['OwnersPosition'] = row[11]
                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({"status": "success", "output": data_list})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OGGRetrieveCustomerName(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=OggQueryUsingServiceNumSerializer,
        operation_summary="Retrieve customer name"
    )

    def get(self, request):

        # verify passed data
        input_data = OggQueryUsingServiceNumSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:

            try:

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['oracle']['HOST'],
                    settings.DATABASES['oracle']['PORT'],
                    service_name=settings.DATABASES['oracle']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['oracle']['USER'],
                    password=settings.DATABASES['oracle']['PASSWORD'],
                    dsn=dsn
                )

                # # Initialize variables using the passed data
                serialized_data = input_data.data

                cursor = conn.cursor()

                # Query Activity Details with status In Progress
                query = f"""
                    SELECT
                        soe.name " Account"
                FROM
                        assurance_nova.s_evt_act act 
                LEFT JOIN assurance_nova.s_contact ownercontact ON
                        (act.owner_per_id = ownercontact.row_id) 
                LEFT JOIN assurance_nova.s_postn ownerpostn ON
                        (ownercontact.pr_held_postn_id = ownerpostn.row_id) 
                LEFT JOIN assurance_nova.s_org_ext soe ON
                        (act.target_ou_id = soe.row_id) 
                LEFT JOIN assurance_nova.s_order so ON
                        (act.order_id = so.row_id) 
                LEFT JOIN assurance_nova.s_order_type sot ON
                        (so.order_type_id = sot.row_id)
                LEFT JOIN assurance_nova.s_order_item soi ON
                        (act.ORDER_ITEM_ID = soi.row_id) 
                LEFT JOIN assurance_nova.s_prod_int prod ON
                        (soi.prod_id = prod.row_id) 
                LEFT JOIN assurance_nova.s_order_item_x soix ON
                        (soix.par_row_id = soi.root_order_item_id)
                WHERE
                    soi.service_num = '{serialized_data['service_number']}'
                ORDER BY
                        act.row_id"""

                cursor.execute(query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_dict = {}  # Initialize an empty dictionary
                for row in results:
                    if row[0] is not None:
                        data_dict = {'customer_name': row[0]}  # Assuming you meant to access index 0, not 8
                        break  # Stop the loop once data is found

                # Close the cursor and connection
                cursor.close()
                conn.close()

                if data_dict:  # Check if data_dict is not empty
                    return Response({"status": "success", "output": data_dict})
                else:
                    return Response({"status": "success", "output": 'no data found in db'})


            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OGGRetrieveAllActivityInProgress(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        # request_body=OggQueryAllActivitiesInProgressProductName,
        operation_summary="Retrieve in progress activity"
    )

    def get(self, request):

        # verify passed data
        # input_data = OggQueryAllActivitiesInProgressProductName(data=request.data)
        # input_data.is_valid()

        # if len(input_data.errors) > 0:  # check if passed data contains error and return error message
        #     return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        # else:

        try:

            dsn = cx_Oracle.makedsn(
                settings.DATABASES['oracle']['HOST'],
                settings.DATABASES['oracle']['PORT'],
                service_name=settings.DATABASES['oracle']['NAME']
            )
            conn = cx_Oracle.connect(
                user=settings.DATABASES['oracle']['USER'],
                password=settings.DATABASES['oracle']['PASSWORD'],
                dsn=dsn
            )

            # Initialize variables using the passed data
            # serialized_data = input_data.validated_data

            # Generate the comma-separated list of product names
            # product_names = ','.join(["'{}'".format(name) for name in serialized_data['product_name']])

            cursor = conn.cursor()

            query = """
            SELECT
                so.order_num AS "Order Number",
                soi.service_num AS "Service Id",
                prod.name AS "Product Name",
                act.todo_cd AS "Activity Name",
                act.act_created_dt + INTERVAL '8' HOUR AS "Activity Created",
                CASE
                    WHEN act.todo_actl_end_dt IS NULL THEN 
                        TRUNC(SYSDATE) - TRUNC(act.act_created_dt + INTERVAL '8' HOUR)
                    ELSE 
                        TRUNC(act.todo_actl_end_dt) - TRUNC(act.act_created_dt + INTERVAL '8' HOUR)
                END AS "Aging",
                act.evt_stat_cd AS "Activity Status",
                sot.name AS "Order Type",
                soe.name AS "Account",
                so.STATUS_CD AS "Order Status",
                ownercontact.fst_name || ' ' || ownercontact.last_name " Owner",
                ownerpostn.name " OwnersPosition",
                soe.X_SEGMENT_SUBGRP "Segment Subgroup"
                FROM
                    assurance_nova.s_evt_act act
                LEFT JOIN assurance_nova.s_contact ownercontact ON
                    act.owner_per_id = ownercontact.row_id
                LEFT JOIN assurance_nova.s_postn ownerpostn ON
                    ownercontact.pr_held_postn_id = ownerpostn.row_id
                LEFT JOIN assurance_nova.s_org_ext soe ON
                    act.target_ou_id = soe.row_id
                LEFT JOIN assurance_nova.s_order so ON
                    act.order_id = so.row_id
                LEFT JOIN assurance_nova.s_order_type sot ON
                    so.order_type_id = sot.row_id
                LEFT JOIN assurance_nova.s_order_item soi ON
                    act.ORDER_ITEM_ID = soi.row_id
                LEFT JOIN assurance_nova.s_prod_int prod ON
                    soi.prod_id = prod.row_id
                WHERE
                    so.STATUS_CD IN ('Processing')   
                    AND act.evt_stat_cd = 'In Progress'
                    AND prod.name IN (
                        'NID - Primary', 'NID - Backup', 
                        'UPE - Primary', 'UPE - Backup', 
                        'Primary UPE Interface', 
                        'Primary UPE Interface - Metro Ethernet', 
                        'Backup UPE Interface - Metro Ethernet', 
                        'Metro-E Leg', 
                        'Metro-E Leg - Customized', 
                        'Metro-E Service', 
                        'Primary Metro-E Service', 
                        'Backup Metro-E Service', 
                        'Direct', 
                        'Direct Standard Plus Package', 
                        'Customized Direct Package', 
                        'Standard Direct Package - DOME', 
                        'Platinum Direct Package - DOME', 
                        'Gold Direct Package - DOME', 
                        'Silver Direct Package - DOME', 
                        'Primary Direct Access (Metro Ethernet)', 
                        'Primary Direct Service (Metro Ethernet)', 
                        'Primary Customized Direct Access (Metro Ethernet)', 
                        'Primary Customized Direct Service (Metro Ethernet)', 
                        'Secondary Direct Access (Metro Ethernet)', 
                        'Secondary Direct Service (Metro Ethernet)', 
                        'Secondary Customized Direct Service (Metro Ethernet)', 
                        'Primary IPVPN Access (Metro Ethernet)', 
                        'IPVPN Premier Standard Plus Leg',
                        'IPVPN',
                        'IPVPN Premier Leg',
                        'IPVPN Premier Standard Plus Leg',
                        'IPVPN Premier Customized Leg',
                        'IPVPN Lite Leg',
                        'IPVPN Lite Standard Plus Leg',
                        'IPVPN Value Standard Plus Leg',
                        'Primary IPVPN Access (Metro Ethernet)',
                        'Primary IPVPN Access (EFM-HSBB)',
                        'Primary IPVPN Access (Leased Line)',
                        'Primary IPVPN Service (Metro Ethernet)',
                        'Primary IPVPN Service (EFM-HSBB)',
                        'Primary IPVPN Service (Leased Line)',
                        'Primary IPVPN Service (ADSL)',
                        'Primary IPVPN Service (Cellular)',
                        'Primary Customized IPVPN Access (Metro Ethernet)',
                        'Primary Customized IPVPN Service (Metro Ethernet)',
                        'Primary Customized IPVPN Access (Leased Line)',
                        'Primary Customized IPVPN Service (Leased Line)',
                        'Primary Customized IPVPN Value Access (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Value Service (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Lite Access (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Lite Service (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Lite Service (ADSL)',
                        'Primary Customized IPVPN Lite Service (Cellular)',
                        'Primary Customized IPVPN Value Service (ADSL)',
                        'Secondary IPVPN Access (Metro Ethernet)',
                        'Secondary IPVPN Access (EFM-HSBB)',
                        'Secondary IPVPN Service (Metro Ethernet)',
                        'Secondary IPVPN Service (EFM-HSBB)',
                        'Secondary IPVPN Service (ADSL)',
                        'Secondary Customized IPVPN Access (Metro Ethernet)',
                        'Secondary Customized IPVPN Service (Metro Ethernet)',
                        'Secondary Customized IPVPN Service (ADSL)',
                        'Secondary Customized IPVPN Lite Service (Cellular)',
                        'Secondary Customized IPVPN Service (Others - Proxy)'
                    )"""

            cursor.execute(query)

            # Fetch the results and print them
            results = cursor.fetchall()

            data_dict = dict()
            data_list = list()

            for row in results:
                # return Response({"status": "success", "output": row[0]})
                data_dict = dict()
                data_dict['OrderNumber'] = row[0]
                data_dict['ServiceId'] = row[1]
                data_dict['ProductName'] = row[2]
                data_dict['ActivityName'] = row[3]
                data_dict['ActivityCreated'] = row[4]
                data_dict['Aging'] = row[5]
                data_dict['ActivityStatus'] = row[6]
                data_dict['OrderType'] = row[7]
                data_dict['Account'] = row[8]
                data_dict['OrderStatus'] = row[9]
                data_dict['Owner'] = row[10]
                data_dict['OwnersPosition'] = row[11]
                data_dict['Segment Subgroup'] = row[12]
                data_list.append(data_dict)

            # Close the cursor and conn
            cursor.close()
            conn.close()

            if (len(data_list) > 0):
                return Response({"status": "success", "output": data_list})
            else:
                return Response({"status": "success", "output": 'no data found in db'})

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })

class OGGRetrieveAllActivityPONR(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        # request_body=OggQueryAllActivitiesInProgressProductName,
        operation_summary="Retrieve in progress activity"
    )

    def get(self, request):

        try:

            dsn = cx_Oracle.makedsn(
                settings.DATABASES['oracle']['HOST'],
                settings.DATABASES['oracle']['PORT'],
                service_name=settings.DATABASES['oracle']['NAME']
            )
            conn = cx_Oracle.connect(
                user=settings.DATABASES['oracle']['USER'],
                password=settings.DATABASES['oracle']['PASSWORD'],
                dsn=dsn
            )

            cursor = conn.cursor()

            query = """
            SELECT
                so.order_num AS "Order Number",
                soi.service_num AS "Service Id",
                prod.name AS "Product Name",
                act.todo_cd AS "Activity Name",
                act.act_created_dt + INTERVAL '8' HOUR AS "Activity Created",
                CASE
                    WHEN act.todo_actl_end_dt IS NULL THEN 
                        TRUNC(SYSDATE) - TRUNC(act.act_created_dt + INTERVAL '8' HOUR)
                    ELSE 
                        TRUNC(act.todo_actl_end_dt) - TRUNC(act.act_created_dt + INTERVAL '8' HOUR)
                END AS "Aging",
                act.evt_stat_cd AS "Activity Status",
                sot.name AS "Order Type",
                soe.name AS "Account",
                so.STATUS_CD AS "Order Status",
                ownercontact.fst_name || ' ' || ownercontact.last_name " Owner",
                ownerpostn.name " OwnersPosition",
                soe.X_SEGMENT_SUBGRP "Segment Subgroup"
                FROM
                    assurance_nova.s_evt_act act
                LEFT JOIN assurance_nova.s_contact ownercontact ON
                    act.owner_per_id = ownercontact.row_id
                LEFT JOIN assurance_nova.s_postn ownerpostn ON
                    ownercontact.pr_held_postn_id = ownerpostn.row_id
                LEFT JOIN assurance_nova.s_org_ext soe ON
                    act.target_ou_id = soe.row_id
                LEFT JOIN assurance_nova.s_order so ON
                    act.order_id = so.row_id
                LEFT JOIN assurance_nova.s_order_type sot ON
                    so.order_type_id = sot.row_id
                LEFT JOIN assurance_nova.s_order_item soi ON
                    act.ORDER_ITEM_ID = soi.row_id
                LEFT JOIN assurance_nova.s_prod_int prod ON
                    soi.prod_id = prod.row_id
                WHERE
                    so.STATUS_CD IN ('PONR')   
                    AND act.evt_stat_cd = 'In Progress'
                    AND prod.name IN (
                        'NID - Primary', 'NID - Backup', 
                        'UPE - Primary', 'UPE - Backup', 
                        'Primary UPE Interface', 
                        'Primary UPE Interface - Metro Ethernet', 
                        'Backup UPE Interface - Metro Ethernet', 
                        'Metro-E Leg', 
                        'Metro-E Leg - Customized', 
                        'Metro-E Service', 
                        'Primary Metro-E Service', 
                        'Backup Metro-E Service', 
                        'Direct', 
                        'Direct Standard Plus Package', 
                        'Customized Direct Package', 
                        'Standard Direct Package - DOME', 
                        'Platinum Direct Package - DOME', 
                        'Gold Direct Package - DOME', 
                        'Silver Direct Package - DOME', 
                        'Primary Direct Access (Metro Ethernet)', 
                        'Primary Direct Service (Metro Ethernet)', 
                        'Primary Customized Direct Access (Metro Ethernet)', 
                        'Primary Customized Direct Service (Metro Ethernet)', 
                        'Secondary Direct Access (Metro Ethernet)', 
                        'Secondary Direct Service (Metro Ethernet)', 
                        'Secondary Customized Direct Service (Metro Ethernet)', 
                        'Primary IPVPN Access (Metro Ethernet)', 
                        'IPVPN Premier Standard Plus Leg',
                        'IPVPN',
                        'IPVPN Premier Leg',
                        'IPVPN Premier Standard Plus Leg',
                        'IPVPN Premier Customized Leg',
                        'IPVPN Lite Leg',
                        'IPVPN Lite Standard Plus Leg',
                        'IPVPN Value Standard Plus Leg',
                        'Primary IPVPN Access (Metro Ethernet)',
                        'Primary IPVPN Access (EFM-HSBB)',
                        'Primary IPVPN Access (Leased Line)',
                        'Primary IPVPN Service (Metro Ethernet)',
                        'Primary IPVPN Service (EFM-HSBB)',
                        'Primary IPVPN Service (Leased Line)',
                        'Primary IPVPN Service (ADSL)',
                        'Primary IPVPN Service (Cellular)',
                        'Primary Customized IPVPN Access (Metro Ethernet)',
                        'Primary Customized IPVPN Service (Metro Ethernet)',
                        'Primary Customized IPVPN Access (Leased Line)',
                        'Primary Customized IPVPN Service (Leased Line)',
                        'Primary Customized IPVPN Value Access (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Value Service (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Lite Access (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Lite Service (EFM-HSBB Proxy)',
                        'Primary Customized IPVPN Lite Service (ADSL)',
                        'Primary Customized IPVPN Lite Service (Cellular)',
                        'Primary Customized IPVPN Value Service (ADSL)',
                        'Secondary IPVPN Access (Metro Ethernet)',
                        'Secondary IPVPN Access (EFM-HSBB)',
                        'Secondary IPVPN Service (Metro Ethernet)',
                        'Secondary IPVPN Service (EFM-HSBB)',
                        'Secondary IPVPN Service (ADSL)',
                        'Secondary Customized IPVPN Access (Metro Ethernet)',
                        'Secondary Customized IPVPN Service (Metro Ethernet)',
                        'Secondary Customized IPVPN Service (ADSL)',
                        'Secondary Customized IPVPN Lite Service (Cellular)',
                        'Secondary Customized IPVPN Service (Others - Proxy)'
                    )"""

            cursor.execute(query)

            # Fetch the results and print them
            results = cursor.fetchall()

            data_dict = dict()
            data_list = list()

            for row in results:
                # return Response({"status": "success", "output": row[0]})
                data_dict = dict()
                data_dict['OrderNumber'] = row[0]
                data_dict['ServiceId'] = row[1]
                data_dict['ProductName'] = row[2]
                data_dict['ActivityName'] = row[3]
                data_dict['ActivityCreated'] = row[4]
                data_dict['Aging'] = row[5]
                data_dict['ActivityStatus'] = row[6]
                data_dict['OrderType'] = row[7]
                data_dict['Account'] = row[8]
                data_dict['OrderStatus'] = row[9]
                data_dict['Owner'] = row[10]
                data_dict['OwnersPosition'] = row[11]
                data_dict['Segment Subgroup'] = row[12]
                data_list.append(data_dict)

            # Close the cursor and conn
            cursor.close()
            conn.close()

            if (len(data_list) > 0):
                return Response({"status": "success", "output": data_list})
            else:
                return Response({"status": "success", "output": 'no data found in db'})

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            return Response({'status': 'failed', 'output': str(error), 'output_error': output_error })


class CsrInfoApi(generics.GenericAPIView):
    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    serializer_class = CsrInfoSerializer
    queryset = CsrInfo.objects.all()

    @swagger_auto_schema(
        operation_summary="Retrieve list of CSR"
    )

    def get(self, request):
        page_num = int(request.GET.get("page", 1))
        limit_num = int(request.GET.get("limit", 10))
        start_num = (page_num - 1) * limit_num
        end_num = limit_num * page_num
        search_param = request.GET.get("search")
        ne = CsrInfo.objects.all()
        total_ne = ne.count()
        if search_param:
            ne = ne.filter(csr__icontains=search_param)
            total_ne = ne.count()
        serializer = self.serializer_class(ne[start_num:end_num], many=True)
        return Response({
            "status": "success",
            "total": total_ne,
            "page": page_num,
            "last_page": math.ceil(total_ne / limit_num),
            "csr": serializer.data
        })
        
    @swagger_auto_schema(
        operation_summary="Create new CSR(s)"
    )
    def post(self, request):
        serializer = self.serializer_class(data=request.data, many=isinstance(request.data, list))
        serializer.is_valid(raise_exception=True)
        
        try:
            created_objects = []
            for obj_data in serializer.validated_data:
                obj = CsrInfo.objects.create(**obj_data)
                created_objects.append(obj)
                
            serializer = self.serializer_class(created_objects, many=True)
            return Response({
                "status": "success",
                "csr": serializer.data
            }, status=status.HTTP_201_CREATED)
        except Exception as error:
            return Response({
                "status": "fail",
                "message": str(error)
            }, status=status.HTTP_400_BAD_REQUEST)


class CsrInfoDetailApi(generics.GenericAPIView):

    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    serializer_class = CsrInfoSerializer
    queryset = CsrInfo.objects.all()

    def get_csr(self, csr):
        try:
            return CsrInfo.objects.get(csr=csr)
        except:
            return None

    # @swagger_auto_schema(
    #     # query_serializer=NetworkElementSerializer,
    #     operation_summary="Get network element details"
    # )

    def get(self, request, csr):
        csr = self.get_csr(csr=csr)
        if csr == None:
            return Response({"status": "fail", "message": f"CSR {csr} is not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(csr)
        return Response({"status": "success", "csr": serializer.data})

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Modify CSR details"
    )

    def patch(self, request, csr):
        csr = self.get_csr(csr)
        if csr == None:
            return Response({"status": "fail", "message": f"CSR {csr} is not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(
            csr, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.validated_data['updatedAt'] = datetime.now()
            serializer.save()
            return Response({"status": "success", "csr": serializer.data})
        return Response({"status": "fail", "message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        # query_serializer=NetworkElementSerializer,
        operation_summary="Delete CSR"
    )

    def delete(self, request, csr):
        csr = self.get_csr(csr)
        if csr == None:
            return Response({"status": "fail", "message": f"CSR {csr} is not found"}, status=status.HTTP_404_NOT_FOUND)

        csr.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


