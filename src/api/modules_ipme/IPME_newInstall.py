from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
from api.serializers_service import IPMENewInstall
import requests, os, json, time, re, sys
from api.libs import log_text, log_database
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
from json import dumps
from django.db import OperationalError

from dotenv import load_dotenv          # to retrieve data from .env file
load_dotenv()

class API(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		request_body=IPMENewInstall
	)

	def post(self, request):
		# verify passed data
		input_data = IPMENewInstall(data=request.data)
		input_data.is_valid()

		if len(input_data.errors) > 0:  # check if passed data contains error and return error message
			return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
		else:
			try:
				payload = input_data.data
				response = requests.post(os.getenv('NODERED_URL')+'/juniper/ipme/new_install', json=payload, verify=False)
				output = response.json()

				return Response(output,status=status.HTTP_200_OK)

			except Exception as error:
				api_response = dict()
				api_response['status'] = "failed"
				api_response['message'] = "Internal server error. Please contact administrator."

				return Response(api_response,status=status.HTTP_500_INTERNAL_SERVER_ERROR)
