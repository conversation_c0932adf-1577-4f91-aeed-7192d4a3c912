from rest_framework.views import APIView 
from rest_framework.response import Response
from rest_framework import status
from api.serializers_data import UnisGetUpeNidSerializer
from drf_yasg.utils import swagger_auto_schema
from django.conf import settings
import cx_Oracle
import json, sys  # Import json for serialization


class API(APIView):

    authentication_classes = ()  # exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        operation_description="Get list of UPE/NID details",
        request_body=UnisGetUpeNidSerializer,
        responses={
            status.HTTP_200_OK: "Successfully retrieved list of UPE/NID details",
            status.HTTP_400_BAD_REQUEST: "An error has occurred",
        },
    )
    def post(self, request):

        # Verify passed data
        input_data = UnisGetUpeNidSerializer(data=request.data)
        if not input_data.is_valid():  # Check if passed data contains errors
            return Response({
                'status': 'failed',
                'output': 'incomplete/invalid information fetch by API',
                'data': input_data.errors
            }, status=status.HTTP_206_PARTIAL_CONTENT)

        try:
            # Initialize variables using the passed data
            serialized_data = input_data.data

            dsn = cx_Oracle.makedsn(
                settings.DATABASES['UNIS']['HOST'],
                settings.DATABASES['UNIS']['PORT'],
                service_name=settings.DATABASES['UNIS']['NAME']
            )
            conn = cx_Oracle.connect(
                user=settings.DATABASES['UNIS']['USER'],
                password=settings.DATABASES['UNIS']['PASSWORD'],
                dsn=dsn
            )

            cursor = conn.cursor()

            nid_upe_names = serialized_data['nid_upe_name']  # assuming this is a list like ['ABC', 'DEF']
            nid_upe_list = "', '".join(nid_upe_names)  # results in 'ABC', 'DEF'

            sql = f"""SELECT * from oraforms.TM_SELFSERVE_UPE WHERE EQUIPMENT_ID IN ('{nid_upe_list}')"""

            cursor.execute(sql)

            # Fetch the results
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]  # Get column names

            # Convert to a list of dictionaries
            data_list = [dict(zip(columns, row)) for row in results]

            # Close the cursor and connection
            cursor.close()
            conn.close()

            # testing
            # return Response({
            #     "status": "success",    
            #     "output": sql
            # })

            # Return Response
            if data_list:
                return Response({
                    "status": "success", 
                    "output": data_list
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "status": "success", 
                    "output": 'no data found in db'
                }, status=status.HTTP_200_OK)

        except Exception as error:
            output_error = list()
            exc_type, exc_obj, exc_tb = sys.exc_info()
            output_error.append(str(exc_type))
            output_error.append(str(exc_obj))
            output_error.append(str(exc_tb.tb_lineno))
            return Response({
                'status': 'failed', 
                'output': str(error), 
                'output_error': output_error 
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
