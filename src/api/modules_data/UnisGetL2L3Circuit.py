from rest_framework.views import APIView 
from rest_framework.response import Response
from rest_framework import status  # status code and DRF view
from api.serializers_data import UnisGetL3CircuitSerializer
from drf_yasg.utils import swagger_auto_schema
import sys
from datetime import datetime
from django.db import connections
from django.conf import settings
import cx_Oracle


class API(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        operation_description="Get list of Layer 3 Circuits",
        request_body=UnisGetL3CircuitSerializer,
        responses={
            status.HTTP_200_OK: "Successfully retrieved list of Layer 3 circuits",
            status.HTTP_400_BAD_REQUEST: "An error has occurred",
        },
    )


    def post(self, request):

        # verify passed data
        input_data = UnisGetL3CircuitSerializer(data=request.data)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({
                'status': 'failed',
                'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors
            }, status=status.HTTP_206_PARTIAL_CONTENT)

        else:
            try:

                # initialize variables using the passed data
                serialized_data = input_data.data

                dsn = cx_Oracle.makedsn(
                    settings.DATABASES['UNIS']['HOST'],
                    settings.DATABASES['UNIS']['PORT'],
                    service_name=settings.DATABASES['UNIS']['NAME']
                )
                conn = cx_Oracle.connect(
                    user=settings.DATABASES['UNIS']['USER'],
                    password=settings.DATABASES['UNIS']['PASSWORD'],
                    dsn=dsn
                )

                cursor = conn.cursor()

                # Generate SQL query
                cust_abbr_values = serialized_data['cust_abbr']  # assuming this is a list like ['ABC', 'DEF']
                cust_abbr_string = "', '".join(cust_abbr_values)  # results in 'ABC', 'DEF'
                sql_query = f"""
                SELECT a.*, b.*
                FROM SSP_LAYER3 a
                LEFT JOIN SSP_LAYER2 b ON a.SERVICE_ID = b.PARENT_SERVICE_ID
                WHERE a.CUST_ABBR IN ('{cust_abbr_string}')
                AND (b.PARENT_SERVICE_ID IS NULL OR b.PARENT_SERVICE_ID IS NOT NULL)
                AND a.SERVICE_PRODUCT NOT IN ('PE', 'CE')
                AND a.PATH_STATUS = 'In Service'
                AND (b.PATH_STATUS = 'In Service' OR b.PATH_STATUS IS NULL)
                """

                cursor.execute(sql_query)

                # Fetch the results and print them
                results = cursor.fetchall()

                data_list = list()

                for row in results:
                    # return Response({"status": "success", "output": row[0]})
                    data_dict = dict()
                    # LAYER 3
                    data_dict['CustAbbr'] = row[0]  #CUST_ABBR
                    data_dict['ServiceId'] = row[1] #SERVICE_ID
                    data_dict['Product'] = row[2]   #SERVICE_PRODUCT
                    data_dict['Bandwidth'] = row[3] #BANDWIDTH
                    data_dict['RedundancyId'] = row[4]  #REDUNDANCY_GROUP_ID
                    data_dict['QOS'] = row[5]   #QOS
                    data_dict['ParentId'] = row[6]  #PARENT_SERVICE
                    data_dict['PathStatus'] = row[7]    #PATH_STATUS
                    data_dict['ServiceType'] = row[8]   #SERVICE_TYPE
                    data_dict['Npe'] = row[9]   #EQUIPMENTID_NPE
                    data_dict['NpeInterface'] = row[10]   #PORTACCESSID_NPE
                    data_dict['Pe'] = row[11]   #EQUIPMENTID_PE
                    data_dict['PeInterface'] = row[12]  #PORTACCESSID_PE
                    data_dict['PeDeviceId'] = row[13]  #DEVICEID_PE
                    data_dict['PeIpMask'] = row[14]  #PE_WAN_IP_NETMASK
                    data_dict['PeIp'] = row[15] #DEVICEID_PE
                    data_dict['PeIpCidr'] = row[16] #DEVICEID_PE
                    data_dict['CeIp'] = row[19] #CE_WAN_IPV4

                    # LAYER 2
                    data_dict['L2CustAbbr'] = row[27]  #CUST_ABBR
                    data_dict['ParentServiceId'] = row[28] #PARENT_SERVICE_ID
                    data_dict['L2ServiceId'] = row[29]   #SERVICE_ID
                    data_dict['L2Product'] = row[30] #SERVICE_PRODUCT
                    data_dict['L2Bandwidth'] = row[31]  #BANDWIDTH
                    data_dict['L2RedundancyId'] = row[32]  #REDUNDANCY_GROUP_ID
                    data_dict['L2ParentSvc'] = row[34]    #PARENT_SERVICE
                    data_dict['L2PathStatus'] = row[35]   #PATH_STATUS
                    data_dict['L2ServiceType'] = row[36]   #SERVICE_TYPE

                    data_dict['UpeNidDlink'] = row[37] #EQUIPMENTID_DOWNLINK
                    data_dict['UpeNidDlinkInterface'] = row[38]   #PORTACCESSID_DOWNLINK
                    data_dict['UpeNidUlinkPri'] = row[39]  #EQUIPMENTID_UPLINK_PRI
                    data_dict['UpeNidUlinkPriInterface'] = row[40] #PORTACCESSID_UPLINK_PRI
                    data_dict['UpeNidUlinkSec'] = row[41]  #EQUIPMENTID_UPLINK_SEC
                    data_dict['UpeNidUlinkSecInterface'] = row[42] #PORTACCESSID_UPLINK_SEC

                    data_dict['EpePri'] = row[43]   #EQUIPMENTID_EPE_PRI
                    data_dict['EpePriInterface'] = row[44]   #PORTACCESSID_EPE_PRI
                    data_dict['EpeSec'] = row[45]   #EQUIPMENTID_EPE_SEC
                    data_dict['EpeSecInterface'] = row[46]   #PORTACCESSID_EPE_SEC

                    data_list.append(data_dict)

                # Close the cursor and conn
                cursor.close()
                conn.close()

                if (len(data_list) > 0):
                    return Response({
                        "status": "success", 
                        "output": data_list
                    }, status=status.HTTP_200_OK)
                else:
                    return Response({
                        "status": "success", 
                        "output": 'no data found in db'
                    }, status=status.HTTP_200_OK)

            except Exception as error:
                output_error = list()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                output_error.append(str(exc_type))
                output_error.append(str(exc_obj))
                output_error.append(str(exc_tb.tb_lineno))
                return Response({
                    'status': 'failed', 
                    'output': str(error), 
                    'output_error': output_error 
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)