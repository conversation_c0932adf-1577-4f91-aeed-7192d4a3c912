from django.urls import path
from api import files

urlpatterns = [
    path('v1/configuration/', files.api_configuration.as_view(), name='configuration'),
    path('v1/read_configuration/', files.api_read_configuration.as_view(), name='read_configuration'),
    path('v1/provision_log/', files.api_log.as_view(), name='provision_log'),
    path('v1/read_provision_log/', files.api_log_content.as_view(), name='read_provision_log'),
    path('v1/ne_log/', files.api_ne_log.as_view(), name='ne_log'),
    path('v1/read_ne_log/', files.api_ne_log_content.as_view(), name='read_ne_log'),
    path('v1/logs/', files.api_read_log_content.as_view(), name='read_log'),
    path('v1/read_sas_logs/', files.api_read_sas_log_content.as_view(), name='read_sas_logs'),
    # path('v1/log/', Bod.API.as_view(), name='log'),
]