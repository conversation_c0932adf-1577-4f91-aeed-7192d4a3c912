# from rest_framework import generics
# from .models import AuditTrail
# from .serializers import AuditTrailSerializer
# from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
# from rest_framework.pagination import PageNumberPagination

# class AuditTrailPagination(PageNumberPagination):
#     page_size = 10
#     page_size_query_param = 'page_size'
#     max_page_size = 100

# class AuditTrailApi(generics.ListAPIView):

#     # exclude from global authentication
#     authentication_classes = ()
#     permission_classes = ()

#     queryset = AuditTrail.objects.all().order_by('-updated')  # return result date descending
#     # queryset = AuditTrail.objects.all()
#     serializer_class = AuditTrailSerializer
#     filter_backends = [OrderingFilter, SearchFilter]
#     search_fields = ['service_type', 'order_type', 'input_details']  # Fields to search
#     pagination_class = AuditTrailPagination