from django.core import validators
from rest_framework import serializers
from django.core.validators import validate_ipv4_address
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token
from api.models import AuditTrail, NetworkElement, User5GBot, CsrInfo
import re
import ipaddress

# Global validation for bandwidth
def validate_bandwidth(bw):
    if not re.search('(^[1-9][0-9]*[mMkKgG]$)', bw):    # bandwidth must start with numbers ^[1-9], can contain numbers 0-9 in the middle [0-9]* and end with m/M/k/K/g/G [mMkKgG]$
        raise serializers.ValidationError('Invalid bandwidth format')

# Global validation for interface
def validate_interface(interface):
    if not len(re.findall('[-/.]', interface)) == 4:    # ensure there are total of 1 X '-', 2 X '/' and 1 X '.'
        raise serializers.ValidationError('Invalid interface format')

# Global validation for interface
def validate_interface_me(interface):
    # ensure there are total of 1 X '-', 2 X '/' and 1 X '.'
    if not len(re.findall('[.]', interface)) == 1:
        raise serializers.ValidationError('Invalid interface format')


class HeaderSerializer(serializers.Serializer):
	BusinessEvent = serializers.CharField()
	CorrelationID = serializers.CharField()
	Timestamp = serializers.CharField(allow_blank=True, allow_null=True)

class BodSerializer(serializers.Serializer):
	service_id = serializers.CharField()
	node_name = serializers.CharField()
	device_id = serializers.CharField(validators=[validate_ipv4_address]) 
	# username = serializers.CharField(required=False, default=None)
	# password = serializers.CharField(required=False, default=None)
	interface = serializers.CharField()
	pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
	# qos = serializers.CharField()
	vpn_name = serializers.CharField()
	ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
	# order_category = serializers.CharField()
	new_bandwidth = serializers.CharField()
	protocol = serializers.CharField()
	cpe_type = serializers.CharField()     #cisco/juniper
	l3_service_ce = serializers.CharField()       #managed/un-managed
	product_name = serializers.CharField()
	pe_region = serializers.CharField()
	old_bandwidth = serializers.CharField()

	# Verify new bandwidth format
	def validate_new_bandwidth(self, value):
		bw = value.lower()
		if re.compile('[mMkKgG]').search(bw) != None and re.compile('[0-9]').search(bw) != None:
			if re.compile('[@_!#$%^&*()<>?/\|}{~:.,]').search(bw) != None:
				raise serializers.ValidationError("Invalid bandwidth format")
			elif int(re.sub(r'\D', '', value)) <= 0:
				raise serializers.ValidationError("Bandwidth must be greater than 0")
			else:
				return value
		else:
			raise serializers.ValidationError("Invalid bandwidth format")

	# Verify old bandwidth format
	def validate_old_bandwidth(self, value):
		bw = value.lower()
		if re.compile('[mMkKgG]').search(bw) != None and re.compile('[0-9]').search(bw) != None:
			if re.compile('[@_!#$%^&*()<>?/\|}{~:.,]').search(bw) != None:
				raise serializers.ValidationError("Invalid bandwidth format")
			elif int(re.sub(r'\D', '', value)) <= 0:
				raise serializers.ValidationError("Bandwidth must be greater than 0")
			else:
				return value
		else:
			raise serializers.ValidationError("Invalid bandwidth format")
	
	# Verify interface format
	def validate_interface(self, value):
		if '.' not in value or '-' not in value or '/' not in value:
			raise serializers.ValidationError("Invalid interface format")
		else:
			return value

class PisaBodSerializer(serializers.Serializer):
	Header = HeaderSerializer()
	Request = BodSerializer()

class RequestPisaBodSerializer(serializers.Serializer):
	RequestPISA = PisaBodSerializer()

class UserSerializer(serializers.ModelSerializer):
	class Meta:
		model = User
		fields = ('username', 'email', 'password')
		extra_kwargs = {'password': {'write_only': True}}       # Avoid display the password in response

	def create(self, validated_data):
		user = User(
			email=validated_data['email'],
			username=validated_data['username']
		)
		user.set_password(validated_data['password'])
		user.save()
		Token.objects.create(user=user)
		return user

class AuditTrailSerializer(serializers.ModelSerializer):
	class Meta:
		model = AuditTrail
		user = serializers.Field(source='user.username')
		fields = ['id','order_type','service_type','input_details','message','api_output','status','updated','log_file','config_file','user']
		read_only_fields = ['id','updated']

class ReadFileSerializer(serializers.Serializer):
	filename = serializers.CharField()

class ReadServiceIdSerializer(serializers.Serializer):
    service_id = serializers.CharField()

# DOME
class DomeBodSerializer(serializers.Serializer):
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    device_id = serializers.CharField(validators=[validate_ipv4_address]) 
    product_name = serializers.CharField()
    interface = serializers.CharField(validators=[validate_interface])
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    protocol = serializers.CharField()
    pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    ce_lan_ip = serializers.CharField(required=False, default=None, allow_blank=True, validators=[validate_ipv4_address])
    l3_service_ce = serializers.CharField()       #managed/un-managed
    cpe_type = serializers.CharField()     #cisco/juniper
    cust_name = serializers.CharField()     #customer - cust_name
    leg_id = serializers.CharField()     #leg_name - leg_id
    pe_region = serializers.CharField()
    order_no = serializers.CharField()
    bod_start = serializers.CharField()
    bod_end = serializers.CharField()
    site_abbr = serializers.CharField()
    cust_abbr = serializers.CharField()

    # Verify node name to support only MSE & IMSE
    def validate_node_name(self, value):
        pe_name = value.upper()
        if re.search('IGW', pe_name, re.IGNORECASE):
            raise serializers.ValidationError("Only supported IMSE")
        else:
            return value


    # Verify new bandwidth format
    def validate_new_bandwidth(self, value):
        bw = value.lower()
        if re.compile('[mMkKgG]').search(bw) != None and re.compile('[0-9]').search(bw) != None:
            if re.compile('[@_!#$%^&*()<>?/\|}{~:.,]').search(bw) != None:
                raise serializers.ValidationError("Invalid bandwidth format")
            elif int(re.sub(r'\D', '', value)) <= 0:
                raise serializers.ValidationError("Bandwidth must be greater than 0")
            else:
                return value
        else:
            raise serializers.ValidationError("Invalid bandwidth format")

    # Verify old bandwidth format
    def validate_old_bandwidth(self, value):
        bw = value.lower()
        if re.compile('[mMkKgG]').search(bw) != None and re.compile('[0-9]').search(bw) != None:
            if re.compile('[@_!#$%^&*()<>?/\|}{~:.,]').search(bw) != None:
                raise serializers.ValidationError("Invalid bandwidth format")
            elif int(re.sub(r'\D', '', value)) <= 0:
                raise serializers.ValidationError("Bandwidth must be greater than 0")
            else:
                return value
        else:
            raise serializers.ValidationError("Invalid bandwidth format")
    
    # Verify interface format
    def validate_interface(self, value):
        if '.' not in value or '-' not in value or '/' not in value:
            raise serializers.ValidationError("Invalid interface format")
        else:
            return value


class PisaDomeBodSerializer(serializers.Serializer):
    Header = HeaderSerializer()
    Request = DomeBodSerializer()

class RequestPisaDomeBodSerializer(serializers.Serializer):
    RequestPISA = PisaDomeBodSerializer()


# IPME Modify Bandwidth
class IpmeModBwSerializer(serializers.Serializer):
    order_no = serializers.CharField()
    cust_abbr = serializers.CharField()
    # site_abbr = serializers.CharField(required=False,allow_blank=True)
    # ce_host_name = serializers.CharField()
    cust_name = serializers.CharField()     #customer - cust_name
    leg_id = serializers.CharField()     #leg_name - leg_id
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    device_id = serializers.CharField(validators=[validate_ipv4_address]) 
    # username = serializers.CharField(required=False, default=None)
    # password = serializers.CharField(required=False, default=None)
    interface = serializers.CharField(validators=[validate_interface])
    pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    # qos = serializers.CharField()
    vpn_name = serializers.CharField()
    ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    # order_category = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    protocol = serializers.CharField()
    cpe_type = serializers.CharField()     #cisco/juniper
    l3_service_ce = serializers.CharField()       #managed/un-managed
    product_name = serializers.CharField()
    pe_region = serializers.CharField()
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])

    # Validate VPN except MLS, TM_WIFI
    def validate_vpn_name(self, value):
        if re.search('SIPT', value, re.IGNORECASE) or re.search('TM_INT', value, re.IGNORECASE) or re.search('TM_WIFI', value, re.IGNORECASE):
            raise serializers.ValidationError("VPN MLS/TM WIFI is not supported")
        else:
            return value

class PisaIpmeModBwSerializer(serializers.Serializer):
    Header = HeaderSerializer()
    Request = IpmeModBwSerializer()

class RequestPisaIpmeModBwSerializer(serializers.Serializer):
    RequestPISA = PisaIpmeModBwSerializer()


# 1GOV TBU Bod
class TbuSerializer(serializers.Serializer):
    start_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    end_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    device_id = serializers.CharField(validators=[validate_ipv4_address]) 
    # username = serializers.CharField(required=False, default=None)
    # password = serializers.CharField(required=False, default=None)
    interface = serializers.CharField(validators=[validate_interface])
    pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    # qos = serializers.CharField()
    vpn_name = serializers.CharField()
    ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    # order_category = serializers.CharField()
    # new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    # protocol = serializers.CharField()
    # cpe_type = serializers.CharField()     #cisco/juniper
    # l3_service_ce = serializers.CharField()       #managed/un-managed
    # product_name = serializers.CharField()
    pe_region = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])

# 1GOV TBU Bod Layer 2
class L2TbuSerializer(serializers.Serializer):
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    vpn_name = serializers.CharField()
    # device_id = serializers.CharField(validators=[validate_ipv4_address])
    interface = serializers.CharField(validators=[validate_interface_me])
    sys_name = serializers.CharField()
    # order_category = serializers.CharField()
    # new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    # protocol = serializers.CharField()
    # cpe_type = serializers.CharField()     #cisco/juniper
    # l3_service_ce = serializers.CharField()       #managed/un-managed
    # product_name = serializers.CharField()
    # pe_region = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    region = serializers.CharField()

# DOME TBU Bod Layer 2
class L2DomeBodSerializer(serializers.Serializer):
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    interface = serializers.CharField(validators=[validate_interface_me])
    sys_name = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    region = serializers.CharField()

# IPME TBU UBOD Layer 2
class L2IpmeUbodSerializer(serializers.Serializer):
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    vpn_name = serializers.CharField()
    qos_package = serializers.CharField()
    interface = serializers.CharField(validators=[validate_interface_me])
    sys_name = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    region = serializers.CharField()

# IPME Bod Layer 3
class L3IpmeBodSerializer(serializers.Serializer):
    start_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    end_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    device_id = serializers.CharField(validators=[validate_ipv4_address])
    interface = serializers.CharField(validators=[validate_interface])
    pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    vpn_name = serializers.CharField()
    ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    cpe_type = serializers.ChoiceField(choices=['cisco', 'juniper', 'others'])     #cisco/juniper
    l3_service_ce = serializers.ChoiceField(choices=['managed', 'unmanaged'])       #managed/unmanaged
    pe_region = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])

    # cpe_type is mandatory if l3_service_ce is managed
    # def validate(self, data):
         
    #     if data['l3_service_ce'] == 'managed' and not data['cpe_type']:
    #         raise serializers.ValidationError("cpe_type is required if l3_service_ce is set to 'managed'")
        
    #     return data

# DOME BOD Layer 3
class L3DomeUbodSerializer(serializers.Serializer):
    start_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    end_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    service_id = serializers.CharField()
    node_name = serializers.CharField()
    protocol = serializers.CharField()
    device_id = serializers.CharField(validators=[validate_ipv4_address])
    interface = serializers.CharField(validators=[validate_interface])
    pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    cpe_type = serializers.ChoiceField(choices=['cisco', 'juniper', 'others'])     #cisco/juniper
    l3_service_ce = serializers.ChoiceField(choices=['managed', 'unmanaged'])       #managed/unmanaged
    pe_region = serializers.CharField()
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])


class GetLogSerializer(serializers.Serializer):
    year = serializers.ChoiceField(choices=['All', 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027])


class NetworkElementSerializer(serializers.ModelSerializer):
    def validate_loopbackIp(self, ip):
        try:
            ipaddress.ip_address(ip)
            return ip
        except ValueError:
            raise serializers.ValidationError("Invalid Loopback IP Address")

    class Meta:
        model = NetworkElement
        fields = '__all__'
        read_only_fields = ['id', 'createdAt', 'updatedAt']


# 5G Self Verification Bot
class SelfVerificationBot5GSerializer(serializers.Serializer):
    site_id = serializers.CharField()
    # agg
    agg_hostname = serializers.CharField()
    # agg_loopback_ip = serializers.CharField(validators=[validate_ipv4_address])  # remove loopback ip and replace with query from IPSDNC DB
    agg_port_main = serializers.CharField()
    agg_port_protect = serializers.CharField(required=False, default=None)
    agg_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    # csr
    csr_hostname = serializers.CharField()
    csr_loopback_ip = serializers.CharField(validators=[validate_ipv4_address])
    csr_wan_ip = serializers.CharField(validators=[validate_ipv4_address])

# 5G Self Verification Bot for NOCC
class SelfVerificationBot5Gv2Serializer(serializers.Serializer):
    csr_hostname = serializers.CharField()
    staff_id     = serializers.CharField(required=False, default='not_available')

# L2 and L3 1GOV
class L2L3TbuSerializer(serializers.Serializer):
    service_id = serializers.CharField()
    start_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    end_date = serializers.DateField(format="%d-%m-%Y", input_formats=['%d/%m/%Y', '%d-%m-%Y', 'iso-8601'], required=False)
    new_bandwidth = serializers.CharField(validators=[validate_bandwidth])
    old_bandwidth = serializers.CharField(validators=[validate_bandwidth])

# 5G bot verify CSR connectivity
class verify5GCsrConnectivitySerializer(serializers.Serializer):
    csr_info = serializers.CharField()
    staff_id = serializers.CharField(required=False, default='not_available')

# class user 5G bot
class User5GBotSerializer(serializers.ModelSerializer):
    class Meta:
        model = User5GBot
        fields = '__all__'
        read_only_fields = ['createdAt', 'updatedAt']

# 5G un-shut port
class UnShutPort5GSerializer(serializers.Serializer):
    agg_hostname = serializers.CharField()
    interface = serializers.CharField()


# OGG DATABASE
class OggQueryActivitiesSerializer(serializers.Serializer):
    soc_group = serializers.ChoiceField(choices=['IP TRANSIT', 'WIA', 'DIRECT'])

class OggQueryLineAttributeSerializer(serializers.Serializer):
    order_item_id = serializers.CharField()

class OggQueryLineItemSerializer(serializers.Serializer):
    order_no = serializers.CharField()

class OggQueryPerActivityIdSerializer(serializers.Serializer):
    activity_id = serializers.CharField()

class OggQueryUsingServiceNumSerializer(serializers.Serializer):
    service_number = serializers.CharField()


# telnet to network element
class telnetIpSerializer(serializers.Serializer):
    ip = serializers.CharField(validators=[validate_ipv4_address])
    port = serializers.IntegerField()

# csr_info from granite and ngoss dump
class CsrInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CsrInfo
        fields = '__all__'
        read_only_fields = ['id', 'createdAt', 'updatedAt']

class L2L3SelfServeSerializer(serializers.Serializer):
    network_type = serializers.ChoiceField(choices=['pe','ce','nid','upe', 'npe', 'epe'])
    vendor = serializers.ChoiceField(choices=['juniper','cisco','huawei','zte', 'raisecom', 'hfr','alcatel-lucent'])
    ne_ip = serializers.CharField(validators=[validate_ipv4_address])
    commands = serializers.ListField(child=serializers.CharField(), required=False)

class ServiceTestingSerializer(serializers.Serializer):
    interface_status = serializers.BooleanField(default=False, required=False)
    interface_power_level = serializers.BooleanField(default=False, required=False)
    bandwidth_subscription = serializers.BooleanField(default=False, required=False)
    ping_test_pe_ce = serializers.BooleanField(default=False, required=False)
    ping_gipvpn_pe_ce = serializers.BooleanField(default=False, required=False)
    traceroute = serializers.BooleanField(default=False, required=False)
    ping_lan_ip = serializers.BooleanField(default=False, required=False)
    incoming_route = serializers.BooleanField(default=False, required=False)
    advertise_route = serializers.BooleanField(default=False, required=False)
    route_summary = serializers.BooleanField(default=False, required=False)
    ip_prefix_register = serializers.BooleanField(default=False, required=False)
    bgp_status = serializers.BooleanField(default=False, required=False)
    show_routing_table = serializers.BooleanField(default=False, required=False)
    nid_loopback_checking = serializers.BooleanField(default=False, required=False)
    l2vpn_mpls_ping_domestic_igw = serializers.BooleanField(default=False, required=False)

class L3ServiceTestingSelfServeSerializer(serializers.Serializer):
    network_type = serializers.ChoiceField(choices=['pe', 'nid'])
    vendor = serializers.ChoiceField(choices=['juniper', 'cisco', 'huawei', 'zte', 'nokia'])
    ne_ip = serializers.CharField()
    pe_interface = serializers.CharField()
    pe_ip = serializers.CharField()
    ce_ip = serializers.CharField()
    nid_model = serializers.CharField(required=False, default=None)
    nid_device_id = serializers.CharField(required=False, default=None)
    nid_port_uplink = serializers.CharField(required=False, default=None)
    service_testing = ServiceTestingSerializer(required=False, default=None)
    traceroute_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)
    ping_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)
    advertise_route_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)
    show_routing_table_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)


class L3ServiceTestingv2SelfServeSerializer(serializers.Serializer):
    network_type = serializers.ChoiceField(choices=['pe'])
    vendor = serializers.ChoiceField(choices=['juniper', 'cisco', 'huawei', 'zte', 'nokia'])
    ne_ip = serializers.CharField()
    pe_interface = serializers.CharField()
    pe_ipv4 = serializers.CharField()
    ce_ipv4 = serializers.CharField()
    pe_ipv6 = serializers.CharField(required=False, default=None)
    ce_ipv6 = serializers.CharField(required=False, default=None)
    nid_model = serializers.CharField(required=False)
    nid_device_id = serializers.CharField(required=False)
    # nid_port_uplink = serializers.CharField(required=False)
    service_testing = ServiceTestingSerializer(required=False)
    traceroute_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)
    ping_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)
    advertise_route_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)
    show_routing_table_lan_ip = serializers.ListField(child=serializers.CharField(), required=False)


class authLdapSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()

class selfServeInsighVerificationSerializer(serializers.Serializer):
    vendor = serializers.ChoiceField(choices=['huawei', 'zte'])

    # AGG HUAWEI
    service_id = serializers.CharField()
    product = serializers.ChoiceField(choices=['ipvpn', '1gov'])
    nid_upe_name = serializers.CharField()
    ne_ip = serializers.CharField(validators=[validate_ipv4_address])
    vrf_name = serializers.CharField()
    interface_name = serializers.CharField()
    site_abr = serializers.CharField(required=False, default=None)
    vpn_name = serializers.CharField(required=False, default=None)
    pe_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    ce_wan_ip = serializers.CharField(validators=[validate_ipv4_address])
    pev6_wan_ip = serializers.CharField(required=False, default=None)
    cev6_wan_ip = serializers.CharField(required=False, default=None)
    cos = serializers.ChoiceField(choices=['classic', 'essential', 'versatile', 'performance', 'convergence', 'customized'])
    prefix_name_v4 = serializers.CharField(required=False, default=None)
    prefix_name_v6 = serializers.CharField(required=False, default=None)

    def validate(self, data):
        # Check if product is '1gov' and prefix_name_v4 is not provided
        if data.get('product') == '1gov' and not data.get('prefix_name_v4'):
            raise serializers.ValidationError({"prefix_name_v4": "This field is required when product is set to '1gov'."})
        
        # Check if product is '1gov' and pev6_wan_ip has value, then site_abr and vpn_name are required
        if data.get('product') == '1gov' and data.get('pev6_wan_ip') is not None:
            if not data.get('site_abr'):
                raise serializers.ValidationError({"site_abr": "This field is required when product is '1gov' and pev6_wan_ip is provided."})
            if not data.get('vpn_name'):
                raise serializers.ValidationError({"vpn_name": "This field is required when product is '1gov' and pev6_wan_ip is provided."})
        
        return data
    


    
class BGPUptimeStatusCircuitSerializer(serializers.Serializer):
    service_id = serializers.CharField()
    ce_ip = serializers.CharField(validators=[validate_ipv4_address])

class BGPUptimeStatusSerializerv2(serializers.Serializer):
    pe_router = serializers.CharField()
    ne_router = serializers.CharField()
    data = serializers.ListField(child=BGPUptimeStatusCircuitSerializer())

class verifyXdslamInputSerializer(serializers.Serializer):
    access_id = serializers.CharField(required=False)
    epe_name = serializers.CharField(required=False)
    epe_device_id = serializers.CharField(required=False)
    epe_vendor = serializers.CharField(required=False)
    epe_interface = serializers.CharField(required=False)

    # def validate_epe_name(self, value):
    #     if not re.search('EPE', value, re.IGNORECASE):
    #         raise serializers.ValidationError("EPE name must contain 'EPE' (case insensitive)")
    #     return value

    # def validate_epe_vendor(self, value):
    #     valid_vendors = ['alcatel-lucent', 'huawei']
    #     if value.lower() not in valid_vendors:
    #         raise serializers.ValidationError(f"Vendor must be one of: {', '.join(valid_vendors)}")
    #     return value.lower()

class verifyXdslamSerializer(serializers.Serializer):
    xdslam_list = verifyXdslamInputSerializer(many=True)
    staff_id = serializers.CharField(required=False, default='not_available')
    password = serializers.CharField()
