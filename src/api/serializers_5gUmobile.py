from rest_framework import serializers
import re

def validate_port(value):
    """
    Validate that the port should contains symbol '/'
    """
    if '/' not in value:
        raise serializers.ValidationError("Invalid port format")
    return value

def validate_agg_hostname(value):
    """
    Validate that the aggregation hostname is not empty and contains 'agghw' or 'aggzt'.
    """
    # Check if the hostname contains 'agghw' or 'aggzt'
    if not re.search(r'(agghw|aggzt)', value, re.IGNORECASE):
        raise serializers.ValidationError("agg hostname must contain 'agghw' or 'aggzt'")
    
    return value

class VerifyNewSiteSerializer(serializers.Serializer):
    """
    Verify the new site data before saving it.
    """
    nid_hostname = serializers.CharField(max_length=100, required=True)
    nid_ip = serializers.IPAddressField(required=True)
    agg_hostname = serializers.CharField(max_length=100, required=True, validators=[validate_agg_hostname])
    agg_port = serializers.CharField(max_length=100, required=True, validators=[validate_port])
    staff_id = serializers.Char<PERSON>ield(max_length=100, required=False)
