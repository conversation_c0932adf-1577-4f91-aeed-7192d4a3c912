from django.urls import path
from api.modules import L3_GOV_TBU, IPME_Bod, EAI_Bod, IPME_EAI_Bod, GOV_Bod, DOME_Bod, IPME_Modify_Bandwidth, \
                        DOME_EAI_Bod, IPME_1GOV_EAI_Bod, GOV_Modify_Bandwidth, IPME_1GOV_EAI_Modify_Bandwidth, \
                        L2_GOV_TBU, SelfVerificationBot_5G, SelfVerificationBot_telegram_5G, L2L3_GOV_TBU, \
                        UnshutPort_5G_telegram, SelfVerificationBotNewSite_telegram_5G, \
                        SelfVerificationBotExistingSite_telegram_5G, L3_GOV_UBOD, L2_GOV_UBOD, L2L3_GOV_UBOD, \
                        L2L3_GOV_UBODv2, L2L3_GOV_TBUv2, L2_IPME_UBOD, L2_IPME_TBU, L3_IPME_UBOD, \
                        L3_IPME_TBU_withCE, L3_IPME_TBU_withoutCE, L2L3_IPME_UBOD, L2L3_IPME_TBU, \
                        SelfVerificationBotExistingSite_telegram_5Gv2, L2L3_Get_Granite_Info, L2_DOME_UBOD, \
                        L3_DOME_UBOD, L2_DOME_TBU, L3_DOME_TBU, L2L3_DOME_UBOD, L2L3_DOME_TBU, \
                        SelfVerificationBotExistingSite_telegram_5Gv3, telnet_ip_port, VerifyCsrConnectivity_5G, \
                        L2L3_SelfServe, L3_SvcTesting_SelfServe, L3_SvcTesting_SelfServe_v2, SelfServe_BGPUptimeStatus, \
                        SelfServe_InsightVerification, SelfServe_BGPUptimeStatusv2, VerifyXdslam
from api.modules_5g_Umobile import VerificationExistingSite, VerificationNewSite

from api import authentications

urlpatterns = [
    path('v1/ipvpn/bod/', IPME_Bod.API.as_view(), name='ipme_bod'),
    # path('v1/ipvpn/EAI/bod/', EAI_Bod.API.as_view(), name='eai_bod'),
    # path('v1/ipvpn/EAI/bod/', IPME_EAI_Bod.API.as_view(), name='ipme_eai_bod'),
    path('v1/ipvpn/EAI/bod/', IPME_1GOV_EAI_Bod.API.as_view(), name='ipme_eai_bod'),
    path('v1/register_user/', authentications.RegisterUser.as_view(), name="register_user"),
    # path('update_user/', authentications.UpdateUser.as_view(), name="update_user"),
    path('v1/get_token/', authentications.GetToken.as_view(), name="get_token"),
    path('v1/1GOV/bod/', GOV_Bod.API.as_view(), name='1gov_bod'),
    path('v1/dome/bod/', DOME_Bod.API.as_view(), name='dome_bod'),
    path('v1/dome/EAI/bod/', DOME_EAI_Bod.API.as_view(), name='dome_eai_bod'),
    path('v1/ipvpn/modify_bandwidth/', IPME_Modify_Bandwidth.API.as_view(), name='ipme_modify_bandwidth'),
    # path('v1/ipvpn/EAI/modify_bandwidth/', IPME_EAI_Modify_Bandwidth.API.as_view(), name='ipme_eai_modify_bandwidth'),
    path('v1/1GOV/modify_bandwidth/', GOV_Modify_Bandwidth.API.as_view(), name='1gov_modify_bandwidth'),
    path('v1/ipvpn/EAI/modify_bandwidth/', IPME_1GOV_EAI_Modify_Bandwidth.API.as_view(), name='ipme_eai_modify_bandwidth'),
    path('v1/1GOV/tbu/layer3', L3_GOV_TBU.API.as_view(), name='1gov_tbu_L3_v1'),
    path('v1/1GOV/ubod/layer3', L3_GOV_UBOD.API.as_view(), name='1gov_ubod_L3_v1'),
    path('v1/ipvpn/ubod/layer2', L2_IPME_UBOD.API.as_view(), name='ipvpn_ubod_L2_v1'),
    path('v1/ipvpn/ubod/layer3', L3_IPME_UBOD.API.as_view(), name='ipvpn_ubod_L3_v1'),
    path('v1/ipvpn/tbu/layer2', L2_IPME_TBU.API.as_view(), name='ipvpn_tbu_L2_v1'),
    path('v1/ipvpn/tbu_with_ce/layer3', L3_IPME_TBU_withCE.API.as_view(), name='ipvpn_tbu_with_ce_L3_v1'),
    path('v1/ipvpn/tbu_with_no_ce/layer3', L3_IPME_TBU_withoutCE.API.as_view(), name='ipvpn_tbu_with_no_ce_L3_v1'),
    path('v1/1GOV/tbu/layer2', L2_GOV_TBU.API.as_view(), name='1gov_tbu_L2_v1'),
    path('v1/1GOV/ubod/layer2', L2_GOV_UBOD.API.as_view(), name='1gov_ubod_L2_v1'),
    # 1GOV
    path('v1/1GOV/tbu/', L2L3_GOV_TBU.API.as_view(), name='1gov_tbu_L2_L3_v1'),
    path('v2/1GOV/tbu/', L2L3_GOV_TBUv2.API.as_view(), name='1gov_tbu_L2_L3_v2'),
    path('v1/1GOV/ubod/', L2L3_GOV_UBOD.API.as_view(), name='1gov_ubod_L2_L3_v1'),
    path('v2/1GOV/ubod/', L2L3_GOV_UBODv2.API.as_view(), name='1gov_ubod_L2_L3_v2'),
    # IPME
    path('v1/ipvpn/ubod/', L2L3_IPME_UBOD.API.as_view(), name='ipvpn_ubod_L2_L3_v1'),
    path('v1/ipvpn/tbu/', L2L3_IPME_TBU.API.as_view(), name='ipvpn_tbu_L2_L3_v1'),
    # DOME
    path('v1/dome/ubod/', L2L3_DOME_UBOD.API.as_view(), name='dome_ubod_L2_L3_v1'),
    path('v1/dome/tbu/', L2L3_DOME_TBU.API.as_view(), name='dome_tbu_L2_L3_v1'),
    path('v1/dome/ubod/layer2', L2_DOME_UBOD.API.as_view(), name='dome_ubod_L2_v1'),
    path('v1/dome/tbu/layer2', L2_DOME_TBU.API.as_view(), name='dome_tbu_L2_v1'),
    path('v1/dome/ubod/layer3', L3_DOME_UBOD.API.as_view(), name='dome_ubod_L3_v1'),
    path('v1/dome/tbu/layer3', L3_DOME_TBU.API.as_view(), name='dome_tbu_L3_v1'),
    # 5G
    path('v1/5G/self_verification', SelfVerificationBot_5G.API.as_view(), name='self_verification_v1'),
    path('v1/5G/self_verification/telegram', SelfVerificationBot_telegram_5G.API.as_view(), name='self_verification_v1'),
    path('v1/5G/self_verification_new_site/telegram', SelfVerificationBotNewSite_telegram_5G.API.as_view(), name='self_verification_new_site_v1'),
    path('v1/5G/self_verification_existing_site/telegram', SelfVerificationBotExistingSite_telegram_5G.API.as_view(), name='self_verification_existing_site_v1'),
    path('v2/5G/self_verification_existing_site/telegram', SelfVerificationBotExistingSite_telegram_5Gv2.API.as_view(), name='self_verification_existing_site_v2'),
    path('v3/5G/self_verification_existing_site/telegram', SelfVerificationBotExistingSite_telegram_5Gv3.API.as_view(), name='self_verification_existing_site_v3'),
    path('v1/5G/un_shut_interface', UnshutPort_5G_telegram.API.as_view(), name='un_shut_interface'),
    path('v1/get_granite_info', L2L3_Get_Granite_Info.API.as_view(), name='get_granite_info_v1'),
    path('v1/5G/verify_csr_connectivity', VerifyCsrConnectivity_5G.API.as_view(), name='verify_csr_connectivity'),
    # 5G Umobile
    path('v1/5G/umobile/verification_new_site', VerificationNewSite.API.as_view(), name='verification_new_site_5G_umobile'),
    path('v1/5G/umobile/verification_existing_site', VerificationExistingSite.API.as_view(), name='verification_existing_site_5G_umobile'),
    # telnet 
    path('v1/telnet_ip_port', telnet_ip_port.api.as_view(), name='telnet_ip_port'),
    # self serve
    path('v1/self_serve/', L2L3_SelfServe.API.as_view(), name='l2_l3_self_serve'),
    path('v1/self_serve/service_testing', L3_SvcTesting_SelfServe.API.as_view(), name='l2_svc_testing_self_serve'),
    path('v2/self_serve/service_testing', L3_SvcTesting_SelfServe_v2.API.as_view(), name='l2_svc_testing_self_serve_v2'),
    path('v1/self_serve/bgp_uptime_status', SelfServe_BGPUptimeStatus.API.as_view(), name='self_serve_bgp_uptime_status'),
    path('v1/self_serve/bgp_uptime_status_data_v2', SelfServe_BGPUptimeStatusv2.API.as_view(), name='self_serve_bgp_uptime_status_data_v2'),
    path('v1/self_serve/insight_verification', SelfServe_InsightVerification.API.as_view(), name='self_serve_insight_verification'),
    # verify xdslam
    path('v1/verify_xdslam', VerifyXdslam.API.as_view(), name='verify_xdslam'),
]

