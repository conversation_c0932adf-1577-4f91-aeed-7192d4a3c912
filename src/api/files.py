from rest_framework.views import APIView #activate REST //post,get,put,delete
from rest_framework.response import Response #return Response JSON associative array
from rest_framework import status # error status code 404
import glob, os, re
from api.serializers import ReadFileSerializer, ReadServiceIdSerializer
from drf_yasg.utils import swagger_auto_schema


class api_configuration(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	def get(self, request):

		try:

			list_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*.txt")]
			return Response({
				'status':'success',
				'data': list_files
			},status=status.HTTP_200_OK)

		except Exception as error:

			return Response({
				'status':'failed',
				'data':str(error)
			},status=status.HTTP_400_BAD_REQUEST)

class api_log(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	def get(self, request):

		try:

			list_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*.txt")]
			return Response({
				'status':'success',
				'data': list_files
			},status=status.HTTP_200_OK)

		except Exception as error:

			return Response({
				'status':'failed',
				'data':str(error)
			},status=status.HTTP_400_BAD_REQUEST)

class api_ne_log(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	def get(self, request):

		try:

			list_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*.txt")]
			return Response({
				'status':'success',
				'data': list_files
			},status=status.HTTP_200_OK)

		except Exception as error:

			return Response({
				'status':'failed',
				'data':str(error)
			},status=status.HTTP_400_BAD_REQUEST)

class api_read_configuration(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		query_serializer=ReadFileSerializer,
	)

	def get(self, request):

		# verify passed data
		input_data = ReadFileSerializer(data=request.query_params)
		input_data.is_valid()

		if len(input_data.errors) > 0:  # check if passed data contains error and return error message
			return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
		else:
			try:
				# Initialize variables using the passed data
				serialized_data = input_data.data

				filename = "/root/home/<USER>/{}".format(serialized_data['filename'])

				f = open(filename, "r")
				content = f.read()

				return Response({
					'status':'success',
					'data': content
				},status=status.HTTP_200_OK)

			except Exception as error:

				return Response({
					'status':'failed',
					'data':str(error)
				},status=status.HTTP_400_BAD_REQUEST)


class api_log_content(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		query_serializer=ReadFileSerializer,
	)

	def get(self, request):

		# verify passed data
		input_data = ReadFileSerializer(data=request.query_params)
		input_data.is_valid()

		if len(input_data.errors) > 0:  # check if passed data contains error and return error message
			return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
		else:
			try:
				# Initialize variables using the passed data
				serialized_data = input_data.data

				filename = "/root/home/<USER>/{}".format(serialized_data['filename'])

				f = open(filename, "r")
				content = f.read()

				return Response({
					'status':'success',
					'data': content
				},status=status.HTTP_200_OK)

			except Exception as error:

				return Response({
					'status':'failed',
					'data':str(error)
				},status=status.HTTP_400_BAD_REQUEST)


class api_ne_log_content(APIView):

	authentication_classes = ()     #exclude from global authentication
	permission_classes = ()

	@swagger_auto_schema(
		query_serializer=ReadFileSerializer,
	)

	def get(self, request):

		# verify passed data
		input_data = ReadFileSerializer(data=request.query_params)
		input_data.is_valid()

		if len(input_data.errors) > 0:  # check if passed data contains error and return error message
			return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
		else:
			try:
				# Initialize variables using the passed data
				serialized_data = input_data.data

				filename = "/root/home/<USER>/{}".format(serialized_data['filename'])

				f = open(filename, "r")
				content = f.read()

				return Response({
					'status':'success',
					'data': content
				},status=status.HTTP_200_OK)

			except Exception as error:

				return Response({
					'status':'failed',
					'data':str(error)
				},status=status.HTTP_400_BAD_REQUEST)


class api_read_log_content(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    @swagger_auto_schema(
        query_serializer=ReadServiceIdSerializer,
    )

    def get(self, request):

        # verify passed data
        input_data = ReadServiceIdSerializer(data=request.query_params)
        input_data.is_valid()

        if len(input_data.errors) > 0:  # check if passed data contains error and return error message
            return Response({'status': 'failed', 'output': 'incomplete/invalid information fetch by API', 'data': input_data.errors})
        else:

            try:

                serialized_data = input_data.data

                # NEWINSTALL, TERMINATE, MODLAN, MODBWADD, MODBWDELETE

                configuration_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*"+serialized_data["service_id"]+"*")]
                ne_log_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*"+serialized_data["service_id"]+"*")]

                ne_log_config_files_data = list()
                orderTypeMap = {"newinstall": "New Install", "ni": "New Install", "new install": "New Install", "terminate": "Terminate", "modlan": "Modify LAN", "modbwadd": "Modify Bandwidth Add", "modbwdelete": "Modify Bandwidth Delete", "modwan": "Modify WAN", "modbw": "Modify Bandwidth  (HOT)"}


                # PE_IPME_MOD_BW_PS00001-ModBW_iMSE21.PUJLAB_25-06-2021_06:39:08_by_SAS_log.txt
                # PE_1GOV_MOD_BW_BS1008025956_iMSE21.PUJLAB_05-07-2021_12:35:37_by_SAS_log.txt
                # PE_1GOV_NI_PS1081637438_M1.iMSE01.KMR_SJA_26-12-2022_03:05:35_by_SAS_log.txt
                # PE_1GOV_NI_PS1081637438_iMSE01.KMR_SJA_26-12-2022_03:05:35_by_SAS_log.txt
                # PE_1GOV_NI_PS1081637438_iMSE01.KMR_26-12-2022_03:05:35_by_SAS_log.txt

                # last = my_str.rsplit(',', 3)[-3]

                # NE LOG
                for val in ne_log_files:

                    temp_dict = dict()
                    order_type = str()
                    status_provision = str()

                    if re.search('SAS', val, re.IGNORECASE):

                        split_dot = val.split('.txt')

                        temp_dict['network_type'] = val.split('_')[0]
                        temp_dict['service_name'] = val.split('_')[1]

                        if val.split('_')[2].strip().lower() in orderTypeMap:
                            order_type = orderTypeMap[val.split('_')[2].strip().lower()]
                        else:
                            order_type = val.split('_')[2].strip()

                        temp_dict['order_type'] = order_type
                        temp_dict['service_id'] = val.split('_')[3]

                        if split_dot[0].rsplit('_', 1)[-1] == 'log':
                            temp_dict['created_on'] = split_dot[0].rsplit('_', 5)[-5] + " " + split_dot[0].rsplit('_', 4)[-4]
                        else:
                            temp_dict['created_on'] = split_dot[0].rsplit('_', 4)[-4] + " " + split_dot[0].rsplit('_', 3)[-3]

                        with open('/root/home/<USER>/'+val, 'r') as file:
                            content = file.read()
                            if 'commit complete' in content:
                                result_provision = 'success'
                            else:
                                result_provision = 'failed'

                        # file_config = open('/root/home/<USER>/'+val, 'r')
                        # config = file_config.read()
                        # config_list = config.split('\n')

                        # for config in config_list:
                        #     if config == 'commit complete':
                        #         status_provision = 'found'
                        #         break
                        #     else:
                        #         continue

                        # if status_provision == 'found':
                        #     result_provision = 'success'
                        # else:
                        #     result_provision = 'failed'

                        temp_dict['provision_status'] = result_provision

                        val_split_underscore = val.split("_")
                        if len(val_split_underscore) == 11:
                            node_name = val_split_underscore[4] + "_" + val_split_underscore[5]
                        elif len(val_split_underscore) == 10:
                            node_name = val_split_underscore[4]
                        else:
                            node_name = val.split('_')[4]

                        temp_dict['node_name'] = node_name
                        temp_dict['file_name'] = val
                        temp_dict['file_type'] = 'NE log'

                        ne_log_config_files_data.append(temp_dict)


                # Configuration FIle
                for val in configuration_files:

                    temp_dict = dict()

                    if re.search('SAS', val, re.IGNORECASE):

                        split_dot = val.split('.txt')

                        temp_dict['network_type'] = val.split('_')[0]
                        temp_dict['service_name'] = val.split('_')[1]

                        if val.split('_')[2].strip().lower() in orderTypeMap:
                            order_type = orderTypeMap[val.split('_')[2].strip().lower()]
                        else:
                            order_type = val.split('_')[2].strip()

                        temp_dict['order_type'] = order_type
                        temp_dict['service_id'] = val.split('_')[3]

                        val_split_underscore = val.split("_")
                        if len(val_split_underscore) == 11:
                            node_name = val_split_underscore[4] + "_" + val_split_underscore[5]
                        elif len(val_split_underscore) == 10:
                            node_name = val_split_underscore[4]
                        else:
                            node_name = val.split('_')[4]

                        temp_dict['node_name'] = node_name

                        if split_dot[0].rsplit('_', 1)[-1] == 'log':
                            temp_dict['created_on'] = split_dot[0].rsplit('_', 5)[-5] + " " + split_dot[0].rsplit('_', 4)[-4]
                        else:
                            temp_dict['created_on'] = split_dot[0].rsplit('_', 4)[-4] + " " + split_dot[0].rsplit('_', 3)[-3]
                        
                        temp_dict['provision_status'] = 'N/A'
                        temp_dict['file_name'] = val
                        temp_dict['file_type'] = 'Config template'

                        ne_log_config_files_data.append(temp_dict)

                return Response({
                    'status':'success',
                    'data': ne_log_config_files_data
                },status=status.HTTP_200_OK)

            except Exception as error:

                return Response({
                    'status':'failed',
                    'data':str(error)
                },status=status.HTTP_400_BAD_REQUEST)

class api_read_sas_log_content(APIView):

    authentication_classes = ()     #exclude from global authentication
    permission_classes = ()

    def get(self, request):

        try:

            configuration_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*.txt")]
            ne_log_files = [os.path.basename(x) for x in glob.glob("/root/home/<USER>/*.txt")]

            ne_log_config_files_data = list()
            orderTypeMap = {"newinstall": "New Install", "ni": "New Install", "new install": "New Install", "terminate": "Terminate", "modlan": "Modify LAN", "modbwadd": "Modify Bandwidth Add", "modbwdelete": "Modify Bandwidth Delete", "modwan": "Modify WAN", "modbw": "Modify Bandwidth  (HOT)"}

            # NE LOG
            for val in ne_log_files:

                temp_dict = dict()
                order_type = str()
                status_provision = str()

                if re.search('SAS', val, re.IGNORECASE):

                    split_dot = val.split('.txt')

                    temp_dict['network_type'] = val.split('_')[0]
                    temp_dict['service_name'] = val.split('_')[1]

                    if val.split('_')[2].strip().lower() in orderTypeMap:
                        order_type = orderTypeMap[val.split('_')[2].strip().lower()]
                    else:
                        order_type = val.split('_')[2].strip()

                    temp_dict['order_type'] = order_type
                    temp_dict['service_id'] = val.split('_')[3]

                    if split_dot[0].rsplit('_', 1)[-1] == 'log':
                        temp_dict['created_on'] = split_dot[0].rsplit('_', 5)[-5] + " " + split_dot[0].rsplit('_', 4)[-4]
                    else:
                        temp_dict['created_on'] = split_dot[0].rsplit('_', 4)[-4] + " " + split_dot[0].rsplit('_', 3)[-3]

                    with open('/root/home/<USER>/'+val, 'r') as file:
                        content = file.read()
                        if 'commit complete' in content:
                            result_provision = 'success'
                        else:
                            result_provision = 'failed'

                    temp_dict['provision_status'] = result_provision

                    val_split_underscore = val.split("_")
                    if len(val_split_underscore) == 11:
                        node_name = val_split_underscore[4] + "_" + val_split_underscore[5]
                    elif len(val_split_underscore) == 10:
                        node_name = val_split_underscore[4]
                    else:
                        node_name = val.split('_')[4]

                    temp_dict['node_name'] = node_name

                    temp_dict['file_name'] = val
                    temp_dict['file_type'] = 'NE log'

                    ne_log_config_files_data.append(temp_dict)


            # Configuration FIle
            for val in configuration_files:

                temp_dict = dict()

                if re.search('SAS', val, re.IGNORECASE):

                    split_dot = val.split('.txt')

                    temp_dict['network_type'] = val.split('_')[0]
                    temp_dict['service_name'] = val.split('_')[1]

                    if val.split('_')[2].strip().lower() in orderTypeMap:
                        order_type = orderTypeMap[val.split('_')[2].strip().lower()]
                    else:
                        order_type = val.split('_')[2].strip()

                    temp_dict['order_type'] = order_type
                    # temp_dict['order_type'] = val.split('_')[2]
                    temp_dict['service_id'] = val.split('_')[3]

                    val_split_underscore = val.split("_")
                    if len(val_split_underscore) == 11:
                        node_name = val_split_underscore[4] + "_" + val_split_underscore[5]
                    elif len(val_split_underscore) == 10:
                        node_name = val_split_underscore[4]
                    else:
                        node_name = val.split('_')[4]

                    temp_dict['node_name'] = node_name

                    if split_dot[0].rsplit('_', 1)[-1] == 'log':
                        temp_dict['created_on'] = split_dot[0].rsplit('_', 5)[-5] + " " + split_dot[0].rsplit('_', 4)[-4]
                    else:
                        temp_dict['created_on'] = split_dot[0].rsplit('_', 4)[-4] + " " + split_dot[0].rsplit('_', 3)[-3]
                    
                    temp_dict['provision_status'] = 'N/A'
                    temp_dict['file_name'] = val
                    temp_dict['file_type'] = 'Config template'

                    ne_log_config_files_data.append(temp_dict)

            return Response({
                'status':'success',
                'data': ne_log_config_files_data
            },status=status.HTTP_200_OK)

        except Exception as error:

            return Response({
                'status':'failed',
                'data':str(error)
            },status=status.HTTP_400_BAD_REQUEST)