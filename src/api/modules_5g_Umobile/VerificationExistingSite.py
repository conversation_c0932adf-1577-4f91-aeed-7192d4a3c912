from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User
# Serializers
from api.serializers_5gUmobile import VerifyNewSiteSerializer
# Swagger
from drf_yasg.utils import swagger_auto_schema
# File and Database
from api.libs import log_text, log_database
# General
import sys, requests, ipaddress, re
from datetime import datetime
from json import dumps
from dotenv import load_dotenv
load_dotenv()


class API(APIView):
    """
    API for verifying existing 5G sites.
    """

	# exclude from global authentication
    authentication_classes = ()
    permission_classes = ()
    
    @swagger_auto_schema(
        request_body=VerifyNewSiteSerializer,
        request_body_example={
            "nid_hostname": "CSRZT41.DQKCH0285",
            "nid_ip": "**************",
            "agg_hostname": "AGGZT41.PPN",
            "agg_port": "xgei-0/2/0/10",
            "staff_id": "s54197"
        },
        tags=['5G Umobile'],
        operation_summary="Verify existing 5G Umobile site",
        operation_description="Verify existing 5G Umobile site",
        responses={
            200: "Site verification successful",
            400: "Bad request",
            # 401: "Unauthorized",
            500: "Internal server error"
        }
    )
    def post(self, request):
        stage = 'Start Process'
        try:
            
            # Get User Id from Token
            token = request.META.get('HTTP_AUTHORIZATION', '').replace('Token ', '')
            user_id = Token.objects.get(key=token).user_id if token else None
            user = User.objects.get(id=user_id).username if user_id else request.data['staff_id']

            '''
            Verify API input format
            '''
            stage = 'Verify Data Format'
            input_data = VerifyNewSiteSerializer(data=request.data)
            if not input_data.is_valid():
                output_msg = 'Stage: ' + stage + '. Invalid input data: ' + str(input_data.errors)
                log_database(
                    data_DB={
                        'order_type': 'Verification',
                        'service_type': '5G Umobile',
                        'input_details': request.data,
                        'message': output_msg,
                        'api_output': str(input_data.errors),
                        'status': 'failed',
                        'log_file': 'N/A',
                        'user': user
                    }
                )
                return Response({
                    'status': 'failed', 
                    'output': output_msg,
                    'data': input_data.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            serialized_data = input_data.validated_data

            # Create log file name
            stage = 'Create Log File'
            date_now = datetime.now().strftime('%d-%m-%Y')
            time_now = datetime.now().strftime('%H:%M:%S')
            log_file = f"5G_Umobile_Existing_Site_Verification_{serialized_data['agg_hostname'].replace('.', '')}_{serialized_data['nid_hostname'].replace('.', '')}_{date_now}@{time_now}_by_{user}.txt" 

            # Log the start of the process
            output_message = f"File created at {date_now} {time_now} for 5G Umobile Existing Site Verification Bot Log"
            log_text(filename=log_file, content=output_message)

            output_message = 'Information fetched by API contains no error'
            log_text(filename=log_file, content=output_message, ne_output=dumps(request.data, indent=4, separators=(',', ': ')))

            # Assign service id, agg_vendor, agg_ip and headers
            serialized_data['service_id'] = serialized_data['nid_hostname']
            serialized_data['nid_interface'] = "xgei-1/1/0/1"
            final_output = []
            headers = {
                'Content-Type': 'application/json',
                'Accept' : 'application/json',
            }

            # if 'hw' in serialized_data['agg_hostname'].lower():
            #     serialized_data['agg_vendor'] = 'Huawei'
            # elif 'zt' in serialized_data['agg_hostname'].lower():
            #     serialized_data['agg_vendor'] = 'ZTE'

            # '''
            # Retrieve AGG details from the database
            # '''
            # stage = 'Retrieve AGG Details'
            # Retrieve the agg_ip and vendor from the API endpoint using agg_hostname

            # https://pisa-dev.tm.com.my/workflow/database/v1/network_element/?search=aggzt22.lab

            # {
            #     "status": "success",
            #     "total": 506,
            #     "page": 1,
            #     "last_page": 51,
            #     "network_element": [
            #         {
            #             "id": "c9d2a1c9-743c-469e-bd05-9d3a389eff3f",
            #             "hostname": "aggzt22.lab",
            #             "neType": "agg",
            #             "loopbackIp": "************",
            #             "vendor": "zte",
            #             "createdAt": "2023-02-01T09:34:02.294045+08:00",
            #             "updatedAt": "2023-02-01T09:34:02.294118+08:00"
            #         }
            #     ]
            # }

            # API endpoint for network element search
            # api_url = f"http://localhost:8000/workflow/database/v1/network_element/"
            # headers = {
			# 	'Content-Type': 'application/json',
			# 	'Accept' : 'application/json',
			# }
            # payload = {
            #     'search': serialized_data['agg_hostname'].lower()
            # }
            
            # response = requests.get(api_url, headers=headers, params=payload, verify=False)  # Set verify=False for self-signed certificates in development
            # output = response.json()

            # if response.status_code == 200:
            #     result = output.get('network_element', [])
            #     if result:
            #         # Assuming the first result is the correct one
            #         serialized_data['agg_loopback_ip'] = result[0].get('loopbackIp')
            #         serialized_data['agg_vendor'] = result[0].get('vendor')

            #         output_msg = f"AGG details retrieved successfully for hostname {serialized_data['agg_hostname'].upper()} from database"
            #         log_text(filename=log_file, content=output_msg, ne_output=dumps(result[0], indent=4, separators=(',', ': ')))
            #     else:
            #         output_msg = f"Stage: {stage}. No matching AGG details found for the provided hostname {serialized_data['agg_hostname'].upper()} in database"
            #         log_text(filename=log_file, content=output_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
            #         log_database(
            #             data_DB={
            #                 'order_type': 'Verification',
            #                 'service_type': '5G Umobile',
            #                 'input_details': serialized_data,
            #                 'message': output_msg,
            #                 'api_output': str(output),
            #                 'status': 'failed',
            #                 'log_file': log_file,
            #                 'user': user
            #             }
            #         )
            #         return Response({
            #             'status': 'failed',
            #             'message': output_msg,
            #             'data': output,
            #             'log_file': log_file
            #         }, status=status.HTTP_400_BAD_REQUEST)
            # else:
            #     error_msg = f"Stage: {stage}. Failed to retrieve AGG details for hostname {serialized_data['agg_hostname'].upper()} from database"
            #     log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
            #     log_database(
            #         data_DB={
            #             'order_type': 'Verification',
            #             'service_type': '5G Umobile',
            #             'input_details': serialized_data,
            #             'message': output_msg,
            #             'api_output': str(output),
            #             'status': 'failed',
            #             'log_file': log_file,
            #             'user': user
            #         }
            #     )
            #     return Response({
            #         'status': 'failed',
            #         'message': output_msg,
            #         'data': output,
            #         'log_file': log_file
            #     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Get the agg_ip from the nid_ip
            ip = f"{serialized_data['nid_ip']}/31"
            network = ipaddress.ip_network(ip, strict=False)
            hosts = list(network.hosts())

            for host in hosts:
                if str(host) != serialized_data['nid_ip']:
                    serialized_data['agg_ip'] = str(host)
                    break
                
            # Determine the agg_vendor based on the agg_hostname
            if 'agghw' in serialized_data['agg_hostname'].lower():
                serialized_data['agg_vendor'] = 'Huawei'
            elif 'aggzt' in serialized_data['agg_hostname'].lower():  
                serialized_data['agg_vendor'] = 'ZTE'

            '''
            AGG HUAWEI
            '''
            if serialized_data['agg_vendor'].lower() == 'huawei':
                # '''
                # Retrieve AGG interface from the NE
                # '''
                # url = 'http://pisa-huawei-svc/huawei/agg/v1/'

                # # Using requests to call the HUAWEI AGG API
                # payload = {
                #     "ne_ip": f"{serialized_data['agg_hostname'].lower()}.tmone.my",
                #     "commands": [
                #         f"display ip interface brief | include {serialized_data['agg_ip']}"
                #     ]
                # }

                # headers = {
                #     'accept': 'application/json',
                #     'Content-Type': 'application/json'
                # }

                # response = requests.post(f"{url}show_config/", json=payload, verify=False)  # Set verify=False for self-signed certificates in developmenturl, headers=headers, json=payload, verify=False)  # Set verify=False for self-signed certificates in development
                # output = response.json()

                # if response.status_code == 200: 
                #     if output.get('status') == 'failed':
                #         error_msg = f"Stage: {stage}. Failed to retrieve AGG interface details for hostname {serialized_data['agg_hostname'].upper()}"
                #         log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #         log_database(
                #             data_DB={
                #                 'order_type': 'Verification',
                #                 'service_type': '5G Umobile',
                #                 'input_details': serialized_data,
                #                 'message': error_msg,
                #                 'api_output': str(output),
                #                 'status': 'failed',
                #                 'log_file': log_file,
                #                 'user': user
                #             }
                #         )
                #         return Response({
                #             'status': 'failed',
                #             'message': error_msg,
                #             'data': output,
                #             'log_file': log_file
                #         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #     else:
                #         ne_config = output.get('data', [])[0].get('ne_response', '') 
                #         if re.search('error', ne_config, re.IGNORECASE):
                #             error_msg = f"Stage: {stage}. Error in AGG interface config details for hostname {serialized_data['agg_hostname'].upper()}"
                #             log_text(filename=log_file, content=error_msg, ne_output=dumps(ne_config, indent=4, separators=(',', ': ')))
                #             log_database(
                #                 data_DB={
                #                     'order_type': 'Verification',
                #                     'service_type': '5G Umobile',
                #                     'input_details': serialized_data,
                #                     'message': error_msg,
                #                     'api_output': str(output),
                #                     'status': 'failed',
                #                     'log_file': log_file,
                #                     'user': user
                #                 }
                #             )
                #             return Response({
                #                 'status': 'failed',
                #                 'message': error_msg,
                #                 'data': output,
                #                 'log_file': log_file
                #             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #         else:
                #             for line in ne_config.splitlines():
                #                 if not re.search('display ip interface brief', line, re.IGNORECASE) and serialized_data['agg_ip'] in line:
                #                     # Extract the interface name and status
                #                     parts = line.split()
                #                     serialized_data['agg_logical_interface'] = parts[0]
                #                     break
                                
                #     if 'agg_logical_interface' not in serialized_data or not re.search('eth-trunk', serialized_data['agg_logical_interface'], re.IGNORECASE):
                #         error_msg = f"Stage: {stage}. Failed to retrieve AGG interface for hostname {serialized_data['agg_hostname'].upper()}"
                #         log_text(filename=log_file, content=error_msg, ne_output=dumps(ne_config, indent=4, separators=(',', ': ')))
                #         log_database(
                #             data_DB={
                #                 'order_type': 'Verification',
                #                 'service_type': '5G Umobile',
                #                 'input_details': serialized_data,
                #                 'message': error_msg,
                #                 'api_output': str(output),
                #                 'status': 'failed',
                #                 'log_file': log_file,
                #                 'user': user
                #             }
                #         )
                #         return Response({
                #             'status': 'failed',
                #             'message': error_msg,
                #             'data': output,
                #             'log_file': log_file
                #         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)    
                #     else:
                #         if '.' in serialized_data['agg_logical_interface']:
                #             # If the interface is a sub-interface, extract the main interface
                #             agg_logical_port = serialized_data['agg_logical_interface'].split('.')[0].replace('Eth-Trunk', 'Eth-Trunk ')
                #         else:
                #             agg_logical_port = serialized_data['agg_logical_interface'].replace('Eth-Trunk', 'Eth-Trunk ')

                #         payload = {
                #             "ne_ip": f"{serialized_data['agg_hostname'].lower()}.tmone.my",
                #             "commands": [
                #                 f"dis lacp statistics {agg_logical_port}"
                #             ]
                #         }

                #         response = requests.post(f"{url}show_config/", json=payload, verify=False)  # Set verify=False for self-signed certificates in development url, headers=headers, json=payload, verify=False)
                #         output = response.json()

                #         if response.status_code == 200:
                #             if output.get('status') == 'failed':
                #                 error_msg = f"Stage: {stage}. Failed to retrieve AGG interface details for hostname {serialized_data['agg_hostname'].upper()}"
                #                 log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #                 log_database(
                #                     data_DB={
                #                         'order_type': 'Verification',
                #                         'service_type': '5G Umobile',
                #                         'input_details': serialized_data,
                #                         'message': error_msg,
                #                         'api_output': str(output),
                #                         'status': 'failed',
                #                         'log_file': log_file,
                #                         'user': user
                #                     }
                #                 )
                #                 return Response({
                #                     'status': 'failed',
                #                     'message': error_msg,
                #                     'data': output,
                #                     'log_file': log_file
                #                 }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #             else:
                #                 ne_config_2 = output.get('data', [])[0].get('ne_response', '')
                #                 if re.search('error', ne_config_2, re.IGNORECASE):
                #                     error_msg = f"Stage: {stage}. Error in AGG interface config details for hostname {serialized_data['agg_hostname'].upper()}"
                #                     log_text(filename=log_file, content=error_msg, ne_output=dumps(ne_config_2, indent=4, separators=(',', ': ')))
                #                     log_database(
                #                         data_DB={
                #                             'order_type': 'Verification',
                #                             'service_type': '5G Umobile',
                #                             'input_details': serialized_data,
                #                             'message': error_msg,
                #                             'api_output': str(output),
                #                             'status': 'failed',
                #                             'log_file': log_file,
                #                             'user': user
                #                         }
                #                     )
                #                     return Response({
                #                         'status': 'failed',
                #                         'message': error_msg,
                #                         'data': output,
                #                         'log_file': log_file
                #                     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #                 else:
                #                     for line in ne_config_2.splitlines():
                #                         if 'ethernet' in line.lower():
                #                             # Extract the interface name and status
                #                             parts = line.split()
                #                             serialized_data['agg_interface'] = parts[0]
                #                             break
                #         else:
                #             error_msg = f"Stage: {stage}. Failed to retrieve AGG interface details for hostname {serialized_data['agg_hostname'].upper()}"
                #             log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #             log_database(
                #                 data_DB={
                #                     'order_type': 'Verification',
                #                     'service_type': '5G Umobile',
                #                     'input_details': serialized_data,
                #                     'message': error_msg,
                #                     'api_output': str(output),
                #                     'status': 'failed',
                #                     'log_file': log_file,
                #                     'user': user
                #                 }
                #             )
                #             return Response({
                #                 'status': 'failed',
                #                 'message': error_msg,
                #                 'data': output,
                #                 'log_file': log_file
                #             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                                        
                # else:
                #     error_msg = f"Stage: {stage}. Failed to retrieve AGG interface for hostname {serialized_data['agg_hostname'].upper()}"
                #     log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #     log_database(
                #         data_DB={
                #             'order_type': 'Verification',
                #             'service_type': '5G Umobile',
                #             'input_details': serialized_data,
                #             'message': error_msg,
                #             'api_output': str(output),
                #             'status': 'failed',
                #             'log_file': log_file,
                #             'user': user
                #         }
                #     )
                #     return Response({
                #         'status': 'failed',
                #         'message': error_msg,
                #         'data': output,
                #         'log_file': log_file
                #     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                                
                # # Log the successful retrieval of AGG interface details
                # output_msg = f"AGG interface {serialized_data['agg_port']} retrieved successfully for hostname {serialized_data['agg_hostname'].upper()}"
                # log_text(filename=log_file, content=output_msg, ne_output=dumps(f"{ne_config}\n\n{ne_config_2}", indent=4, separators=(',', ': ')))

                '''
                Retrive AGG outputs from list of commands
                '''

                url = 'http://pisa-huawei-svc/huawei/agg/v1/'

                stage = 'Retrieve AGG Outputs'
                agg_port_number = re.search(r'\d+/\d+/\d+', serialized_data['agg_port']).group()
                agg_commands = [
                    f"display optical-module brief | i {agg_port_number}",
                    f"display lldp neighbor brief | i {agg_port_number}",
                    f"display interface {serialized_data['agg_port']} | i CRC",
                    F"display interface {serialized_data['agg_port']} | i Description",
                ]

                payload = {
                    "ne_ip": f"{serialized_data['agg_hostname'].lower()}.tmone.my",
                    "commands": agg_commands
                }   

                response = requests.post(f"{url}show_config/", json=payload, headers=headers, verify=False)  # Set verify=False for self-signed certificates in development url, headers=headers, json=payload, verify=False)
                output = response.json()

                if response.status_code == 200:
                    if output.get('status') == 'failed':
                        error_msg = f"Stage: {stage}. Failed to retrieve AGG outputs for hostname {serialized_data['agg_hostname'].upper()}"
                        log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                        log_database(
                            data_DB={
                                'order_type': 'Verification',
                                'service_type': '5G Umobile',
                                'input_details': serialized_data,
                                'message': error_msg,
                                'api_output': str(output),
                                'status': 'failed',
                                'log_file': log_file,
                                'user': user
                            }
                        )
                        return Response({
                            'status': 'failed',
                            'message': error_msg,
                            'data': output,
                            'log_file': log_file
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    else:
                        # Process the output for each command
                        agg_configs = output.get('data')
                        if not agg_configs:
                            error_msg = f"Stage: {stage}. No data returned for AGG outputs for hostname {serialized_data['agg_hostname'].upper()}"
                            log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                            log_database(
                                data_DB={
                                    'order_type': 'Verification',
                                    'service_type': '5G Umobile',
                                    'input_details': serialized_data,
                                    'message': error_msg,
                                    'api_output': str(output),
                                    'status': 'failed',
                                    'log_file': log_file,
                                    'user': user
                                }
                            )
                            return Response({
                                'status': 'failed',
                                'message': error_msg,
                                'data': output,
                                'log_file': log_file
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                        
                        verification_steps = [
                            'power_reading',
                            'lldp_neighbor',
                            'crc_error',
                            'interface_description'
                        ]

                        verification_output = {}
                        #  Iterate through the commands and their outputs
                        for index, config in enumerate(agg_configs):
                            if verification_steps[index] == 'power_reading':
                                '''
                                Verify Power Reading
                                '''
                                stage = 'Verify Power Reading (AGG)'
                                fiber_reading_config = config.get('ne_response', '')
                                if re.search('error', fiber_reading_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Fiber reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in fiber reading config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                else:
                                    
                                    power_reading_exist = False

                                    for line in fiber_reading_config.splitlines():
                                        if agg_port_number in line and 'dBm' in line:
                                            # Extract the power reading value
                                            parts = line.split()
                                            sfp_type = parts[3]
                                            rx_power = parts[5].replace('dBm', '')
                                            tx_power = parts[6].replace('dBm', '')
                                            power_reading_exist = True
                                            break
                                        
                                    if power_reading_exist:
                                        # Check if the power readings are within acceptable limits
                                        rx_power_check, tx_power_check = False, False
                                        thresholds = {
                                            "10km": {
                                                'RxPower': [-14, 0.5],
                                                'TxPower': [-7.898, 0.499],
                                            },
                                            "40km": {
                                                'RxPower': [-15, -1],
                                                'TxPower': [-4.4, 3.999],
                                            },
                                            "80km": {
                                                'RxPower': [-23.1, -7],
                                                'TxPower': [-0.3, 5],
                                            },
                                        }

                                        for dist in list(thresholds.keys()):
                                            if re.search(dist, sfp_type, re.IGNORECASE):
                                                serialized_data['agg_sfp_type'] = dist
                                                if thresholds[dist]['RxPower'][0] <= float(rx_power) <= thresholds[dist]['RxPower'][1]:
                                                    rx_power_check = True
                                                if thresholds[dist]['TxPower'][0] <= float(tx_power) <= thresholds[dist]['TxPower'][1]:
                                                    tx_power_check = True
                                                break

                                        if rx_power_check and tx_power_check:
                                            output_msg = f"Fiber Reading verification successful for hostname {serialized_data['agg_hostname'].upper()}. RX Power: {rx_power}dBm and TX Power: {tx_power}dBm are within threshold limits"
                                            verification_output = { 'status': True , 'detail' : output_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                        elif not rx_power_check and not tx_power_check:
                                            error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. RX Power: {rx_power}dBm and TX Power: {tx_power}dBm are out of threshold limits"
                                            verification_output = { 'status': False , 'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                        elif not rx_power_check:
                                            error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. RX Power: {rx_power}dBm is out of threshold limits"
                                            verification_output = { 'status': False,  'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                        # elif not tx_power_check:
                                        else:
                                            error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. TX Power: {tx_power}dBm is out of threshold limits"
                                            verification_output = { 'status': False,  'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                    else:
                                        error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. No power reading found for port {agg_port_number}"
                                        verification_output = { 'status': 'undefined',  'detail' : error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                
                                # Collect the result_output status
                                agg_output = {'agg': {'power_reading': verification_output}}
                                        
                            elif verification_steps[index] == 'lldp_neighbor':
                                '''
                                Verify LLDP neighbor
                                '''
                                verification_output = {}
                                stage = 'Verify LLDP neighbor (AGG)'
                                lldp_neighbor_config = config.get('ne_response', '')
                                if re.search('error', lldp_neighbor_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. LLDP neighbor verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in LLDP neighbor config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                else:
                                    matched = False
                                    for line in lldp_neighbor_config.splitlines():
                                        if re.search(serialized_data['nid_interface'], line, re.IGNORECASE) and re.search(serialized_data['agg_port'], line, re.IGNORECASE) and not 'display lldp neighbor brief' in line.lower():
                                            output_msg = f"LLDP neighbor verification successful for hostname {serialized_data['agg_hostname'].upper()}. Found matching interface {serialized_data['nid_interface']} and {serialized_data['agg_port']}"
                                            verification_output = { 'status' : True, 'detail' : output_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))
                                            matched = True
                                            break
                                        
                                    if not matched:
                                        error_msg = f"LLDP neighbor verification failed for hostname {serialized_data['agg_hostname'].upper()}. No matching interface found for {serialized_data['nid_interface']} and {serialized_data['agg_port']}"
                                        verification_output = { 'status' : False, 'detail' : error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))

                                # Collect the result_output status
                                agg_output['agg']['lldp_neighbor'] = verification_output

                            elif verification_steps[index] == 'crc_error':
                                '''
                                Verify CRC error
                                '''
                                verification_output = {}
                                stage = 'Verify CRC error (AGG)'
                                crc_error_config = config.get('ne_response', '')
                                if re.search('invalid', crc_error_config, re.IGNORECASE) and re.search('%error', crc_error_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. CRC error verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in CRC config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                else:
                                    # <agghw41.lab>display interface GigabitEthernet0/2/1 | i CRC 
                                    # 2025-06-20 15:47:15.659 +08:00
                                    # Info: It will take a long time if the content you search is too much or the string you input is too long, you can press CTRL_C to break.
                                    #     CRC: 0 packets, Symbol: 0 packets

                                    for line in crc_error_config.splitlines():
                                        if 'CRC:' in line:
                                            # Extract the CRC error count
                                            crc_error_count = re.search(r'CRC:\s*(\d+)\s*packets', line)
                                            if crc_error_count:
                                                crc_error_count = int(crc_error_count.group(1))
                                                if crc_error_count > 0:
                                                    error_msg = f"CRC error verification failed for hostname {serialized_data['agg_hostname'].upper()}. Found {crc_error_count} CRC errors"
                                                    verification_output = { 'status' : False, 'detail' : error_msg }
                                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                                else:
                                                    output_msg = f"CRC error verification successful for hostname {serialized_data['agg_hostname'].upper()}. No CRC errors found"
                                                    verification_output = { 'status' : True, 'detail' : output_msg }
                                                    log_text(filename=log_file, content=output_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                            else:
                                                error_msg = f"CRC error verification failed for hostname {serialized_data['agg_hostname'].upper()}. No CRC information found in config"
                                                verification_output = { 'status' : False, 'detail' : error_msg }
                                                log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))

                                # Collect the result_output status
                                agg_output['agg']['crc_error'] = verification_output

                            elif verification_steps[index] == 'interface_description':
                                '''
                                Verify Interface Description
                                '''
                                verification_output = {}
                                stage = 'Verify Interface Description (AGG)'
                                is_dwdm = False
                                interface_description_config = config.get('ne_response', '')
                                if re.search('error', interface_description_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in interface description config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                else:
                                    agg_interface_description = ''
                                    # Get the interface description
                                    for line in interface_description_config.splitlines():
                                        if re.search('description', line, re.IGNORECASE) and not 'display interface' in line.lower():
                                            # Split at 'Description:' and take the part after it
                                            _, _, value = line.partition("Description:")

                                            # Remove any leading/trailing spaces
                                            agg_interface_description = value.strip()

                                    if not agg_interface_description:
                                        error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. No interface description found for interface {serialized_data['agg_port']}"
                                        verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                    else:
                                        # Check if the description contains the required information
                                        if not (re.search(serialized_data['nid_hostname'], agg_interface_description, re.IGNORECASE) and re.search(serialized_data['nid_interface'], agg_interface_description, re.IGNORECASE)):
                                            error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Interface description does not match with NID hostname {serialized_data['nid_hostname']} and interface {serialized_data['nid_interface']}"
                                            verification_output = { 'status' : False, 'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                        else:
                                            # Verify if the interface is DWDm or not
                                            if re.search('obw', agg_interface_description, re.IGNORECASE):
                                                is_dwdm = True

                                            # Verify distance match sfp type
                                            if not is_dwdm:
                                                total_km = 0
                                                fiber_reading_range = {
                                                    "10km" : [0, 10],
                                                    "40km" : [10.1, 40],
                                                    "80km" : [40.1, 80]
                                                }
                                                km_values = re.findall(r'(\d+(?:\.\d+)?)KM', agg_interface_description, re.IGNORECASE)
                                                total_km = sum(float(km) for km in km_values)

                                                sfp_match = False
                                                if total_km > 0:
                                                    value = fiber_reading_range[serialized_data['agg_sfp_type']]
                                                    if value[0] <= total_km <= value[1]:
                                                        sfp_match = True
                                                        
                                                    if sfp_match:
                                                        output_msg = f"Interface description verification successful for hostname {serialized_data['agg_hostname'].upper()}. Interface description matches with NID hostname {serialized_data['nid_hostname']} and interface {serialized_data['nid_interface']} and fiber distance {total_km}km within the range with SFP type {serialized_data['agg_sfp_type']}"
                                                        verification_output = { 'status' : True, 'detail' : output_msg }
                                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                                    else:
                                                        error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Fiber distance {total_km}km not within the range for SFP type {serialized_data['agg_sfp_type']}"
                                                        verification_output = { 'status' : False, 'detail' : error_msg }
                                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                                        
                                                else:
                                                    error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. No fiber distance found in interface description"
                                                    verification_output = { 'status' : False, 'detail' : error_msg }
                                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                            else:
                                                serialized_data['agg_sfp_type'] = '10km'
                                                output_msg = f"Interface description verification successful for hostname {serialized_data['agg_hostname'].upper()}. Interface description matches with NID hostname {serialized_data['nid_hostname']} and interface {serialized_data['nid_interface']}"
                                                verification_output = { 'status' : True, 'detail' : output_msg }

                                agg_output['agg']['interface_description'] = verification_output

                    #  Collect the result_output status
                    final_output.append(agg_output)

                else:
                    error_msg = f"Stage: {stage}. Failed to retrieve AGG outputs for hostname {serialized_data['agg_hostname'].upper()}"
                    log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                    log_database(
                        data_DB={
                            'order_type': 'Verification',
                            'service_type': '5G Umobile',
                            'input_details': serialized_data,
                            'message': error_msg,
                            'api_output': str(output),
                            'status': 'failed',
                            'log_file': log_file,
                            'user': user
                        }
                    )
                    return Response({
                        'status': 'failed',
                        'message': error_msg,
                        'data': output,
                        'log_file': log_file
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                '''
                AGG ZTE
                '''
            elif serialized_data['agg_vendor'].lower() == 'zte':
                
                # '''
                # Retrieve AGG interface from the NE
                # '''
                
                # url = 'http://pisa-zte-svc/zte/agg/'

                # # Using requests to call the ZTE AGG API
                # payload = {
                #     "ne_ip": f"{serialized_data['agg_hostname'].lower()}.tmone.my",
                #     "commands": [
                #         f"show ip interface brief | include {serialized_data['agg_ip']}"
                #     ]
                # }
                
                # headers = {
                #     'accept': 'application/json',
                #     'Content-Type': 'application/json'
                # }
                
                # response = requests.post(f"{url}v1/show_config/", json=payload, headers=headers, verify=False)
                # output = response.json()

                # if response.status_code == 200:
                #     if output.get('status') == 'failed':
                #         error_msg = f"Stage: {stage}. Failed to retrieve AGG interface details for hostname {serialized_data['agg_hostname'].upper()}"
                #         log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #         log_database(
                #             data_DB={
                #                 'order_type': 'Verification',
                #                 'service_type': '5G Umobile',
                #                 'input_details': serialized_data,
                #                 'message': error_msg,
                #                 'api_output': str(output),
                #                 'status': 'failed',
                #                 'log_file': log_file,
                #                 'user': user
                #             }
                #         )
                #         return Response({
                #             'status': 'failed',
                #             'message': error_msg,
                #             'data': output,
                #             'log_file': log_file
                #         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #     else:
                #         ne_config = output.get('data', [])[0].get('ne_response', '') 
                #         if re.search('error', ne_config, re.IGNORECASE):
                #             error_msg = f"Stage: {stage}. Error in AGG interface config details for hostname {serialized_data['agg_hostname'].upper()}"
                #             output_msg = f"{error_msg}\n [API Response: {ne_config}]"
                #             log_text(filename=log_file, content=output_msg)
                #             log_database(
                #                 data_DB={
                #                     'order_type': 'Verification',
                #                     'service_type': '5G Umobile',
                #                     'input_details': serialized_data,
                #                     'message': error_msg,
                #                     'api_output': str(output),
                #                     'status': 'failed',
                #                     'log_file': log_file,
                #                     'user': user
                #                 }
                #             )
                #             return Response({
                #                 'status': 'failed',
                #                 'message': error_msg,
                #                 'data': output,
                #                 'log_file': log_file
                #             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #         else:
                #             for line in ne_config.splitlines():
                #                 if not 'show ip interface brief' in line.lower() and serialized_data['agg_ip'] in line:
                #                     # Extract the interface name and status
                #                     parts = line.split()
                #                     serialized_data['agg_logical_interface'] = parts[0]  # Assuming the first part is the interface name
                #                     break
                                
                #     if 'agg_logical_interface' not in serialized_data or not re.search('smartgroup', serialized_data['agg_logical_interface'], re.IGNORECASE):
                #         error_msg = f"Stage: {stage}. Failed to retrieve AGG interface for hostname {serialized_data['agg_hostname'].upper()}"
                #         log_text(filename=log_file, content=error_msg, ne_output=dumps(ne_config, indent=4, separators=(',', ': ')))
                #         log_database(
                #             data_DB={
                #                 'order_type': 'Verification',
                #                 'service_type': '5G Umobile',
                #                 'input_details': serialized_data,
                #                 'message': error_msg,
                #                 'api_output': str(output),
                #                 'status': 'failed',
                #                 'log_file': log_file,
                #                 'user': user
                #             }
                #         )
                #         return Response({
                #             'status': 'failed',
                #             'message': error_msg,
                #             'data': output,
                #             'log_file': log_file
                #         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    
                #     else:
                        
                #         if '.' in serialized_data['agg_logical_interface']:
                #             # If the interface is a sub-interface, extract the main interface
                #             agg_logical_port = serialized_data['agg_logical_interface'].split('.')[0].replace('smartgroup', '')
                #         else:
                #             agg_logical_port = serialized_data['agg_logical_interface'].replace('smartgroup', '')

                #         payload = {
                #             "ne_ip": f"{serialized_data['agg_hostname'].lower()}.tmone.my",
                #             "commands": [
                #                 f"show lacp {agg_logical_port} neighbors"
                #             ]
                #         }
                        
                #         headers = {
                #             'accept': 'application/json',
                #             'Content-Type': 'application/json'
                #         }
                        
                #         response = requests.post(f"{url}v1/show_config/", json=payload, headers=headers, verify=False)
                #         output = response.json()

                #         if response.status_code == 200:
                #             if output.get('status') == 'failed':
                #                 error_msg = f"Stage: {stage}. Failed to retrieve AGG interface details for hostname {serialized_data['agg_hostname'].upper()}"
                #                 log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #                 log_database(
                #                     data_DB={
                #                         'order_type': 'Verification',
                #                         'service_type': '5G Umobile',
                #                         'input_details': serialized_data,
                #                         'message': error_msg,
                #                         'api_output': str(output),
                #                         'status': 'failed',
                #                         'log_file': log_file,
                #                         'user': user
                #                     }
                #                 )
                #                 return Response({
                #                     'status': 'failed',
                #                     'message': error_msg,
                #                     'data': output,
                #                     'log_file': log_file
                #                 }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #             else:
                #                 ne_config_2 = output.get('data', [])[0].get('ne_response', '')
                #                 if re.search('error', ne_config_2, re.IGNORECASE):
                #                     error_msg = f"Stage: {stage}. Error in AGG interface config details for hostname {serialized_data['agg_hostname'].upper()}"
                #                     log_text(filename=log_file, content=error_msg, ne_output=dumps(ne_config_2, indent=4, separators=(',', ': ')))
                #                     log_database(
                #                         data_DB={
                #                             'order_type': 'Verification',
                #                             'service_type': '5G Umobile',
                #                             'input_details': serialized_data,
                #                             'message': output_msg,
                #                             'api_output': str(output),
                #                             'status': 'failed',
                #                             'log_file': log_file,
                #                             'user': user
                #                         }
                #                     )
                #                     return Response({
                #                         'status': 'failed',
                #                         'message': output_msg,
                #                         'data': output,
                #                         'log_file': log_file
                #                     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                #                 else:
                #                     for line in ne_config_2.splitlines():
                #                         if 'gei' in line.lower():
                #                             # Extract the interface name and status
                #                             parts = line.split()
                #                             serialized_data['agg_interface'] = parts[0]
                #                             break
                #         else:
                #             error_msg = f"Stage: {stage}. Failed to retrieve AGG interface details for hostname {serialized_data['agg_hostname'].upper()}"
                #             log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #             log_database(
                #                 data_DB={
                #                     'order_type': 'Verification',
                #                     'service_type': '5G Umobile',
                #                     'input_details': serialized_data,
                #                     'message': error_msg,
                #                     'api_output': str(output),
                #                     'status': 'failed',
                #                     'log_file': log_file,
                #                     'user': user
                #                 }
                #             )
                #             return Response({
                #                 'status': 'failed',
                #                 'message': error_msg,
                #                 'data': output,
                #                 'log_file': log_file
                #             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    
                # else:
                #     error_msg = f"Stage: {stage}. Failed to retrieve AGG interface for hostname {serialized_data['agg_hostname'].upper()}"
                #     log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                #     log_database(
                #         data_DB={
                #             'order_type': 'Verification',
                #             'service_type': '5G Umobile',
                #             'input_details': serialized_data,
                #             'message': error_msg,
                #             'api_output': str(output),
                #             'status': 'failed',
                #             'log_file': log_file,
                #             'user': user
                #         }
                #     )
                #     return Response({
                #         'status': 'failed',
                #         'message': error_msg,
                #         'data': output,
                #         'log_file': log_file
                #     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                                
                # # Log the successful retrieval of AGG interface details
                # output_msg = f"AGG interface {serialized_data['agg_interface']} retrieved successfully for hostname {serialized_data['agg_hostname'].upper()}"
                # log_text(filename=log_file, content=output_msg, ne_output=dumps(f"{ne_config}\n\n{ne_config_2}", indent=4, separators=(',', ': ')))
            
                '''
                Retrive AGG outputs from list of commands
                '''

                url = 'http://pisa-zte-svc/zte/agg/'

                
                stage = 'Retrieve AGG Outputs'
                agg_port_number = serialized_data['agg_port'].split('-')[1]
                agg_commands = [
                    f"show opticalinfo brief | include {agg_port_number}",
                    f"show lldp neighbor interface {serialized_data['agg_port']}",
                    f"show interface {serialized_data['agg_port']} | include CRC",
                    F"show interface description | include {serialized_data['nid_hostname']}",
                ]

                # Using requests to call the ZTE AGG API
                payload = {
                    "ne_ip": f"{serialized_data['agg_hostname'].lower()}.tmone.my",
                    "commands": agg_commands
                }
                response = requests.post(f"{url}v1/show_config/", json=payload, headers=headers, verify=False)
                output = response.json()

                if response.status_code == 200:
                    if output.get('status') == 'failed':
                        error_msg = f"Stage: {stage}. Failed to retrieve AGG outputs for hostname {serialized_data['agg_hostname'].upper()}"
                        log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                        log_database(
                            data_DB={
                                'order_type': 'Verification',
                                'service_type': '5G Umobile',
                                'input_details': serialized_data,
                                'message': error_msg,
                                'api_output': str(output),
                                'status': 'failed',
                                'log_file': log_file,
                                'user': user
                            }
                        )
                        return Response({
                            'status': 'failed',
                            'message': error_msg,
                            'data': output,
                            'log_file': log_file
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    else:
                        # Process the retrieved data
                        agg_configs = output.get('data')
                        if not agg_configs:
                            error_msg = f"Stage: {stage}. No data retrieved for AGG outputs for hostname {serialized_data['agg_hostname'].upper()}"
                            log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                            log_database(
                                data_DB={
                                    'order_type': 'Verification',
                                    'service_type': '5G Umobile',
                                    'input_details': serialized_data,
                                    'message': error_msg,
                                    'api_output': str(output),
                                    'status': 'failed',
                                    'log_file': log_file,
                                    'user': user
                                }
                            )
                            return Response({
                                'status': 'failed',
                                'message': error_msg,
                                'data': output,
                                'log_file': log_file
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                        
                        verification_steps = [
                            'power_reading',
                            'lldp_neighbor',
                            'crc_error',
                            'interface_description'
                        ]

                        verification_output = []
                        # Start processing the retrieved data
                        for index, data in enumerate(agg_configs):
                            if verification_steps[index] == 'power_reading':
                                '''
                                Verify power reading
                                '''
                                stage = 'Verify Power Reading (AGG)'
                                fiber_reading_config = data.get('ne_response', '')
                                if re.search('error', fiber_reading_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in fiber reading config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined', 'detail' : error_msg}
                                else:
                                    
                                    power_reading_exist = False
                                    
                                    for line in fiber_reading_config.splitlines():
                                        if serialized_data['agg_port'] in line:
                                            # Extract the power reading value
                                            parts = line.split()
                                            sfp_type = parts[1]  # Assuming the first part is the SFP type
                                            rx_power = parts[3].split('/')[0]  # Assuming the second part is the RX power
                                            tx_power = parts[4].split('/')[0]  # Assuming the third part is the TX power
                                            power_reading_exist = True
                                            break
                                        
                                    if power_reading_exist:
                                    
                                        # Check if the power readings are within acceptable limits
                                        rx_power_check, tx_power_check = False, False
                                        thresholds = {
                                            "10km": {
                                                'RxPower': [-14, 0.5],
                                                'TxPower': [-7.898, 0.499],
                                            },
                                            "40km": {
                                                'RxPower': [-15, -1],
                                                'TxPower': [-4.4, 3.999],
                                            },
                                            "80km": {
                                                'RxPower': [-23.1, -7],
                                                'TxPower': [-0.3, 5],
                                            },
                                        }

                                        for dist in list(thresholds.keys()):
                                            if re.search(dist, sfp_type, re.IGNORECASE):
                                                serialized_data['agg_sfp_type'] = dist
                                                if thresholds[dist]['RxPower'][0] <= float(rx_power) <= thresholds[dist]['RxPower'][1]:
                                                    rx_power_check = True
                                                if thresholds[dist]['TxPower'][0] <= float(tx_power) <= thresholds[dist]['TxPower'][1]:
                                                    tx_power_check = True
                                                break

                                        if rx_power_check and tx_power_check:
                                            output_msg = f"Fiber Reading verification successful for hostname {serialized_data['agg_hostname'].upper()}. RX Power: {rx_power}dBm and TX Power: {tx_power}dBm are within threshold limits"
                                            verification_output = { 'status': True , 'detail' : output_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                        elif not rx_power_check and not tx_power_check:
                                            error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. RX Power: {rx_power}dBm and TX Power: {tx_power}dBm are out of threshold limits"
                                            verification_output = { 'status': False , 'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                        elif not rx_power_check:
                                            error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. RX Power: {rx_power}dBm is out of threshold limits"
                                            verification_output = { 'status': False,  'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                        # elif not tx_power_check:
                                        else:
                                            error_msg = f"Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. TX Power: {tx_power}dBm is out of threshold limits"
                                            verification_output = { 'status': False,  'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                    else:
                                        error_msg = f"Stage: {stage}. Fiber Reading verification failed for hostname {serialized_data['agg_hostname'].upper()}. Power reading not found for port {serialized_data['agg_port']}"
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                        verification_output = { 'status': 'undefined', 'detail' : error_msg}
                                
                                # Collect the result_output status
                                agg_output = {'agg': {'power_reading': verification_output}}

    
                            elif verification_steps[index] == 'lldp_neighbor':
                                '''
                                Verify LLDP neighbor
                                '''
                                verification_output = {}
                                stage = 'Verify LLDP Neighbor (AGG)'
                                lldp_neighbor_config = data.get('ne_response', '')
                                if re.search('error', lldp_neighbor_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. LLDP Neighbor verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in LLDP neighbor config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined' , 'detail' : error_msg }
                                else:
                                    matched = False
                                    for line in lldp_neighbor_config.splitlines():
                                        if re.search(serialized_data['agg_port'], line, re.IGNORECASE) and re.search(serialized_data['nid_interface'], line, re.IGNORECASE) and not 'show lldp neighbor' in line.lower():
                                            output_msg = f"LLDP Neighbor verification successful for hostname {serialized_data['agg_hostname'].upper()}. Neighbor interface {serialized_data['nid_interface']} found in LLDP neighbor output"
                                            verification_output = { 'status': True,  'detail' : output_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))
                                            matched = True
                                            break
                                            
                                    if not matched:
                                        error_msg = f"LLDP Neighbor verification failed for hostname {serialized_data['agg_hostname'].upper()}. Neighbor interface {serialized_data['nid_interface']} not found in LLDP neighbor output"
                                        verification_output = { 'status': False, 'detail' : error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))
                                    
                                # Add to agg_output
                                agg_output['agg']['lldp_neighbor'] = verification_output
                                    
                            elif verification_steps[index] == 'crc_error':
                                '''
                                Verify CRC error
                                '''
                                verification_output = {}
                                stage = 'Verify CRC Error (AGG)'
                                crc_error_config = data.get('ne_response', '')  
                                if re.search('invalid', crc_error_config, re.IGNORECASE) and re.search('%error', crc_error_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. CRC Error verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in CRC Error config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined' , 'detail' : error_msg }
                                else:
                                    for line in crc_error_config.splitlines():
                                        if re.search('In_CRC_ERROR', line, re.IGNORECASE):
                                            crc_error = int(line.split()[1])    
                                            break
                                    if crc_error >  0:
                                        error_msg = f"CRC Error verification failed for hostname {serialized_data['agg_hostname'].upper()}. CRC Error count is {crc_error}"
                                        verification_output = { 'status': False,  'detail' : error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                    else:
                                        output_msg = f"CRC Error verification successful for hostname {serialized_data['agg_hostname'].upper()}. CRC Error count is {crc_error}"
                                        verification_output = { 'status': True, 'detail' : output_msg }
                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                # Add to agg_output
                                agg_output['agg']['crc_error'] = verification_output

                            elif verification_steps[index] == 'interface_description':
                                '''
                                Verify Interface Description
                                '''
                                verification_output = {}
                                stage = 'Verify Interface Description (AGG)'
                                is_dwdm = False
                                interface_description_config = data.get('ne_response', '')
                                if re.search('error', interface_description_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Error in interface description config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                else:
                                    agg_interface_description = ''
                                    # Get the interface description
                                    for line in interface_description_config.splitlines():
                                        if re.search(serialized_data['agg_port'], line, re.IGNORECASE):
                                            # The line format has interface name, status fields, and then description
                                            # Split the line by whitespace and get the description part
                                            parts = line.split()
                                            # Check if we have enough parts and the interface name matches
                                            if len(parts) >= 5 and serialized_data['agg_port'] in parts[0]:
                                                # Join all parts from the 5th element onwards to get the description
                                                agg_interface_description = ' '.join(parts[4:])
                                                break

                                    if not agg_interface_description:
                                        error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. No interface description found for interface {serialized_data['agg_port']}"
                                        verification_output = { 'status' : 'undefined', 'detail' : error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                    else:
                                        # Check if the description contains the required information
                                        if not (re.search(serialized_data['nid_hostname'], agg_interface_description, re.IGNORECASE) and re.search(serialized_data['nid_interface'], agg_interface_description, re.IGNORECASE)):
                                            error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Interface description does not match with NID hostname {serialized_data['nid_hostname']} and interface {serialized_data['nid_interface']}"
                                            verification_output = { 'status' : False, 'detail' : error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                        else:
                                            # Verify if the interface is DWDm or not
                                            if re.search('obw', agg_interface_description, re.IGNORECASE):
                                                is_dwdm = True

                                            # Verify distance match sfp type
                                            if not is_dwdm:
                                                total_km = 0
                                                fiber_reading_range = {
                                                    "10km" : [0, 10],
                                                    "40km" : [10.1, 40],
                                                    "80km" : [40.1, 80]
                                                }
                                                km_values = re.findall(r'(\d+(?:\.\d+)?)KM', agg_interface_description, re.IGNORECASE)
                                                total_km = sum(float(km) for km in km_values)

                                                sfp_match = False
                                                if total_km > 0:
                                                    range_value = fiber_reading_range[serialized_data['agg_sfp_type']]
                                                    if range_value[0] <= total_km <= range_value[1]:
                                                        sfp_match = True
                                                        
                                                    if sfp_match:
                                                        output_msg = f"Interface description verification successful for hostname {serialized_data['agg_hostname'].upper()}. Interface description matches with NID hostname {serialized_data['nid_hostname']} and interface {serialized_data['nid_interface']} and fiber distance {total_km}km within the range with SFP type {serialized_data['agg_sfp_type']}"
                                                        verification_output = { 'status' : True, 'detail' : output_msg }
                                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                                    else:
                                                        error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Fiber distance {total_km}km not within the range for SFP type {serialized_data['agg_sfp_type']}"
                                                        verification_output = { 'status' : False, 'detail' : error_msg }
                                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                                        
                                                else:
                                                    error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. No fiber distance found in interface description"
                                                    verification_output = { 'status' : False, 'detail' : error_msg }
                                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                            else:
                                                serialized_data['agg_sfp_type'] = '10km'
                                                output_msg = f"Interface description verification successful for hostname {serialized_data['agg_hostname'].upper()}. Interface description matches with NID hostname {serialized_data['nid_hostname']} and interface {serialized_data['nid_interface']}"
                                                verification_output = { 'status' : True, 'detail' : output_msg }

                                agg_output['agg']['interface_description'] = verification_output

                    #  Collect the result_output status
                    final_output.append(agg_output)

                else:
                    error_msg = f"Stage: {stage}. Failed to retrieve AGG outputs for hostname {serialized_data['agg_hostname'].upper()}"
                    log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                    log_database(
                        data_DB={
                            'order_type': 'Verification',
                            'service_type': '5G Umobile',
                            'input_details': serialized_data,
                            'message': error_msg,
                            'api_output': str(output),
                            'status': 'failed',
                            'log_file': log_file,
                            'user': user
                        }
                    )
                    return Response({
                        'status': 'failed',
                        'message': error_msg,
                        'data': output,
                        'log_file': log_file
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            '''
            NID ZTE
            '''
            stage = 'Retrieve NID Outputs'
            url = 'http://pisa-zte-svc/zte/csr/v1/'

            nid_commands = [
                "show power",
                f"show opticalinfo brief | include {serialized_data['nid_interface']}",
                # f"show lldp neighbor brief interface {serialized_data['nid_interface']}",
                f"show lldp entry",
                f"show running-config-interface {serialized_data['nid_interface']} | include description",
                f"show interface {serialized_data['nid_interface']} | include CRC",
                f"show license port-enable resource-permanent",
                f"show license port-enable port-state brief",
                f"show version",
            ]

            payload = {
                # "agg_hostname": serialized_data['agg_hostname'],
                # "agg_ip": serialized_data['agg_ip'],
                # "nid_ip": serialized_data['nid_ip'],
                "nid_ip": serialized_data['nid_ip'],
                "commands": nid_commands
            }
            headers = {
                'accept': 'application/json',
                'Content-Type': 'application/json'
            }

            response = requests.post(f"{url}show_config_multiple_commands/", json=payload, headers=headers, verify=False)
            output = response.json()
            if response.status_code == 200:
                if output.get('status') == 'failed':
                    error_msg = f"Stage: {stage}. Failed to retrieve NID outputs for hostname {serialized_data['nid_hostname'].upper()}"
                    log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                    log_database(
                        data_DB={
                            'order_type': 'Verification',
                            'service_type': '5G Umobile',
                            'input_details': serialized_data,
                            'message': error_msg,
                            'api_output': str(output),
                            'status': 'failed',
                            'log_file': log_file,
                            'user': user
                        }
                    )
                    # return Response({
                    #     'status': 'failed',
                    #     'message': output_msg,
                    #     'data': output,
                    #     'log_file': log_file
                    # }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                else:
                    # Process the retrieved data
                    nid_data = output.get('data', [])
                    if not nid_data:
                        error_msg = f"Stage: {stage}. No data retrieved for NID outputs for hostname {serialized_data['nid_hostname'].upper()}"
                        log_text(filename=log_file, content=error_msg, ne_output=dumps(output, indent=4, separators=(',', ': ')))
                        log_database(
                            data_DB={
                                'order_type': 'Verification',
                                'service_type': '5G Umobile',
                                'input_details': serialized_data,
                                'message': error_msg,
                                'api_output': str(output),
                                'status': 'failed',
                                'log_file': log_file,
                                'user': user
                            }
                        )
                        # return Response({
                        #     'status': 'failed',
                        #     'message': output_msg,
                        #     'data': output,
                        #     'log_file': log_file
                        # }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    else:
                        verification_steps = [
                            "power",
                            "power_reading",
                            "lldp_neighbor",
                            "interface_description",
                            "crc_error",
                            "license_port_enable",
                            "license_port_state",
                            "version"
                        ]

                        verification_output = []
                        # Start processing the retrieved data
                        for index, data in enumerate(nid_data):
                            if verification_steps[index] == "power":
                                '''
                                Verify power
                                '''
                                stage = "Verify Power (NID)"
                                power_config = data.get('ne_response', '')
                                if re.search('error', power_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Power verification failed for hostname {serialized_data['nid_hostname'].upper()}. Error in power config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(power_config, indent=4, separators=(',', ': ')))
                                    verification_output = {'status': 'undefined' , 'detail': error_msg}
                                else:

                                    # Define important variables
                                    temperatures, in_temperature_table = {}, False
                                    
                                    for line in power_config.splitlines():
                                        # Detect the start of the temperature table
                                        if "Power  Power       Power     Output" in line:
                                            in_temperature_table = True
                                            continue
                                        # Process lines in the temperature table
                                        if in_temperature_table:
                                            # Match lines with power module data
                                            # Example: "A0     41C         400.00W   48.18V   2.52A   121.84W    278.16W   48.18V  121.84W   N/A"
                                            match = re.match(r'^(\w+)\s+(\S+)\s+\S+\s+\S+\s+\S+\s+\S+\s+\S+\s+\S+\s+\S+\s+\S+', line)
                                            if match:
                                                module = match.group(1)  # e.g., A0, B0
                                                temp = match.group(2)    # e.g., 41C, N/A
                                                temperatures[module] = temp

                                    # Use regex to find the temperature value after A0
                                    # pattern = r"Power\s+Module\s+Temperature.*?\nA0\s+(\S+)"
                                    # match = re.search(pattern, power_config, re.DOTALL)

                                    # if match:
                                    #     temperature = match.group(1)
                                    # Check if temperatures dictionary is not empty
                                    if temperatures and 'A0' in temperatures:
                                        if temperatures.get('A0') == 'N/A':
                                            error_msg = f"Power verification failed for hostname {serialized_data['nid_hostname'].upper()}. Temperature value is N/A"
                                            verification_output = {'status': False,  'detail': error_msg}
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(power_config, indent=4, separators=(',', ': ')))
                                        elif 'C' in temperatures.get('A0'):
                                            output_msg = f"Power verification successful for hostname {serialized_data['nid_hostname'].upper()}. Temperature value is: {temperatures['A0']}"
                                            verification_output = {'status': True,  'detail': output_msg}
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(power_config, indent=4, separators=(',', ': ')))
                                        else:
                                            error_msg = f"Power verification failed for hostname {serialized_data['nid_hostname'].upper()}. Temperature value not found in response"
                                            verification_output = {'status': False,  'detail': error_msg}
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(power_config, indent=4, separators=(',', ': ')))
                                    else:
                                        error_msg = f"Power verification failed for hostname {serialized_data['nid_hostname'].upper()}. Temperature value not found in response"
                                        verification_output = {'status': False,  'detail': error_msg}
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(power_config, indent=4, separators=(',', ': ')))

                                nid_output = { 'nid': { 'power' : verification_output } }

                            elif verification_steps[index] == "power_reading":
                                '''
                                Verify power reading
                                '''
                                verification_output = {}
                                stage = "Verify Power Reading (NID)"
                                fiber_reading_config = data.get('ne_response', '')
                                if re.search('error', fiber_reading_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Fiber Reading verification failed for hostname {serialized_data['nid_hostname'].upper()}"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    nid_output['nid']['power_reading'] = {'status': 'undefined'}
                                    nid_output['nid']['sfp_match'] = {'status': 'undefined'}

                                else:
                                    
                                    # Regex pattern to match the exact interface string
                                    interface_pattern = r"^" + re.escape(serialized_data['nid_interface']) + r"$"

                                    for line in fiber_reading_config.splitlines():
                                        # match regex xgei-1/1/0/1 but not match xgei-1/1/0/10
                                        if re.match(interface_pattern, line):
                                            # Extract the power reading value
                                            parts = line.split()
                                            sfp_type = parts[1]  # Assuming the first part is the SFP type
                                            rx_power = parts[3].split('/')[0]  # Assuming the second part is the RX power
                                            tx_power = parts[4].split('/')[0]  # Assuming the third part is the TX power
                                            break
                                        
                                    # Check if the power readings are within acceptable limits
                                    rx_power_check, tx_power_check = False, False
                                    thresholds = {
                                        "10km": {
                                            'RxPower': [-14, 0.5],
                                            'TxPower': [-7.9, 1.5],
                                        },
                                        "40km": {
                                            'RxPower': [-15, -1],
                                            'TxPower': [-4.4, 5],
                                        },
                                        "80km": {
                                            'RxPower': [-23.1, -7],
                                            'TxPower': [-0.3, 5],
                                        }
                                    }

                                    for dist in list(thresholds.keys()):
                                        if re.search(dist, sfp_type, re.IGNORECASE):
                                            serialized_data['nid_sfp_type'] = dist
                                            if thresholds[dist]['RxPower'][0] <= float(rx_power) <= thresholds[dist]['RxPower'][1]:
                                                rx_power_check = True
                                            if thresholds[dist]['TxPower'][0] <= float(tx_power) <= thresholds[dist]['TxPower'][1]:
                                                tx_power_check = True
                                            break
                                        
                                    if rx_power_check and tx_power_check:
                                        output_msg =  f"Fiber Reading verification successful for hostname {serialized_data['nid_hostname'].upper()}. RX Power: {rx_power}dBm and TX Power: {tx_power}dBm are within threshold limits"
                                        verification_output = { 'status': True, 'detail': output_msg }
                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    elif not rx_power_check and not tx_power_check:
                                        error_msg = f"Fiber Reading verification failed for hostname {serialized_data['nid_hostname'].upper()}. RX Power: {rx_power}dBm and TX Power: {tx_power}dBm are out of threshold limits"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    elif not rx_power_check:
                                        error_msg = f"Fiber Reading verification failed for hostname {serialized_data['nid_hostname'].upper()}. RX Power: {rx_power}dBm is out of threshold limits"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    # elif not tx_power_check:
                                    else:
                                        error_msg = f"Fiber Reading verification failed for hostname {serialized_data['nid_hostname'].upper()}. TX Power: {tx_power}dBm is out of threshold limits"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                                # Add to nid_output
                                nid_output['nid']['power_reading'] = verification_output

                                # Compare sfp between nid and agg for non dwdm
                                if not is_dwdm:
                                    if serialized_data['agg_sfp_type'] == serialized_data['nid_sfp_type']:
                                        nid_output['nid']['sfp_match'] = {'status': True, 'detail': f"SFP type match successful for {serialized_data['nid_hostname'].upper()} SFP type: {serialized_data['nid_sfp_type']} and {serialized_data['agg_hostname'].upper()} SFP type: {serialized_data['agg_sfp_type']}"}
                                    else:
                                        nid_output['nid']['sfp_match'] = {'status': False, 'detail': f"SFP type mismatch for {serialized_data['nid_hostname'].upper()} SFP type: {serialized_data['nid_sfp_type']} and {serialized_data['agg_hostname'].upper()} SFP type: {serialized_data['agg_sfp_type']}"}

                                    if not nid_output['nid']['sfp_match']['status']:
                                        error_msg = f"SFP type mismatch for  {serialized_data['nid_hostname'].upper()} and {serialized_data['agg_hostname'].upper()}. NID SFP type: {serialized_data['nid_sfp_type']} and AGG SFP type: {serialized_data['agg_sfp_type']}"
                                        verification_output = {'status': False,  'detail': error_msg}
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                    else:
                                        output_msg = f"SFP type match successful for {serialized_data['nid_hostname'].upper()} and {serialized_data['agg_hostname'].upper()}. NID SFP type: {serialized_data['nid_sfp_type']} and AGG SFP type: {serialized_data['agg_sfp_type']}"
                                        verification_output = {'status': True,  'detail': output_msg}
                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))
                                else:
                                    output_msg = f"SFP type match not applicable for DWDM for {serialized_data['nid_hostname'].upper()} and {serialized_data['agg_hostname'].upper()}"
                                    verification_output = {'status': True,  'detail': output_msg}
                                    log_text(filename=log_file, content=output_msg, ne_output=dumps(fiber_reading_config, indent=4, separators=(',', ': ')))

                            elif verification_steps[index] == "lldp_neighbor":
                                '''
                                Verify LLDP neighbor
                                '''
                                verification_output = {}
                                stage = "Verify LLDP Neighbor (NID)"
                                lldp_neighbor_config = data.get('ne_response', '')
                                if re.search('error', lldp_neighbor_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. LLDP Neighbor verification failed for hostname {serialized_data['nid_hostname'].upper()}. Error in LLDP neighbor config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined', 'detail': error_msg }
                                else:
                                    matched_hostname, matched_interface = False, False
                                    for line in lldp_neighbor_config.splitlines(): 
                                        # Check if both hostname and interface are matched
                                        if matched_hostname and matched_interface:
                                            break
                                        # Check if the line contains the NID interface and hostname
                                        if re.search(serialized_data['agg_hostname'], line, re.IGNORECASE) and 'System name' in line:
                                            matched_hostname = True
                                            continue
                                        if re.search(serialized_data['agg_port'], line, re.IGNORECASE) and 'Port ID' in line:
                                            matched_interface = True
                                            continue
                                        
                                    if matched_hostname and matched_interface:
                                        output_msg = f"LLDP Neighbor verification successful for hostname {serialized_data['nid_hostname'].upper()}. Neighbor interface {serialized_data['agg_port']} and hostname {serialized_data['agg_hostname']} found in LLDP neighbor output"
                                        verification_output = { 'status': True, 'detail': output_msg }
                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))

                                    else:
                                        error_msg = f"LLDP Neighbor verification failed for hostname {serialized_data['nid_hostname'].upper()}. Neighbor interface {serialized_data['agg_port']} and hostname {serialized_data['agg_hostname']} not found in LLDP neighbor output"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(lldp_neighbor_config, indent=4, separators=(',', ': ')))

                                # Add to nid_output
                                nid_output['nid']['lldp_neighbor'] = verification_output

                            elif verification_steps[index] == "interface_description":
                                '''
                                Verify Interface Description
                                '''
                                verification_output = {}
                                stage = "Verify Interface Description (NID)"
                                interface_description_config = data.get('ne_response', '')
                                if re.search('error', interface_description_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Interface description verification failed for hostname {serialized_data['nid_hostname'].upper()}"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined' , 'detail': error_msg }

                                else:
                                    get_interface_description = False
                                    for line in interface_description_config.splitlines():
                                        if 'description' in line.lower() and not 'show running-config-interface' in line.lower():
                                            port_description = line.split('description')[1].strip()
                                            get_interface_description = True

                                    if not get_interface_description:
                                        error_msg = f"Stage: {stage}. Interface description verification failed for hostname {serialized_data['nid_hostname'].upper()}. Interface description not found in interface description output"
                                        verification_output = { 'status': 'undefined', 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                    else:
                                        # Check nid interface, nid hostname, agg hostname and agg hostname and agg interface exist in the description
                                        if not (re.search(serialized_data['agg_hostname'], port_description, re.IGNORECASE) and re.search(serialized_data['agg_port'], port_description, re.IGNORECASE)):
                                            error_msg = f"Stage: {stage}. Interface description verification failed for hostname {serialized_data['nid_hostname'].upper()}. Interface description does not contain all the required information. Interface description: {port_description}"
                                            verification_output = { 'status': False, 'detail': error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                        else:
                                            total_km = 0
                                            fiber_reading_range = {
                                                "10km" : [0, 10],
                                                "40km" : [10.1, 40],
                                                "80km" : [40.1, 80]
                                            }
                                            km_values = re.findall(r'(\d+(?:\.\d+)?)KM', agg_interface_description, re.IGNORECASE)
                                            total_km = sum(float(km) for km in km_values)

                                            sfp_match = False
                                            if total_km > 0:
                                                range_value = fiber_reading_range[serialized_data['nid_sfp_type']]
                                                if range_value[0] <= total_km <= range_value[1]:
                                                    sfp_match = True
                                                    
                                                if sfp_match:
                                                    output_msg = f"Interface description verification successful for hostname {serialized_data['nid_hostname'].upper()}. Interface description matches with AGG interface {serialized_data['agg_port']} and AGG hostname {serialized_data['agg_hostname']} and fiber distance {total_km}km within the range with SFP type {serialized_data['nid_sfp_type']}"
                                                    verification_output = { 'status' : True, 'detail' : output_msg }
                                                    log_text(filename=log_file, content=output_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                                else:
                                                    error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. Fiber distance {total_km}km not within the range for SFP type {serialized_data['nid_sfp_type']}"
                                                    verification_output = { 'status' : False, 'detail' : error_msg }
                                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))
                                                    
                                            else:
                                                error_msg = f"Interface description verification failed for hostname {serialized_data['agg_hostname'].upper()}. No fiber distance found in interface description"
                                                verification_output = { 'status' : False, 'detail' : error_msg }
                                                log_text(filename=log_file, content=error_msg, ne_output=dumps(interface_description_config, indent=4, separators=(',', ': ')))

                                # Add to nid_output
                                nid_output['nid']['interface_description'] = verification_output
                            
                            elif verification_steps[index] == "crc_error":
                                '''
                                Verify CRC Error
                                '''
                                verification_output = {}
                                stage = "Verify CRC Error (NID)"
                                crc_error_config = data.get('ne_response', '')
                                if re.search('invalid', crc_error_config, re.IGNORECASE) and re.search('%error', crc_error_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. CRC Error verification failed for hostname {serialized_data['nid_hostname'].upper()}. Error in CRC Error config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined', 'detail': error_msg }
                                else:
                                    for line in crc_error_config.splitlines():
                                        if re.search('in_crc_error', line, re.IGNORECASE):
                                            in_crc_error = int(line.split('In_CRC_ERROR')[1].strip())
                                        if re.search('e_crc_error', line, re.IGNORECASE):
                                            e_crc_error = int(line.split('E_CRC_ERROR')[1].strip())

                                    # Check if both in_crc_error and e_crc_error are present
                                    if 'in_crc_error' in locals() and 'e_crc_error' in locals():
                                        if in_crc_error == 0 and e_crc_error == 0:
                                            output_msg = f"CRC Error verification successful for hostname {serialized_data['nid_hostname'].upper()}. No CRC Errors found"
                                            verification_output = { 'status': True, 'detail': output_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))
                                        else:
                                            error_msg = f"CRC Error verification failed for hostname {serialized_data['nid_hostname'].upper()}. CRC Error count are In_CRC_ERROR: {in_crc_error} and E_CRC_ERROR: {e_crc_error}"
                                            verification_output = { 'status': False, 'detail': error_msg }
                                            log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))

                                    else:
                                        error_msg = f"CRC Error verification failed for hostname {serialized_data['nid_hostname'].upper()}. In_CRC_ERROR or E_CRC_ERROR not found in the output"
                                        verification_output = { 'status': 'undefined', 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(crc_error_config, indent=4, separators=(',', ': ')))

                                nid_output['nid']['crc_error'] = verification_output

                            elif verification_steps[index] == "license_port_enable":
                                # Assign the config to a variable and continue
                                license_port_enable_config = data.get('ne_response', '')
                                continue
                            
                            elif verification_steps[index] == "license_port_state":
                                '''
                                Verify License Port State
                                '''
                                stage = "Verify License Status (NID)"
                                verification_output = {}
                                # Assign the config to a variable and continue
                                license_port_state_config = data.get('ne_response', '')

                                if (re.search('invalid', license_port_enable_config, re.IGNORECASE) and re.search('%error', license_port_enable_config, re.IGNORECASE)) or (re.search('invalid', license_port_state_config, re.IGNORECASE) and re.search('%error', license_port_state_config, re.IGNORECASE)):
                                    error_msg = f"Stage: {stage}. License Port State verification failed for hostname {serialized_data['nid_hostname'].upper()}. Error in License Port Enable or License Port State config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(license_port_state_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined', 'detail': error_msg }
                                else:
                                    # Verify if the port is enabled
                                    # Declare variables
                                    in_table, result = False, {}

                                    for line in license_port_enable_config.splitlines():
                                        # Detect the start of the table
                                        if 'ItemName' in line:
                                            in_table = True
                                            continue
                                        # Process lines in the table
                                        if in_table:
                                            # Match lines with license data
                                            # Example: "PEL-6100-XG-10G        4      0          4        0          0               N/A"
                                            match = re.match(r'^\S+\s+(\d+)\s+(\d+)\s+(\d+)\s+\d+\s+\d+\s+\S+', line)
                                            if match:
                                                result['total'] = int(match.group(1))  # e.g., 4
                                                result['available'] = int(match.group(2))  # e.g., 0
                                                result['matched'] = int(match.group(3))  # e.g., 4
                                                break  # Assuming there's only one relevant row; remove if multiple rows needed

                                    # Check if the port is enabled
                                    port_enable = False
                                    if result['matched'] == 4:
                                        port_enable = True
                                    
                                    # Verify  the port state
                                    count = 0
                                    # Pattern to match lines containing 'xgei-', 'matched', and 'activated' (case-insensitive)
                                    pattern = re.compile(r'xgei-.*matched.*activated', re.IGNORECASE)

                                    for line in license_port_state_config.splitlines():
                                        # Check if line matches all criteria
                                        if pattern.search(line):
                                            count += 1

                                    # Check if 4 port states are activated
                                    port_state_check = False
                                    if count == 4:
                                        port_state_check = True

                                    if port_enable and port_state_check:
                                        output_msg = f"License Port State verification successful for hostname {serialized_data['nid_hostname'].upper()}. The license is activated and all 4 ports are activated"
                                        verification_output = { 'status': True, 'detail': output_msg }
                                        log_text(filename=log_file, content=output_msg, ne_output=dumps(f"{license_port_enable_config}\n\n{license_port_state_config}", indent=4, separators=(',', ': ')))

                                    elif not port_enable and port_state_check:
                                        error_msg = f"License Port State verification failed for hostname {serialized_data['nid_hostname'].upper()}. The license is not activated but all 4 ports are activated"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(license_port_state_config, indent=4, separators=(',', ': ')))

                                    elif port_enable and not port_state_check:  
                                        error_msg = f"License Port State verification failed for hostname {serialized_data['nid_hostname'].upper()}. The license is activated but not all 4 ports are activated"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(license_port_state_config, indent=4, separators=(',', ': ')))

                                    # elif not port_enable and not port_state_check:
                                    else:
                                        error_msg = f"License Port State verification failed for hostname {serialized_data['nid_hostname'].upper()}. The license is not activated and not all 4 ports are activated"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(license_port_state_config, indent=4, separators=(',', ': ')))

                                # Add to nid_output
                                nid_output['nid']['license_status'] = verification_output
                                        
                            elif verification_steps[index] == "version":
                                '''
                                Verify Version
                                '''
                                stage = "Verify Version (NID)"
                                verification_output = {}
                                # Assign the config to a variable and continue
                                version_config = data.get('ne_response', '')
                                if re.search('invalid', version_config, re.IGNORECASE) and re.search('%error', version_config, re.IGNORECASE):
                                    error_msg = f"Stage: {stage}. Version verification failed for hostname {serialized_data['nid_hostname'].upper()}. Error in Version config"
                                    log_text(filename=log_file, content=error_msg, ne_output=dumps(version_config, indent=4, separators=(',', ': ')))
                                    verification_output = { 'status': 'undefined' }
                                else:
                                    pattern = r"<sysdisk0: verset/([^>]+)\.set>"
                                    match = re.search(pattern, version_config)
                                    if match:
                                        software_version = match.group(1)
                                    else:
                                        software_version = None
                                    if software_version:
                                        if '20B220' in software_version:
                                            output_msg = f"Version verification successful for hostname {serialized_data['nid_hostname'].upper()}. The software version is {software_version}"
                                            verification_output = { 'status': True, 'detail': output_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(version_config, indent=4, separators=(',', ': ')))
                                        else:
                                            error_msg = f"Version verification failed for hostname {serialized_data['nid_hostname'].upper()}. The software version is {software_version}, which is not the expected version"
                                            verification_output = { 'status': False,  'detail': error_msg }
                                            log_text(filename=log_file, content=output_msg, ne_output=dumps(version_config, indent=4, separators=(',', ': ')))
                                    else:
                                        error_msg = f"Version verification failed for hostname {serialized_data['nid_hostname'].upper()}. Unable to extract software version from the response"
                                        verification_output = { 'status': False, 'detail': error_msg }
                                        log_text(filename=log_file, content=error_msg, ne_output=dumps(version_config, indent=4, separators=(',', ': ')))

                                # Add to nid_output
                                nid_output['nid']['version'] = verification_output

                        # Collect output and add to final output
                        final_output.append(nid_output)

            else:
                error_msg = f"Stage: {stage}. Failed to retrieve NID outputs for hostname {serialized_data['nid_hostname'].upper()}"
                log_text(filename=log_file, content=error_msg, ne_output=dumps(f"{str(output)}\n\n{payload}", indent=4, separators=(',', ': ')))

            # Finalize the output
            # Check if 'nid' exist
            has_nid = any('nid' in item for item in final_output)
            if not has_nid:
                if 'undefined' in str(final_output) or 'False' in str(final_output):
                    output_msg = f"Successfully run verifications on AGG but some of the verification items contains errors. NID is inaccessible or not available"
                    log_text(filename=log_file, content=output_msg, ne_output=dumps(final_output, indent=4, separators=(',', ': ')))
                    log_database(
                        data_DB={
                            'order_type': 'Verification',
                            'service_type': '5G Umobile',
                            'input_details': serialized_data,
                            'message': output_msg,
                            'api_output': str(serialized_data),
                            'status': 'failed',
                            'log_file': log_file,
                            'user': user
                        }
                    )
                    return Response({
                        'status': 'failed',
                        'message': output_msg,
                        'data': final_output,
                        'log_file': log_file
                    }, status=status.HTTP_200_OK)
                
                else:
                    output_msg = f"Successfully run verifications on AGG and all verification passed but NID is inaccessible or not available"
                    log_text(filename=log_file, content=output_msg, ne_output=dumps(final_output, indent=4, separators=(',', ': ')))
                    # Log to database
                    log_database(
                        data_DB={
                            'order_type': 'Verification',
                            'service_type': '5G Umobile',
                            'input_details': serialized_data,
                            'message': output_msg,
                            'api_output': str(serialized_data),
                            'status': 'failed',
                            'log_file': log_file,
                            'user': user
                        }
                    )
                    # Return success response
                    return Response({
                        'status': 'success',
                        'message': output_msg,
                        'data': final_output,
                        'log_file': log_file
                    }, status=status.HTTP_200_OK)
                
            if 'undefined' in str(final_output) or 'False' in str(final_output):
                output_msg = f"Successfully run verifications on AGG and NID but some of the verification items contains errors"
                log_text(filename=log_file, content=output_msg, ne_output=dumps(final_output, indent=4, separators=(',', ': ')))
                log_database(
                    data_DB={
                        'order_type': 'Verification',
                        'service_type': '5G Umobile',
                        'input_details': serialized_data,
                        'message': output_msg,
                        'api_output': str(serialized_data),
                        'status': 'failed',
                        'log_file': log_file,
                        'user': user
                    }
                )
                return Response({
                    'status': 'failed',
                    'message': output_msg,
                    'data': final_output,
                    'log_file': log_file
                }, status=status.HTTP_200_OK)
            else:
                output_msg = f"Successfully run verifications on AGG and NID and all verifications passed"
                log_text(filename=log_file, content=output_msg, ne_output=dumps(final_output, indent=4, separators=(',', ': ')))
                # Log to database
                log_database(
                    data_DB={
                        'order_type': 'Verification',
                        'service_type': '5G Umobile',
                        'input_details': serialized_data,
                        'message': output_msg,
                        'api_output': str(serialized_data),
                        'status': 'success',
                        'log_file': log_file,
                        'user': user
                    }
                )
                # Return success response
                return Response({
                    'status': 'success',
                    'message': output_msg,
                    'data': final_output,
                    'log_file': log_file
                }, status=status.HTTP_200_OK)
        
        except Exception as error:
            # Get the error details
            exc_type, exc_obj, exc_tb = sys.exc_info()
            error_details = {
                'error_type': str(exc_type),
                'error_details': str(exc_obj),
                'error_line': str(exc_tb.tb_lineno),
            }

            # Generate error message
            error_msg = f"Stage: {stage}. Unexpected error occurred while processing the request"
            output_msg = f"{error_msg}\n [Error details: {error}]"

            # Assign variable database logging
            DB_data = {
                'order_type': 'Verification',
                'service_type': '5G Umobile',
                'message': output_msg,
                'api_output': str(error),
                'status': 'failed',
                'user': user if 'user' in locals() else 'not_available'
            }

            if stage == 'Start Process':
                # For start process stage, we may not have request.data available
                DB_data['input_details'] = 'N/A'  # Set to None explicitly
                DB_data['log_file'] = 'N/A'
            elif stage == 'Verify Data Format':
                # For 'Verify Data Format' stage, we have request.data available
                DB_data['input_details'] = request.data
                DB_data['log_file'] = 'N/A'
            # Log the error for other than 'Start Verify' and 'Verify Data Format' stages    
            else:
                log_text(
                    filename=log_file,
                    content=output_msg,
                    ne_output=dumps(error_details, indent=4, separators=(',', ': '))
                )
                DB_data['input_details'] = request.data
                DB_data['log_file'] = log_file

            # Log to database
            log_database(data_DB=DB_data)

            return Response({
                'status': 'failed',
                'message': output_msg,
                'data': error_details,
                'log_file': log_file if 'log_file' in locals() else None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                