apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pisa-workflow-nginx
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "10800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "10800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "10800"
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "10800"
spec:
  tls:
  - hosts:
    - pisa-regression.tm.com.my
  rules:
  - host: pisa-regression.tm.com.my
    http:
      paths:
      - path: /workflow
        pathType: Prefix
        backend:
          service:
            name: pisa-workflow-svc
            port: 
              number: 80
