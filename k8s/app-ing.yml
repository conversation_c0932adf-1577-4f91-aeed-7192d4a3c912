apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pisa-workflow-nginx
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "10800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "10800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "10800"
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "10800"
spec:
  tls:
  - hosts:
    - pisa.tm.com.my
  rules:
  - host: pisa.tm.com.my
    http:
      paths:
      - path: /workflow
        pathType: Prefix 
        backend:
          service:
            name: pisa-workflow-svc
            port: 
              number: 80

# apiVersion: extensions/v1beta1
# kind: Ingress
# metadata:
#   name: pisa-workflow-nginx
#   annotations:
#     kubernetes.io/ingress.class: "nginx"
#     nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
#     nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
#     nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
#     nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "1800"
# spec:
#   tls:
#   - hosts:
#     - pisa-workflow.devin.kubix.tm
#     secretName: pisa-workflow-tls
#   rules:
#   - host: pisa-workflow.devin.kubix.tm
#     http:
#       paths:
#       - backend:
#           serviceName: pisa-workflow-svc
#           servicePort: 80
