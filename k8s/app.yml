---
apiVersion: v1
kind: Service
metadata:
  name: pisa-workflow-svc
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
  selector:
    app: pisa-workflow
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pisa-workflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pisa-workflow
  template:
    metadata:
      labels:
        app: pisa-workflow
    spec:
      containers:
      - name: pisa-workflow
        image: harbor.dev.kubix.tm.com.my/ipgen-pisa/pisa-workflow:<version>
        # command: ["/bin/sh", "-c"]
        # args: ["python manage.py makemigrations; python manage.py migrate; python manage.py runserver 0.0.0.0:8000"]
        resources:
           limits:
              memory: "600Mi"
              cpu: "300m"
        ports:
        - containerPort: 8000
        volumeMounts:
        - mountPath: /etc/localtime
          name: tz-config
        - mountPath: /root/home/<USER>
          name: nfs-vol
          subPath: configuration_files
        - mountPath: /root/home/<USER>
          name: nfs-vol
          subPath: log_files
        - mountPath: /root/home/<USER>
          name: nfs-vol
          subPath: ne_log_files
      volumes:
      - name: tz-config
        hostPath:
          path: /usr/share/zoneinfo/Asia/Kuala_Lumpur
      - name: nfs-vol
        nfs:
          server: ************
          path: /BF7_NFS_DEV_IPGEN_PISA