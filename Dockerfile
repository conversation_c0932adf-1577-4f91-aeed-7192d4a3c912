FROM python:3.7.17-bullseye

#ENV PYTHON UNBUFFERED 1

RUN mkdir /app
WORKDIR /app

# RUN apt update
# RUN apt-get install telnet -y
# RUN apt install nano -y

# # Install dependency
# RUN apt-get update

# RUN apt-get install -y libaio1 iputils-ping && \
#     rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y \
    libldap2-dev \
    libsasl2-dev \
    libssl-dev \
    telnet \
    nano \
    libaio1 \
    iputils-ping \
 && rm -rf /var/lib/apt/lists/*

ADD instantclient-basic-linux.x64-********.0.zip /tmp/

RUN unzip /tmp/instantclient-basic-linux.x64-********.0.zip -d /opt/oracle && \
    rm /tmp/instantclient-basic-linux.x64-********.0.zip && \
    # rm /opt/oracle/instantclient_11_2/libclntsh.so && \
    # rm /opt/oracle/instantclient_11_2/libocci.so && \
    ln -s /opt/oracle/instantclient_11_2/libclntsh.so.11.1 /opt/oracle/instantclient_11_2/libclntsh.so && \
    ln -s /opt/oracle/instantclient_11_2/libocci.so.11.1 /opt/oracle/instantclient_11_2/libocci.so

ENV ORACLE_HOME /opt/oracle/instantclient_11_2
ENV LD_LIBRARY_PATH $ORACLE_HOME:$LD_LIBRARY_PATH
RUN echo "$ORACLE_HOME/lib" > /etc/ld.so.conf.d/oracle-instantclient.conf \
    && ldconfig \
    && export PATH=$PATH:$ORACLE_HOME/bin

# update pip
RUN python3 -m pip install --upgrade pip

# Copying & Installing python requirements
COPY /src/requirements.txt /app
RUN python3 -m pip install -r requirements.txt

# Create a script to modify /etc/hosts at runtime
RUN echo '#!/bin/sh' > /usr/local/bin/add-hosts.sh && \
    echo 'echo "************* grndbprod" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "************* grndbprod" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "************* grndbprod" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "************ nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "************ nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    echo 'echo "*********** nprcmsodb-scn" >> /etc/hosts' >> /usr/local/bin/add-hosts.sh && \
    chmod +x /usr/local/bin/add-hosts.sh


# Syncing the source of the application
COPY /src /app/
WORKDIR /app

EXPOSE 8000

#RUN python manage.py migrate
# CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
CMD /usr/local/bin/add-hosts.sh && python manage.py runserver 0.0.0.0:8000