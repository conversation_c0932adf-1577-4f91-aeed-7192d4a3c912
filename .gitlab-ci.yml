stages:
  # - test
  - build
  - deploy

variables:
  CONTAINER_IMAGE_DEV: harbor.dev.kubix.tm.com.my/ipgen-pisa/pisa-workflow:${CI_COMMIT_SHORT_SHA}
  CONTAINER_IMAGE_PROD: harbor.prod.kubix.tm.com.my/ipgenpisa/pisa-workflow:${CI_COMMIT_SHORT_SHA}
  DEDICATED_DEV_URL: pisa-dev.tm.com.my
  DEDICATED_REGRESSION_URL: pisa-regression.tm.com.my
  DEDICATED_PROD_URL: pisa.tm.com.my
  NFS_DEV: ************
  NFS_PROD: ************
  GIT_STRATEGY: clone

build-dev-dedicated:
  stage: build
  script:
    - docker login harbor.dev.kubix.tm.com.my -u 'robot$ipgen-pisa+pisa' -p ${HARBOR_PASSWORD_DEV}
    - docker build -t ${CONTAINER_IMAGE_DEV} .
    - docker push ${CONTAINER_IMAGE_DEV}
  tags:
    - kubix
  only:
    - dev_dedicated
  # when: manual

deploy-dev-dedicated:
  stage: deploy
  script:
    - kubectl config set-cluster k8s --server="${SERVER_DEV_DEDICATED}"
    - kubectl config set clusters.k8s.certificate-authority-data ${CA_DEV_DEDICATED}
    - kubectl config set-credentials gitlab --token="${USER_TOKEN_DEV_DEDICATED}"
    - kubectl config set-context --current --namespace="${NAMESPACE_DEDICATED_DEV}"
    - sed -i "s/<version>/${CI_COMMIT_SHORT_SHA}/g" k8s/app.yml
    - kubectl apply -f k8s/app.yml
    - kubectl apply -f k8s/app-ing.yml
    - kubectl apply -f k8s/hpa.yml

  environment:
    name: development
    # url: https://pisa-workflow.devin.kubix.tm/swagger/
  tags:
    - kubix
  only:
    - dev_dedicated
  # when: manual

build-regression-dedicated:
  stage: build
  script:
    - sed -i "s/${DEDICATED_DEV_URL}/${DEDICATED_REGRESSION_URL}/g" src/workflow_api/urls.py
    - docker login harbor.dev.kubix.tm.com.my -u 'robot$ipgen-pisa+pisa' -p ${HARBOR_PASSWORD_DEV}
    - docker build -t ${CONTAINER_IMAGE_DEV} .
    - docker push ${CONTAINER_IMAGE_DEV}
  tags:
    - kubix
  only:
    - regression_dedicated
  # when: manual

deploy-regression-dedicated:
  stage: deploy
  script:
    - kubectl config set-cluster k8s --server="${SERVER_DEV_DEDICATED}"
    - kubectl config set clusters.k8s.certificate-authority-data ${CA_DEV_DEDICATED}
    - kubectl config set-credentials gitlab --token="${USER_TOKEN_DEV_DEDICATED}"
    - kubectl config set-context --current --namespace="${NAMESPACE_DEDICATED_REGRESSION}"
    - sed -i "s/${DEDICATED_DEV_URL}/${DEDICATED_REGRESSION_URL}/g" k8s/app-ing.yml
    - sed -i "s/<version>/${CI_COMMIT_SHORT_SHA}/g" k8s/app.yml
    - kubectl apply -f k8s/app.yml
    - kubectl apply -f k8s/app-ing.yml
    - kubectl apply -f k8s/hpa.yml

  environment:
    name: regression
    # url: https://pisa-workflow.devin.kubix.tm/swagger/
  tags:
    - kubix
  only:
    - regression_dedicated
  # when: manual

build-prod-dedicated:
  stage: build
  script:
    - sed -i "s/${DEDICATED_DEV_URL}/${DEDICATED_PROD_URL}/g" src/workflow_api/urls.py
    - docker login harbor.prod.kubix.tm.com.my -u 'robot$ipgenpisa+gitlab-tm' -p ${HARBOR_PASSWORD_PRD}
    - docker build -t ${CONTAINER_IMAGE_PROD} .
    - docker push ${CONTAINER_IMAGE_PROD}
  tags:
    - kubix
  only:
    - prod_dedicated
  # when: manual

deploy-prod-dedicated:
  stage: deploy
  script:
    - kubectl config set-cluster k8s --server="${SERVER_PRD_DEDICATED}"
    - kubectl config set clusters.k8s.certificate-authority-data ${CA_PRD_DEDICATED}
    - kubectl config set-credentials gitlab --token="${USER_TOKEN_PRD_DEDICATED}"
    - kubectl config set-context default --cluster=k8s --user=gitlab
    - kubectl config use-context default
    - kubectl config set-context --current --namespace="${NAMESPACE_DEDICATED_PRD}"
    - sed -i "s/${DEDICATED_DEV_URL}/${DEDICATED_PROD_URL}/g" k8s/app-ing.yml
    - sed -i "s/<version>/${CI_COMMIT_SHORT_SHA}/g" k8s/app.yml
    - sed -i "s/${NFS_DEV}/${NFS_PROD}/g" k8s/app.yml
    - sed -i "s/dev/prod/g" k8s/app.yml
    - sed -i "s/DEV/PRD/g" k8s/app.yml
    - sed -i "s/ipgen-pisa/ipgenpisa/g" k8s/app.yml
    - cat k8s/app.yml
    - kubectl apply -f k8s/app.yml
    - kubectl apply -f k8s/app-ing.yml
    - kubectl apply -f k8s/hpa.yml

  environment:
    name: production
    # url: https://pisa-workflow.prdin.kubix.tm/swagger/
  tags:
    - kubix
  only:
    - prod_dedicated
  # when: manual
